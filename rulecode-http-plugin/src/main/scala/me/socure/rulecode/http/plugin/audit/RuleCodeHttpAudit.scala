package me.socure.rulecode.http.plugin.audit

import me.socure.common.clock.Clock
import me.socure.common.transaction.id.TrxId
import me.socure.service.audit.client.TPAuditClient
import me.socure.service.audit.model.AuditInfo
import me.socure.thirdparty.audit.common.ThirdPartyServiceIds
import me.socure.thirdparty.audit.common.ThirdPartyServiceIds.ThirdPartyServiceId
import me.socure.thirdparty.auditing.client.ThirdPartyAuditor
import org.json4s.{DefaultFormats, Formats}

import java.util.{Date, UUID}

object RuleCodeHttpAudit {

  private implicit def jsonFormats: Formats = DefaultFormats

  private val thirdPartyServiceIdMap : Map[String, ThirdPartyServiceId] = Map(
    "payfone_trust_v2" -> ThirdPartyServiceIds.PayfoneTrustV2,
    "payfone_verify_v2" -> ThirdPartyServiceIds.PayfoneVerify,
    "neustar" -> ThirdPartyServiceIds.NeustarIdentityVerificationHttpLookup,
    "white_pages"-> ThirdPartyServiceIds.WhitePagesInternational,
    "enstream" -> ThirdPartyServiceIds.Ensvl,
    "rcHelper" -> ThirdPartyServiceIds.RCHelperServiceCall,
    "twilio" -> ThirdPartyServiceIds.TwilioSimSwapHttpLookup,
    "monnai" -> ThirdPartyServiceIds.KycVerifyVendorServiceV2,
    "idmerit" -> ThirdPartyServiceIds.KycVerifyVendorServiceV2
  )

  @deprecated
  def audit(tpAuditClient: TPAuditClient,
            vendorName: String,
            requestURL: String,
            requestBody: String,
            responseBody: String,
            startTime: Date,
            clock: Clock,
            isError: Boolean,
            accountId: Long,
            maskPii: Boolean
           )(implicit trxId: TrxId): Unit = {
    val auditInfo = AuditInfo(
      accountId = accountId,
      transactionId = trxId.value,
      serviceName = thirdPartyServiceIdMap.getOrElse(vendorName, ThirdPartyServiceIds.Unknown).toString(),
      startTime = startTime,
      processingTime = clock.now().getMillis - startTime.getTime,
      isCache = false,
      isError = isError,
      request = requestURL,
      requestBody = requestBody,
      response = responseBody,
      uuid = UUID.randomUUID.toString)
    tpAuditClient.audit(auditInfo, obfuscatePII = maskPii)(TrxId(auditInfo.transactionId))
  }

  def audit(thirdPartyAuditor: ThirdPartyAuditor[AuditInfo],
            vendorName: String,
            requestURL: String,
            requestBody: String,
            responseBody: String,
            startTime: Date,
            clock: Clock,
            isError: Boolean,
            accountId: Long,
            maskPii: Boolean
           )(implicit trxId: TrxId): Unit = {
    val auditInfo = AuditInfo(
      accountId = accountId,
      transactionId = trxId.value,
      serviceName = thirdPartyServiceIdMap.getOrElse(vendorName, ThirdPartyServiceIds.Unknown).toString(),
      startTime = startTime,
      processingTime = clock.now().getMillis - startTime.getTime,
      isCache = false,
      isError = isError,
      request = requestURL,
      requestBody = requestBody,
      response = responseBody,
      uuid = UUID.randomUUID.toString)
    thirdPartyAuditor.audit(auditInfo, obfuscatePII = maskPii)
  }

}