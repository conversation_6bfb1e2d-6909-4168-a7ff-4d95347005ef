withDBMigration=false

scheduler {
  time.interval = 120
  s3.bucket = "rulecode-config-764009278656-us-east-1"
  s3.object = "rulecode-config.txt"
  s3.fetch.wait.time = 30
}
threadpool {
  poolSize=100
}

server {
  port=5000
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 80%
    non_heap.used_max_percentage = "<80.0" //should be less than 80%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

hmac {
  ttl=5
  time.interval=5
  strength=512
  aws.secrets.manager.id="rulecode-service/dev/hmac-49397d"
  secret.refresh.interval=5000
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]

jmx {
  port = 1098
}

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region=us-east-1
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-dev-764009278656-us-east-1"
      }
      third-party {
        region=us-east-1
        bucket="thirdparty-stats-dev-764009278656-us-east-1"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

s3 {
  bucket = "rulecode-config-764009278656-us-east-1"
  paths {
    table = "table_definitions"
    rule = "rulecode_definitions"
    rulev2 = "rulecode_definitions_v2"
  }
  audit {
    bucket = "rulecode-audit-764009278656-us-east-1"
  }
}

refresh {
  rulecode {
    time {
      initial = 600
      interval = 600
    }
  }
  emailIntelligence {
    cron = "0 0 14 ? * *"
  }
}

toggle {
  rulecodeStudio {
    s3config: false
    configApi: false
    configReload: false
  }
  dynamodb {
      configReload: true
  }
}

memcache.servers = "localhost:11211"

http.plugin {
    threadPool {
        poolSize = 50
    }
}

external.vendors.config {
  payfone {
    username = """ENC(msSTgjKPeGDYWCK4NWt/E290Z3qIY+QjQZ9ow6eNXtChaf0LH7sxZ1emRCmGM3oKdSGpE3gdBSiFmCIH67DZyg==)"""
    password = """ENC(u+SXM+dZ1lA3G0ecr0hMtS1ZeFlbYdLaaIx17Ghs8jW7zvOUaajqcuK/9vqT+kB+1PNu/xDRzCuSEYrRxMhkIA==)"""
    clientId = """ENC(FQxnrmhvAbheoZk7tFUb4CXXM7tYMaFY20nzl29c5is=)"""
  }

  #Alpha env creds
  #neustar {
  #  endpoint = "http://a2687d2876af04d00951bbfd43310d6d-1637126139.us-west-2.elb.amazonaws.com:8090/api/identityrisk_fraudprevention-v1",
  #  auth.key = """ENC(EX8YmbCXkgkqaK006StqW8k6I3qALa30KZLiLQDcCpddL+j1k3WEJVOy6wma8GzV/3B1y8K/vMDpwyYZG1Ong4ODCq+/+dA55/5cSIVSNSo=)"""
  #}

  #In-house env creds
  #neustar {
  #  endpoint = "http://k8s-gateway-nifewebg-92c98d6e8c-9f906e33f764182f.elb.us-east-1.amazonaws.com:8090/api/identityRisk_fraudPrevention-v1",
  #  auth.key = """ENC(EX8YmbCXkgkqaK006StqW8k6I3qALa30KZLiLQDcCpddL+j1k3WEJVOy6wma8GzV/3B1y8K/vMDpwyYZG1Ong4ODCq+/+dA55/5cSIVSNSo=)"""
  #}

  #Internal MOCK endpoint
  neustar {
    endpoint = "http://10.10.27.165:8080/api/identityRisk_fraudPrevention-v1?api_key=rafDo2faK5xhV0TmARtOjTTks0flzLXu",
    endpoint2 = "http://10.10.27.165:8080/api/identityRisk_fraudPrevention-v1?api_key=rafDo2faK5xhV0TmARtOjTTks0flzLXu",
    auth.key = """ENC(EX8YmbCXkgkqaK006StqW8k6I3qALa30KZLiLQDcCpddL+j1k3WEJVOy6wma8GzV/3B1y8K/vMDpwyYZG1Ong4ODCq+/+dA55/5cSIVSNSo=)"""
    auth.key2 = """ENC(EX8YmbCXkgkqaK006StqW8k6I3qALa30KZLiLQDcCpddL+j1k3WEJVOy6wma8GzV/3B1y8K/vMDpwyYZG1Ong4ODCq+/+dA55/5cSIVSNSo=)"""
  }

  #External API call creds
  #neustar {
  #  endpoint = "https://webgwy.neustar.biz/api/identityRisk_fraudPrevention-v1",
  #  auth.key = """ENC(3wBhFVTSk3/VZ8zWIEe4HpRlnKqlmEdse5rvS2fxdePKI9nToQAEnR4LbB4IGwABGFQljtBzU5IjIEWpPnGhrJtnbeM16URjZpKUD/q+kbA=)"""
  #}

  whitepages {
        endpoint = "https://api.ekata.com/3.0/identity_check.json",
        # Demo Credentials
        auth.key = """ENC(QAo8/AUAaCyySMRoTTM8MRCP4u87c3IshOQ2inwnlwTaweFePQjJddqGXxasmipjUFoPVu/evtzs4EoeuISPZQ==)"""
        # Prod Credentials
        #auth.key = """ENC(obV1qNhCjt2cljxxixbvOLemmR91fBXtnstaWRZswf3yU4EeE/Yu2Td6l7lZOCMbEXdDOJX89djvPkyYbLif3A==)"""
  }
  whitepages_alternate {
       endpoint = "https://api.ekata.com/3.0/identity_check.json",
       # Demo Credentials
       auth.key = """ENC(fyM8BYJNqcbHINFCeN7RyUYsbYodBnZN2IBPDmN6IHzdxKNJXPfesULdF5X0TtjV9y75GqnJ5aTEGgG3V5/0IA==)"""
       # Prod Credentials
       #auth.key = """ENC(obV1qNhCjt2cljxxixbvOLemmR91fBXtnstaWRZswf3yU4EeE/Yu2Td6l7lZOCMbEXdDOJX89djvPkyYbLif3A==)"""
  }
  enstream {
    endpoint = "https://token-qa.enstreamidentity.com/v1/service"
    auth.key = """ENC(59nhCjDoX7HSBVelBlglUQtGIWtnPKt63voou+smTrqz4Y5yVaq3HFaMP/NjKLEw2Ssvcz5aRqwVpwJ7ye0qvA==)"""
    token.valid.time.in.minutes = 5
    kid = """ENC(lM0CN9KqtTEPs7PgL7TPg4Lw58gw6CxmKevGOfvIApE=)"""
    operation = "CS-V1"
    sign.algorithm = "RSASSA_PKCS1_V1_5_SHA_256"
    encrypt.decrypt.public.key.secret = "kyc-vendor-service/dev/enstream_enc_key_secret"
    sign.verify.public.key.secret = "kyc-vendor-service/dev/enstream_sign_key_secret"
    service.provider.id = "SO001"
  }
    kyc-vendor-service {
        endpoint = "http://mock-service/vendor/data"
    }
}
kms{
  region = "us-east-1"
  sign.key.id = "c846eea0-f412-4a57-bacb-2c26b309cba5"
  decrypt.key.id = "00ccd785-90f4-4932-8bad-5852570dab65"
}

rcHelper {
  endpoint = "http://rc-helper-service"
  timeout = 100 # in ms
  allowedVendors = []
}


context {
  cache {
    size = 10000
    threads = 250
  }
}

#===================Dynamic Control Center==========================#

dynamic.control.center{
  s3 {
    bucketName="globalconfig-764009278656-us-east-1"
  }
  memcached {
    host=localhost
    port=11211
    ttl=86400
  }
  local.cache {
    timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

###====== Dyanomo Table Configuration Begins========###

dynamodb {
  dataFetchEnabled = true
  env = "dev"
  region="us-east-1"
  configTable = "rc_config_dev"
  currentConfigKey = "rc_table_config_stage_ta"
  ingestionKey = "rc_table_config_ingestion_success"
  rollbackKey = "rc_table_config_rollback"

  allowDynamoToggle=false

        unreleasedTables = [
        ]

        compressed_data_tables = [
                "fullcontact_email_lookup",
                "equifax_email_lookup",
                "neustar_ip_reputation_lookup",
                "efx_chm_ssn",
                "efx_lfm_ssn",
                "efx_chm_fullname_dob",
                "towerdata_email_lookup",
                "phone_correlation",
                "ssn_correlation",
                "address_correlation",
                "asl_vendor_lookup",
                "attom_vendor_lookup",
                "neustar_ipv4_geolocation_lookup",
                "neustar_ipv6_geolocation_lookup",
                "scpvl_lookup"
                ]

        localConf {
          isLocal = false
          region = "us-east-1"
          access.key="x" #ignored if isLocal is false
          secret.key="x" #ignored if isLocal is false
        }

        simpleKeyTbl{
        partitionFieldName = "pk"
        dataFieldName = "data"
        numberOfKeysForBatch = "50"
        }

        compositeKeyTbl{
        partitionFieldName = "pk"
        sortFieldName = "sk"
        dataFieldName = "data"
        }
}
###====== Dyanomo Table Configuration Ends========###

server.metrics.enabled = false

#add full name with file type like "ssn_correlation.json"
read_table_definitions_from_mscv = []

address-service {
   endpoint = "http://address-service",
   alternateEndpoint = "http://address-service"
}