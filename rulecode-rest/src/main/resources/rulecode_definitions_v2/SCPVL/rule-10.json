{"version": "v2", "rules": [{"rule_code_name": "SCPVL.111001", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.first_name_optional_"], "options": [], "output": "prefill_first_name"}]}, {"rule_code_name": "SCPVL.111002", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.last_name_optional_"], "options": [], "output": "prefill_last_name"}]}, {"rule_code_name": "SCPVL.111003", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.dob_optional_"], "options": [], "output": "prefill_dob"}]}, {"rule_code_name": "SCPVL.111004", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.matched_email.value_optional_"], "options": [], "output": "prefill_email"}]}, {"rule_code_name": "SCPVL.111005", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.ssn_optional_"], "options": [], "output": "prefill_ssn"}]}, {"rule_code_name": "SCPVL.111006", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.matched_addr.street_optional_"], "options": [], "output": "prefill_street"}]}, {"rule_code_name": "SCPVL.111007", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.matched_addr.city_optional_"], "options": [], "output": "prefill_city"}]}, {"rule_code_name": "SCPVL.111008", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.matched_addr.state_optional_"], "options": [], "output": "prefill_state"}]}, {"rule_code_name": "SCPVL.111009", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.matched_addr.zip_optional_"], "options": [], "output": "prefill_zip"}]}, {"rule_code_name": "SCPVL.111010", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.associated_addrs_optional_"], "options": [{"name": "parseJsonToStr", "value": "true"}], "output": "prefill_associated_addrs"}]}, {"rule_code_name": "SCPVL.111011", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.associated_emails_optional_"], "options": [{"name": "parseJsonToStr", "value": "true"}], "output": "prefill_associated_emails"}]}, {"rule_code_name": "SCPVL.111012", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["table.scpvl_lookup.best_match_scpvl_prefill.associated_phones_optional_"], "options": [{"name": "parseJsonToStr", "value": "true"}], "output": "prefill_associated_phones"}]}]}