{"version": "v2", "vendor_variables": [{"variable_name": "idMeritData", "processors": [{"methods": ["time_adjusted_date_as_date_str"], "inputs": ["input.raw.submissionDate_optional_", "input.raw.transactionDate_optional_"], "output": "time_adjusted_date"}]}], "rules": [{"rule_code_name": "IMIVL.100000", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "inputs": ["http.idmerit.responses.response.result_optional_", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.100001", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.valid_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["lowercase"], "inputs": ["result"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.100002", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.type1_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100003", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.disposable_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["lowercase"], "inputs": ["result"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.100004", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.disposable_provider_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100005", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.suspicious_format_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["lowercase"], "inputs": ["result"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.100006", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.country_code_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100007", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.status_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100008", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.risk_score_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100009", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.portability_details.status_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100010", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.portability_details.first_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100011", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.portability_details.last_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100012", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "difference", "inputs": ["rule.IMIVL.100011", "vendor.idMeritData.time_adjusted_date"], "options": [{"name": "valueType", "value": "date_days"}, {"name": "useDateUtil", "value": "true"}], "output": "diff"}, {"name": "if_else", "inputs": ["diff", "___0.0__as_double__", "___1__as_int__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "result"}]}, {"rule_code_name": "IMIVL.100013", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.portability_details.times_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100014", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.original_carrier_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100015", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.current_carrier_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100016", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.type2_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100017", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.first_seen_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100018", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "difference", "inputs": ["rule.IMIVL.100017", "vendor.idMeritData.time_adjusted_date"], "options": [{"name": "valueType", "value": "date_days"}, {"name": "useDateUtil", "value": "true"}], "output": "diff"}, {"name": "if_else", "inputs": ["diff", "___0.0__as_double__", "___1__as_int__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "result"}]}, {"rule_code_name": "IMIVL.100019", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.breach_details.count_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100020", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.breach_details.first_breach_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100021", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.phone_details.breach_details.last_breach_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100022", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "difference", "inputs": ["rule.IMIVL.100020", "vendor.idMeritData.time_adjusted_date"], "options": [{"name": "valueType", "value": "date_days"}, {"name": "useDateUtil", "value": "true"}], "output": "diff"}, {"name": "if_else", "inputs": ["diff", "___0.0__as_double__", "___1__as_int__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "result"}]}, {"rule_code_name": "IMIVL.100023", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "difference", "inputs": ["rule.IMIVL.100021", "vendor.idMeritData.time_adjusted_date"], "options": [{"name": "valueType", "value": "date_days"}, {"name": "useDateUtil", "value": "true"}], "output": "diff"}, {"name": "if_else", "inputs": ["diff", "___0.0__as_double__", "___1__as_int__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "result"}]}, {"rule_code_name": "IMIVL.100024", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.amazon.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100025", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.apple.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100026", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.buklapak.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100027", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.booking.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100028", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.facebook.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100029", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.flipkart.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100030", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.google.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100031", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.instagram.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100032", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.linkedin.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100033", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.office365.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100034", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.skype.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100035", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.snapchat.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100036", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.telegram.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100037", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.twitter.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100038", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.viber.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100039", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.whatsapp.registered_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100040", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.source_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100041", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "difference", "inputs": ["input.raw.dobString_optional_", "vendor.idMeritData.time_adjusted_date"], "options": [{"name": "valueType", "value": "date_years"}, {"name": "useDateUtil", "value": "true"}], "output": "age_in_days"}, {"name": "if_else", "inputs": ["rule.IMIVL.200002", "___1.0__as_double__", "age_in_days"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.100042", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "difference", "inputs": ["rule.IMIVL.100010", "vendor.idMeritData.time_adjusted_date"], "options": [{"name": "valueType", "value": "date_years"}, {"name": "useDateUtil", "value": "true"}], "output": "diff"}, {"name": "if_else", "inputs": ["diff", "___0.0__as_double__", "___1__as_int__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "result"}]}, {"rule_code_name": "IMIVL.100140", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.result_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100141", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.message_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100142", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.trans_id_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.100143", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.request_id_optional_"], "options": [], "output": "result"}]}]}