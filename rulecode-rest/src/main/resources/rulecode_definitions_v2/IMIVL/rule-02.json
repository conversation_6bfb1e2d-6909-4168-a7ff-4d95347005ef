{"version": "v2", "rules": [{"rule_code_name": "IMIVL.200001", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "fullNames"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["fullNames", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "fullName"}], "inputs": ["list", "fullName"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200002", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900010", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.raw.dobString_optional_"], "output": "dob"}], "inputs": ["list", "dob"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200004", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900012", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.zip_optional_"], "output": "zip"}], "inputs": ["list", "zip"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200005", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["result", "___,"], "output": "names"}]}, {"name": "contains", "preprocessors": [{"methods": ["split"], "inputs": ["names", "___,"], "output": "list"}, {"methods": ["parse_brazil_names"], "inputs": ["list", "___first<PERSON>ame"], "output": "result"}, {"methods": ["join"], "inputs": ["result", "___,"], "output": "firstNames"}, {"methods": ["lowercase", "split"], "inputs": ["firstNames", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.first_name_optional_"], "output": "firstName"}], "inputs": ["list", "firstName"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200006", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["result", "___,"], "output": "names"}]}, {"name": "contains", "preprocessors": [{"methods": ["split"], "inputs": ["names", "___,"], "output": "list"}, {"methods": ["parse_brazil_names"], "inputs": ["list", "___last<PERSON>ame"], "output": "result"}, {"methods": ["join"], "inputs": ["result", "___,"], "output": "lastNames"}, {"methods": ["lowercase", "split"], "inputs": ["lastNames", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.last_name_optional_"], "output": "lastName"}], "inputs": ["list", "lastName"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200007", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900017", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.raw.physicalAddress_optional_"], "output": "<PERSON><PERSON><PERSON>ress"}], "inputs": ["list", "<PERSON><PERSON><PERSON>ress"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200008", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900018", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.raw.physicalAddress2_optional_"], "output": "physicalAddress2"}], "inputs": ["list", "physicalAddress2"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200009", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900011", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.city_optional_"], "output": "input_city"}], "inputs": ["list", "input_city"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200010", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900013", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.state_optional_"], "output": "state"}], "inputs": ["list", "state"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200012", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "inputs": ["http.idmerit.responses.response.profile.data.phone_number_optional_", "input.phone_number_optional_", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "string"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200013", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.email_optional_"], "options": [], "output": "res_email"}, {"name": "if_else", "preprocessors": [{"methods": ["lowercase"], "inputs": ["res_email"], "output": "email"}, {"methods": ["lowercase"], "inputs": ["input.raw.email_optional_"], "output": "input_email"}], "inputs": ["email", "input_email", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "string"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200015", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900014", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.raw.ssn_optional_"], "output": "ssn"}], "inputs": ["list", "ssn"], "options": [{"name": "condition", "value": "any"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200016", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "if_else", "inputs": ["rule.IMIVL.200012", "___1.0__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "=="}], "output": "phone_match"}, {"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "fullNames"}]}, {"name": "kyc_match", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["fullNames", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "fullName"}], "inputs": ["fullName", "list"], "options": [{"name": "type", "value": "name"}, {"name": "returnNoMatchOnEmptyInputs", "value": "true"}], "output": "name_match"}, {"name": "if_else", "inputs": ["phone_match", "name_match", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "&&"}], "output": "result"}]}, {"rule_code_name": "IMIVL.200017", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "fuzzy", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900001", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "fullName"}], "inputs": ["list", "fullName"], "options": [{"name": "algorithm", "value": "ratcliff"}, {"name": "decimalPoints", "value": "2"}], "output": "fullname_ratcliff"}, {"name": "minmax", "inputs": ["fullname_ratcliff"], "options": [{"name": "valueType", "value": "double"}, {"name": "condition", "value": "maximum"}], "output": "record_date_diff_max"}]}, {"rule_code_name": "IMIVL.200018", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "if_else", "inputs": ["rule.IMIVL.200012", "___1.0__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "=="}], "output": "phone_match"}, {"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["result", "___,"], "output": "names"}]}, {"name": "kyc_match", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["names", "___,"], "output": "list"}, {"methods": ["parse_brazil_names"], "inputs": ["list", "___first<PERSON>ame"], "output": "result"}, {"methods": ["join"], "inputs": ["result", "___,"], "output": "firstNames"}, {"methods": ["split"], "inputs": ["firstNames", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.first_name_optional_"], "output": "firstName"}], "inputs": ["firstName", "list"], "options": [{"name": "type", "value": "name"}, {"name": "returnNoMatchOnEmptyInputs", "value": "true"}], "output": "name_match"}, {"name": "if_else", "inputs": ["phone_match", "name_match", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "&&"}], "output": "result"}]}, {"rule_code_name": "IMIVL.200019", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "fuzzy", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900004", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.first_name_optional_"], "output": "firstName"}], "inputs": ["list", "firstName"], "options": [{"name": "algorithm", "value": "ratcliff"}, {"name": "decimalPoints", "value": "2"}], "output": "firstname_ratcliff"}, {"name": "minmax", "inputs": ["firstname_ratcliff"], "options": [{"name": "valueType", "value": "double"}, {"name": "condition", "value": "maximum"}], "output": "record_date_diff_max"}]}, {"rule_code_name": "IMIVL.200020", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "if_else", "inputs": ["rule.IMIVL.200012", "___1.0__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "=="}], "output": "phone_match"}, {"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["result", "___,"], "output": "names"}]}, {"name": "kyc_match", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["names", "___,"], "output": "list"}, {"methods": ["parse_brazil_names"], "inputs": ["list", "___last<PERSON>ame"], "output": "result"}, {"methods": ["join"], "inputs": ["result", "___,"], "output": "lastNames"}, {"methods": ["split"], "inputs": ["lastNames", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.last_name_optional_"], "output": "lastName"}], "inputs": ["lastName", "list"], "options": [{"name": "type", "value": "name"}, {"name": "returnNoMatchOnEmptyInputs", "value": "true"}], "output": "name_match"}, {"name": "if_else", "inputs": ["phone_match", "name_match", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "&&"}], "output": "result"}]}, {"rule_code_name": "IMIVL.200021", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "fuzzy", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900006", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.last_name_optional_"], "output": "lastName"}], "inputs": ["list", "lastName"], "options": [{"name": "algorithm", "value": "ratcliff"}, {"name": "decimalPoints", "value": "2"}], "output": "lastname_ratcliff"}, {"name": "minmax", "inputs": ["lastname_ratcliff"], "options": [{"name": "valueType", "value": "double"}, {"name": "condition", "value": "maximum"}], "output": "record_date_diff_max"}]}, {"rule_code_name": "IMIVL.200022", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900001", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.first_name_optional_"], "output": "firstName"}], "inputs": ["list", "firstName"], "options": [{"name": "condition", "value": "anySubString"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "IMIVL.200023", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.IMIVL.900001", "___,"], "output": "list"}, {"methods": ["lowercase"], "inputs": ["input.last_name_optional_"], "output": "lastName"}], "inputs": ["list", "lastName"], "options": [{"name": "condition", "value": "anySubString"}], "output": "is_match"}, {"name": "if_else", "inputs": ["is_match", "___true__as_boolean__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "result"}]}]}