{"version": "v2", "rules": [{"rule_code_name": "IMIVL.900001", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900002", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails_optional_"], "options": [], "output": "lookup"}, {"name": "filter", "inputs": ["lookup"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "type"}, {"name": "filterCriteria", "value": "person"}], "output": "persons"}, {"name": "lookup", "inputs": ["persons"], "options": [{"name": "attr", "value": "name"}], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900003", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails_optional_"], "options": [], "output": "lookup"}, {"name": "filter", "inputs": ["lookup"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "type"}, {"name": "filterCriteria", "value": "business"}], "output": "persons"}, {"name": "lookup", "inputs": ["persons"], "options": [{"name": "attr", "value": "name"}], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900004", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "names"}]}, {"name": "if_else", "preprocessors": [{"methods": ["split"], "inputs": ["names", "___,"], "output": "list"}, {"methods": ["parse_brazil_names"], "inputs": ["list", "___first<PERSON>ame"], "output": "result"}, {"methods": ["join"], "inputs": ["result", "___,"], "output": "firstNames"}], "inputs": ["___true__as_boolean__", "___true__as_boolean__", "firstNames", "firstNames", "firstNames"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "final_result"}]}, {"rule_code_name": "IMIVL.900005", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "names"}]}, {"name": "if_else", "preprocessors": [{"methods": ["split"], "inputs": ["names", "___,"], "output": "list"}, {"methods": ["parse_brazil_names"], "inputs": ["list", "___middle<PERSON>ame"], "output": "result"}, {"methods": ["join"], "inputs": ["result", "___,"], "output": "middleNames"}], "inputs": ["___true__as_boolean__", "___true__as_boolean__", "middleNames", "middleNames", "middleNames"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "final_result"}]}, {"rule_code_name": "IMIVL.900006", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.name_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "names"}]}, {"name": "if_else", "preprocessors": [{"methods": ["split"], "inputs": ["names", "___,"], "output": "list"}, {"methods": ["parse_brazil_names"], "inputs": ["list", "___last<PERSON>ame"], "output": "result"}, {"methods": ["join"], "inputs": ["result", "___,"], "output": "lastNames"}], "inputs": ["___true__as_boolean__", "___true__as_boolean__", "lastNames", "lastNames", "lastNames"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "=="}], "output": "final_result"}]}, {"rule_code_name": "IMIVL.900007", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.telegram.first_name_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.900008", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.telegram.last_name_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.900009", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.account_details.viber.name_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "IMIVL.900010", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.dob_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900011", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.city_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900012", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.zip_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900013", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.state_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900014", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.id_num_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900015", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.gender_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900017", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.address1_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900018", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails.address2_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "IMIVL.900019", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.idmerit.responses.response.profile.data.persondetails_optional_"], "options": [], "output": "lookup"}, {"name": "filter", "inputs": ["lookup"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "type"}, {"name": "filterCriteria", "value": "person"}], "output": "persons"}, {"name": "lookup", "inputs": ["persons"], "options": [{"name": "attr", "value": "id_num"}], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}]}