{"version": "v2", "rules": [{"rule_code_name": "MNIVL.300001", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.names.validSince_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300002", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.names.lastSeen_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300003", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.genders.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300004", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.genders.validSince_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300005", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.genders.lastSeen_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300006", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.dateOfBirths.validSince_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300007", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.dateOfBirths.lastSeen_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300008", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.validSince_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300009", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.lastSeen_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300010", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.geoLocation_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300011", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.addressType_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300012", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.phoneNumber.validSince_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300013", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.phoneNumber.lastSeen_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300014", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.emailAddress.validSince_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300015", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.emailAddress.lastSeen_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300016", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.phoneDetails.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300017", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.phoneDetails.phoneType_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300018", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.phoneDetails.validSince_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300019", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.phoneDetails.lastSeen_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300020", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.emailDetails.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300021", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.emailDetails.validSince_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300022", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.emailDetails.lastSeen_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300023", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.ipAddress.validSince_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300024", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.ipAddress.lastSeen_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300025", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.additionalDetails.occupation_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300026", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.additionalDetails.incomeCurrency_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300027", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.additionalDetails.incomeAmount_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300028", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.analysis.nameUniqueness.nameConsistencyScore_optional_"], "options": [], "output": "final_result"}]}, {"rule_code_name": "MNIVL.300029", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.analysis.nameUniqueness.namesConsidered_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300030", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.analysis.nameUniqueness.resolvedNames_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300031", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.analysis.nameUniqueness.numberOfUniqueIdentities_optional_"], "options": [], "output": "final_result"}]}, {"rule_code_name": "MNIVL.300032", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.analysis.nameUniqueness.numberOfNamesConsidered_optional_"], "options": [], "output": "final_result"}]}, {"rule_code_name": "MNIVL.300033", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.names.type_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.300034", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.identityDetails.otherId_optional_"], "options": [], "output": "otherId_lookup", "postprocessors": [{"methods": ["flatten"], "inputs": ["otherId_lookup"], "output": "flattened_otherId_lookup"}]}, {"name": "filter", "inputs": ["flattened_otherId_lookup"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "idName"}, {"name": "filterCriteria", "value": "CPF_ID"}], "output": "idName_filter"}, {"name": "lookup", "inputs": ["idName_filter"], "options": [{"name": "attr", "value": "value"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.300035", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "difference", "preprocessors": [{"methods": ["time_adjusted_date_as_date_str", "extract_year_from_date"], "inputs": ["input.raw.submissionDate_optional_", "input.raw.transactionDate_optional_"], "output": "time_adjusted_date_year"}, {"methods": ["extract_year_from_date"], "inputs": ["input.raw.dobString_optional_"], "output": "input_dob_year"}], "inputs": ["time_adjusted_date_year", "input_dob_year"], "options": [{"name": "valueType", "value": "int"}], "postprocessors": [{"methods": ["suppress_negative_value"], "inputs": ["date_max_diff"], "output": "current_age"}], "output": "date_max_diff"}, {"name": "if_else", "inputs": ["rule.MNIVL.200002", "___1__as_double__", "current_age", "_____as_<PERSON>A__", "_____as_<PERSON>A__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "=="}], "output": "result"}]}]}