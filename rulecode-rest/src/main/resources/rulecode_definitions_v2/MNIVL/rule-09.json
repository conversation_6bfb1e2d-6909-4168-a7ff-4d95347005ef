{"version": "v2", "rules": [{"rule_code_name": "MNIVL.900001", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.names.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900002", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.dateOfBirths.dateRange_optional_"], "options": [{"name": "parseJsonToStr", "value": "true"}], "output": "temp_result"}]}, {"rule_code_name": "MNIVL.900003", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.fullAddress_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900004", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.addressLine1_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900005", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.addressLine2_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900006", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.city_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900007", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.state_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900008", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.postalCode_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900009", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.country_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900010", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.countryCode_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900011", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.phoneNumber.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900012", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.emailAddress.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900013", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.ipAddress.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900014", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.nationalId_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900015", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.identityDetails.passport.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900016", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.identityDetails.pan.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900017", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.identityDetails.aadhaar.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900018", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.identityDetails.voterId.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900019", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.identityDetails.driversLicense.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900020", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.identityDetails.rationCard.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900021", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.identityDetails.otherId.value_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "lowercase"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.900022", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.identityDetails.otherId.idName_optional_"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join"], "inputs": ["temp_result", "___::"], "output": "final_result"}]}]}]}