{"version": "v2", "rules": [{"rule_code_name": "MNIVL.100001", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.phoneValid_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["lowercase"], "inputs": ["result"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.100002", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.phoneType_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100003", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.phoneDisposable_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["lowercase"], "inputs": ["result"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.100004", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.active_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100005", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.active_optional_"], "options": [], "output": "isActive"}, {"name": "if_else", "inputs": ["isActive", "___YES__as_string__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "string"}, {"name": "condition", "value": "=="}], "output": "phone_is_active"}]}, {"rule_code_name": "MNIVL.100006", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.active_optional_"], "options": [], "output": "isActive"}, {"name": "if_else", "inputs": ["isActive", "___NO__as_string__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "dataType", "value": "string"}, {"name": "condition", "value": "=="}], "output": "phone_is_not_active"}]}, {"rule_code_name": "MNIVL.100007", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.activationDate_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100008", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.activeSinceXDays_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100009", "type": "numerical", "default": "NA", "operators": [{"name": "difference", "preprocessors": [{"methods": ["getTimeAdjustedDate"], "inputs": ["input.raw.submissionDate_optional_", "input.raw.transactionDate_optional_"], "output": "timeAdjustedDate"}, {"methods": ["date_time_as_millis"], "inputs": ["http.monnai.responses.response.data.phone.basic.activationDate_optional_"], "output": "activationDate"}], "inputs": ["activationDate", "timeAdjustedDate"], "options": [{"name": "valueType", "value": "date_days"}, {"name": "mode", "value": "one_to_many"}, {"name": "exact", "value": "true"}], "output": "days_difference", "postprocessors": [{"methods": ["str_to_num"], "inputs": ["days_difference", "___double"], "output": "days_diff_double"}]}, {"name": "if_else", "inputs": ["days_diff_double", "___0__as_double__", "days_diff_double"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "phone_is_not_active"}]}, {"rule_code_name": "MNIVL.100010", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.simType_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100011", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.phoneTenure.max_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100012", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.phoneTenure.min_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100013", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.country_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100014", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.originalCarrier_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100015", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.ported_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["lowercase"], "inputs": ["result"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.100016", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.portedHistory.portedDate_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100017", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.portedHistory.numberOfPorts_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100018", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.portedHistory.portedSinceXDays_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100019", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.portedHistory.currentCarrierCircle_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100020", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.portedHistory.originalCarrierCircle_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100021", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.portedHistory.changeInCarrierRegion_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100022", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.portedHistory.portedEvents.portedDate_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "dates"}]}]}, {"rule_code_name": "MNIVL.100023", "type": "categorical", "default": "NA", "operators": [{"name": "minmax", "inputs": ["http.monnai.responses.response.data.phone.basic.portedHistory.portedEvents.portedDate_optional_"], "options": [{"name": "valueType", "value": "date"}, {"name": "condition", "value": "minimum"}], "output": "min_date"}]}, {"rule_code_name": "MNIVL.100029", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.portedHistory.portedEvents.carrierCircle_optional_"], "options": [], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.100030", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.currentNetwork.mobileCountryCode_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100031", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.currentNetwork.mobileNetworkCode_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100032", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.currentNetwork.networkName_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100033", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.basic.currentNetwork.serviceProfileId_optional_"], "options": [], "output": "result"}]}, {"rule_code_name": "MNIVL.100140", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.errors_optional_"], "options": [], "output": "lookup"}, {"name": "filter", "inputs": ["lookup"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "package"}, {"name": "filterCriteria", "value": "IDENTITY_ENRICHMENT"}], "output": "monnai_errors"}, {"name": "lookup", "inputs": ["monnai_errors"], "options": [{"name": "attr", "value": "message"}], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.100141", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.errors_optional_"], "options": [], "output": "lookup"}, {"name": "filter", "inputs": ["lookup"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "package"}, {"name": "filterCriteria", "value": "PHONE_BASIC"}], "output": "phone_errors"}, {"name": "lookup", "inputs": ["phone_errors"], "options": [{"name": "attr", "value": "message"}], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}]}