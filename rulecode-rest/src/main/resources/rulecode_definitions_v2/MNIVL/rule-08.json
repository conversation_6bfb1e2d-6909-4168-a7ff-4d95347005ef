{"version": "v2", "rules": [{"rule_code_name": "MNIVL.800001", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.simswap.status_optional_"], "options": [{"name": "trimResult", "value": "true"}], "output": "result"}]}, {"rule_code_name": "MNIVL.800002", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.simswap.minHours_optional_"], "options": [{"name": "trimResult", "value": "true"}], "output": "result"}]}, {"rule_code_name": "MNIVL.800003", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.simswap.maxHours_optional_"], "options": [{"name": "trimResult", "value": "true"}], "output": "result"}]}, {"rule_code_name": "MNIVL.800004", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.phone.simswap.timestamp_optional_"], "options": [{"name": "trimResult", "value": "true"}], "output": "result"}]}, {"rule_code_name": "MNIVL.800140", "type": "categorical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.errors_optional_"], "options": [], "output": "lookup"}, {"name": "filter", "inputs": ["lookup"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "package"}, {"name": "filterCriteria", "value": "PHONE_SIMSWAP"}], "output": "sim_swap"}, {"name": "lookup", "inputs": ["sim_swap"], "options": [{"name": "attr", "value": "message"}], "output": "result", "postprocessors": [{"methods": ["join"], "inputs": ["result", "___,"], "output": "final_result"}]}]}, {"rule_code_name": "MNIVL.800005", "type": "numerical", "default": "NA", "operators": [{"name": "difference", "preprocessors": [{"name": "getTimeAdjustedDate", "methods": ["getTimeAdjustedDate"], "inputs": ["input.raw.submissionDate_optional_", "input.raw.transactionDate_optional_"], "output": "time_adjusted_date"}], "inputs": ["http.monnai.responses.response.data.phone.simswap.timestamp_optional_", "time_adjusted_date"], "options": [{"name": "valueType", "value": "date_days"}], "output": "result"}]}]}