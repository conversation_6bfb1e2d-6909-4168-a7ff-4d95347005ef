{"version": "v2", "vendor_variables": [{"variable_name": "monnai", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses"], "options": [{"name": "arrayIndex", "value": "0"}], "output": "lookup"}]}], "rules": [{"rule_code_name": "MNIVL.200001", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons.names.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "input_fullName_lower"}], "inputs": ["temp_result", "input_fullName_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200002", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons.dateOfBirths.dateRange.start"], "options": [], "output": "date_starts"}, {"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons.dateOfBirths.dateRange.end"], "options": [], "output": "date_ends"}, {"name": "difference", "preprocessors": [{"methods": ["asArray"], "inputs": ["date_starts"], "output": "date_starts_array"}], "inputs": ["input.raw.dobString_optional_", "date_starts_array"], "options": [{"name": "valueType", "value": "date_days"}, {"name": "mode", "value": "one_to_many"}, {"name": "exact", "value": "true"}], "output": "start_dates_difference", "postprocessors": [{"methods": ["str_to_num"], "flattenInputs": false, "inputs": ["start_dates_difference", "___false", "___double"], "output": "start_dates_difference_double"}]}, {"name": "difference", "preprocessors": [{"methods": ["asArray"], "inputs": ["date_ends"], "output": "date_ends_array"}], "inputs": ["input.raw.dobString_optional_", "date_ends_array"], "options": [{"name": "valueType", "value": "date_days"}, {"name": "mode", "value": "one_to_many"}], "output": "end_dates_difference", "postprocessors": [{"methods": ["str_to_num"], "flattenInputs": false, "inputs": ["end_dates_difference", "___false", "___double"], "output": "end_dates_difference_double"}]}, {"name": "arithmetic", "inputs": ["start_dates_difference_double", "end_dates_difference_double"], "options": [{"name": "outputDataType", "value": "double"}, {"name": "operation", "value": "*"}], "output": "diff_product"}, {"name": "minmax", "preprocessors": [{"methods": ["asArray"], "inputs": ["diff_product"], "output": "results_as_array"}], "inputs": ["results_as_array"], "options": [{"name": "valueType", "value": "double"}, {"name": "condition", "value": "minimum"}], "output": "min_diff_product"}, {"name": "if_else", "inputs": ["min_diff_product", "___0__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "<="}], "output": "result"}]}, {"rule_code_name": "MNIVL.200003", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.fullAddress_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.std_address_optional_"], "output": "input_address_lower"}], "inputs": ["temp_result", "input_address_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200004", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.postalCode_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.zip_optional_"], "output": "input_postal_code_lower"}], "inputs": ["temp_result", "input_postal_code_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200005", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.names.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "input_fullName_lower"}, {"methods": ["parse_brazil_names"], "inputs": ["input_fullName_lower", "___first<PERSON>ame"], "output": "input_first_names"}], "inputs": ["temp_result", "input_first_names"], "options": [{"name": "condition", "value": "anyNonEmptySubString"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200006", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.names.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "input_fullName_lower"}, {"methods": ["parse_brazil_names"], "inputs": ["input_fullName_lower", "___last<PERSON>ame"], "output": "input_last_names"}], "inputs": ["temp_result", "input_last_names"], "options": [{"name": "condition", "value": "anyNonEmptySubString"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200007", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.addressLine1_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.physicalAddress_optional_"], "output": "input_address_line1_lower"}], "inputs": ["temp_result", "input_address_line1_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200008", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.addressLine2_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.physicalAddress2_optional_"], "output": "input_address_line2_lower"}], "inputs": ["temp_result", "input_address_line2_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200009", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.city_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.city_optional_"], "output": "input_city_lower"}], "inputs": ["temp_result", "input_city_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200010", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.addresses.state_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.state_optional_"], "output": "input_state_lower"}], "inputs": ["temp_result", "input_state_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200012", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.phoneNumber.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.mobileNumber_optional_"], "output": "input_phone_number_lower"}], "inputs": ["temp_result", "input_phone_number_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200013", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.emailAddress.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.email_optional_"], "output": "input_email_lower"}], "inputs": ["temp_result", "input_email_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200014", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.ipAddresses.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.ip_optional_"], "output": "input_ip_lower"}], "inputs": ["temp_result", "input_ip_lower"], "options": [{"name": "condition", "value": "any"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200015", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.nationalId_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "nationalId_temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.ssn_optional_"], "output": "input_ssn_lower"}], "inputs": ["nationalId_temp_result", "input_ssn_lower"], "options": [{"name": "condition", "value": "any"}], "output": "nationalId_result"}, {"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.passport.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "passport_temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.ssn_optional_"], "output": "input_ssn_lower"}], "inputs": ["passport_temp_result", "input_ssn_lower"], "options": [{"name": "condition", "value": "any"}], "output": "passport_result"}, {"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.aadhaar.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "a<PERSON><PERSON><PERSON>_temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.ssn_optional_"], "output": "input_ssn_lower"}], "inputs": ["a<PERSON><PERSON><PERSON>_temp_result", "input_ssn_lower"], "options": [{"name": "condition", "value": "any"}], "output": "aadhaar_result"}, {"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.pan.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "pan_temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.ssn_optional_"], "output": "input_ssn_lower"}], "inputs": ["pan_temp_result", "input_ssn_lower"], "options": [{"name": "condition", "value": "any"}], "output": "pan_result"}, {"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.voterId.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "voterId_temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.ssn_optional_"], "output": "input_ssn_lower"}], "inputs": ["voterId_temp_result", "input_ssn_lower"], "options": [{"name": "condition", "value": "any"}], "output": "voterId_result"}, {"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.rationCard.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "rationCard_temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.ssn_optional_"], "output": "input_ssn_lower"}], "inputs": ["rationCard_temp_result", "input_ssn_lower"], "options": [{"name": "condition", "value": "any"}], "output": "rationCard_result"}, {"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.driversLicense.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "driversLicense_temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.ssn_optional_"], "output": "input_ssn_lower"}], "inputs": ["driversLicense_temp_result", "input_ssn_lower"], "options": [{"name": "condition", "value": "any"}], "output": "driversLicense_result"}, {"name": "lookup", "inputs": ["http.monnai.responses.response.data.identity.enrichment.persons.otherId.value_optional_"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "otherId_temp_result"}]}, {"name": "contains", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.ssn_optional_"], "output": "input_ssn_lower"}], "inputs": ["otherId_temp_result", "input_ssn_lower"], "options": [{"name": "condition", "value": "any"}], "output": "otherId_result"}, {"name": "minmax", "preprocessors": [{"methods": ["asArray"], "inputs": ["nationalId_result", "passport_result", "aadhaar_result", "pan_result", "voterId_result", "rationCard_result", "driversLicense_result", "otherId_result"], "output": "results_as_array"}], "inputs": ["results_as_array"], "options": [{"name": "valueType", "value": "bool"}, {"name": "condition", "value": "maximum"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200016", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "filter", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.mobileNumber_optional_"], "output": "input_phone_number_lower"}], "inputs": ["temp_result", "input_phone_number_lower"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "phoneNumber.value"}, {"name": "isCaseInsensitive", "value": "true"}, {"name": "ignoreEmptyFilterCriteria", "value": "true"}, {"name": "attrToBeFilteredSafeExtract", "value": "true"}], "output": "filter_result"}, {"name": "lookup", "inputs": ["filter_result"], "options": [{"name": "attr", "value": "names.value"}], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "lookup_result"}]}, {"name": "kyc_match", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "fullName_lower"}], "inputs": ["fullName_lower", "lookup_result"], "options": [{"name": "type", "value": "name"}, {"name": "returnNoMatchOnEmptyInputs", "value": "true"}], "output": "is_match"}]}, {"rule_code_name": "MNIVL.200017", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "filter", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.mobileNumber_optional_"], "output": "input_phone_number_lower"}], "inputs": ["temp_result", "input_phone_number_lower"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "phoneNumber.value"}, {"name": "isCaseInsensitive", "value": "true"}, {"name": "ignoreEmptyFilterCriteria", "value": "true"}, {"name": "attrToBeFilteredSafeExtract", "value": "true"}], "output": "filter_result"}, {"name": "lookup", "inputs": ["filter_result"], "options": [{"name": "attr", "value": "names.value"}], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "lookup_result"}]}, {"name": "fuzzy", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "fullName_lower"}], "inputs": ["lookup_result", "fullName_lower"], "options": [{"name": "algorithm", "value": "ratcliff"}, {"name": "decimalPoints", "value": "4"}, {"name": "returnNoMatchOnEmptyInputs", "value": "true"}], "output": "is_match"}, {"name": "minmax", "preprocessors": [{"methods": ["asArray"], "inputs": ["is_match"], "output": "results_as_array"}], "inputs": ["results_as_array"], "options": [{"name": "valueType", "value": "double"}, {"name": "condition", "value": "maximum"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200018", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "filter", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.mobileNumber_optional_"], "output": "input_phone_number_lower"}], "inputs": ["temp_result", "input_phone_number_lower"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "phoneNumber.value"}, {"name": "isCaseInsensitive", "value": "true"}, {"name": "ignoreEmptyFilterCriteria", "value": "true"}, {"name": "attrToBeFilteredSafeExtract", "value": "true"}], "output": "filter_result"}, {"name": "lookup", "inputs": ["filter_result"], "options": [{"name": "attr", "value": "names.value"}], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "lookup_result"}, {"methods": ["parse_brazil_names"], "inputs": ["lookup_result", "___first<PERSON>ame"], "output": "lookup_first_names"}]}, {"name": "kyc_match", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "fullName_lower"}, {"methods": ["parse_brazil_names", "head"], "inputs": ["fullName_lower", "___first<PERSON>ame"], "output": "input_first_names"}], "inputs": ["input_first_names", "lookup_first_names"], "options": [{"name": "type", "value": "name"}, {"name": "returnNoMatchOnEmptyInputs", "value": "true"}], "output": "is_match"}]}, {"rule_code_name": "MNIVL.200019", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "filter", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.mobileNumber_optional_"], "output": "input_phone_number_lower"}], "inputs": ["temp_result", "input_phone_number_lower"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "phoneNumber.value"}, {"name": "isCaseInsensitive", "value": "true"}, {"name": "ignoreEmptyFilterCriteria", "value": "true"}, {"name": "attrToBeFilteredSafeExtract", "value": "true"}], "output": "filter_result"}, {"name": "lookup", "inputs": ["filter_result"], "options": [{"name": "attr", "value": "names.value"}], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "lookup_result"}, {"methods": ["parse_brazil_names"], "inputs": ["lookup_result", "___first<PERSON>ame"], "output": "lookup_first_names"}]}, {"name": "fuzzy", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "fullName_lower"}, {"methods": ["parse_brazil_names", "head"], "inputs": ["fullName_lower", "___first<PERSON>ame"], "output": "input_first_names"}], "inputs": ["lookup_first_names", "input_first_names"], "options": [{"name": "algorithm", "value": "ratcliff"}, {"name": "decimalPoints", "value": "4"}, {"name": "returnNoMatchOnEmptyInputs", "value": "true"}], "output": "is_match"}, {"name": "minmax", "preprocessors": [{"methods": ["asArray"], "inputs": ["is_match"], "output": "results_as_array"}], "inputs": ["results_as_array"], "options": [{"name": "valueType", "value": "double"}, {"name": "condition", "value": "maximum"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200020", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "filter", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.mobileNumber_optional_"], "output": "input_phone_number_lower"}], "inputs": ["temp_result", "input_phone_number_lower"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "phoneNumber.value"}, {"name": "isCaseInsensitive", "value": "true"}, {"name": "ignoreEmptyFilterCriteria", "value": "true"}, {"name": "attrToBeFilteredSafeExtract", "value": "true"}], "output": "filter_result"}, {"name": "lookup", "inputs": ["filter_result"], "options": [{"name": "attr", "value": "names.value"}], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "lookup_result"}, {"methods": ["parse_brazil_names"], "inputs": ["lookup_result", "___last<PERSON>ame"], "output": "lookup_last_names"}]}, {"name": "kyc_match", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "fullName_lower"}, {"methods": ["parse_brazil_names", "head"], "inputs": ["fullName_lower", "___last<PERSON>ame"], "output": "input_last_names"}], "inputs": ["input_last_names", "lookup_last_names"], "options": [{"name": "type", "value": "name"}, {"name": "returnNoMatchOnEmptyInputs", "value": "true"}], "output": "is_match"}]}, {"rule_code_name": "MNIVL.200021", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons"], "options": [], "output": "lookup", "postprocessors": [{"methods": ["flatten"], "inputs": ["lookup"], "output": "temp_result"}]}, {"name": "filter", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.raw.mobileNumber_optional_"], "output": "input_phone_number_lower"}], "inputs": ["temp_result", "input_phone_number_lower"], "options": [{"name": "filterType", "value": "In"}, {"name": "attrToBeFiltered", "value": "phoneNumber.value"}, {"name": "isCaseInsensitive", "value": "true"}, {"name": "ignoreEmptyFilterCriteria", "value": "true"}, {"name": "attrToBeFilteredSafeExtract", "value": "true"}], "output": "filter_result"}, {"name": "lookup", "inputs": ["filter_result"], "options": [{"name": "attr", "value": "names.value"}], "output": "lookup", "postprocessors": [{"methods": ["flatten", "lowercase"], "inputs": ["lookup"], "output": "lookup_result"}, {"methods": ["parse_brazil_names"], "inputs": ["lookup_result", "___last<PERSON>ame"], "output": "lookup_last_names"}]}, {"name": "fuzzy", "preprocessors": [{"methods": ["lowercase"], "inputs": ["input.fullName_optional_"], "output": "fullName_lower"}, {"methods": ["parse_brazil_names", "head"], "inputs": ["fullName_lower", "___last<PERSON>ame"], "output": "input_last_names"}], "inputs": ["lookup_last_names", "input_last_names"], "options": [{"name": "algorithm", "value": "ratcliff"}, {"name": "decimalPoints", "value": "4"}, {"name": "returnNoMatchOnEmptyInputs", "value": "true"}], "output": "is_match"}, {"name": "minmax", "preprocessors": [{"methods": ["asArray"], "inputs": ["is_match"], "output": "results_as_array"}], "inputs": ["results_as_array"], "options": [{"name": "valueType", "value": "double"}, {"name": "condition", "value": "maximum"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200022", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.MNIVL.900001", "___,"], "output": "lookup_names"}, {"methods": ["lowercase"], "inputs": ["input.first_name_optional_"], "output": "input_first_name"}], "inputs": ["lookup_names", "input_first_name"], "options": [{"name": "condition", "value": "anySubString"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200023", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "contains", "preprocessors": [{"methods": ["lowercase", "split"], "inputs": ["rule.MNIVL.900001", "___,"], "output": "lookup_names"}, {"methods": ["lowercase"], "inputs": ["input.last_name_optional_"], "output": "input_last_name"}], "inputs": ["lookup_names", "input_last_name"], "options": [{"name": "condition", "value": "anySubString"}], "output": "final_result"}]}, {"rule_code_name": "MNIVL.200050", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "arithmetic", "inputs": ["rule.MNIVL.200004", "rule.MNIVL.200009"], "options": [{"name": "outputDataType", "value": "double"}, {"name": "operation", "value": "+"}], "output": "first_sum"}, {"name": "arithmetic", "inputs": ["first_sum", "rule.MNIVL.200010"], "options": [{"name": "outputDataType", "value": "double"}, {"name": "operation", "value": "+"}], "output": "final_sum"}, {"name": "if_else", "inputs": ["final_sum", "___3__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___true__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "<"}], "output": "result"}]}, {"rule_code_name": "MNIVL.200051", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "size", "preprocessors": [{"methods": ["asArray"], "inputs": ["rule.MNIVL.900006", "rule.MNIVL.900007", "rule.MNIVL.900008"], "output": "rules_array"}, {"methods": ["filter_non_empty_value"], "inputs": ["rules_array"], "output": "non_empty_rules"}], "inputs": ["non_empty_rules"], "options": [], "output": "rules_size"}, {"name": "if_else", "inputs": ["rules_size", "___3__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___true__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "<"}], "output": "result"}]}, {"rule_code_name": "MNIVL.200052", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "arithmetic", "inputs": ["rule.MNIVL.200001", "rule.MNIVL.200002"], "options": [{"name": "outputDataType", "value": "double"}, {"name": "operation", "value": "+"}], "output": "first_sum"}, {"name": "arithmetic", "inputs": ["first_sum", "rule.MNIVL.200003"], "options": [{"name": "outputDataType", "value": "double"}, {"name": "operation", "value": "+"}], "output": "final_sum"}, {"name": "if_else", "inputs": ["final_sum", "___3__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "MNIVL.200053", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "arithmetic", "inputs": ["rule.MNIVL.200001", "rule.MNIVL.200002"], "options": [{"name": "outputDataType", "value": "double"}, {"name": "operation", "value": "+"}], "output": "final_sum"}, {"name": "if_else", "inputs": ["final_sum", "___2__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "MNIVL.200054", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "arithmetic", "inputs": ["rule.MNIVL.200001", "rule.MNIVL.200003"], "options": [{"name": "outputDataType", "value": "double"}, {"name": "operation", "value": "+"}], "output": "final_sum"}, {"name": "if_else", "inputs": ["final_sum", "___2__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": "=="}], "output": "result"}]}, {"rule_code_name": "MNIVL.200055", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.analysis.nameUniqueness.nameConsistencyScore"], "options": [], "output": "score"}, {"name": "if_else", "inputs": ["score", "___0.95__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "result"}]}, {"rule_code_name": "MNIVL.200056", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "size", "preprocessors": [{"methods": ["split"], "inputs": ["rule.MNIVL.900015", "___::"], "output": "rule_15"}, {"methods": ["split"], "inputs": ["rule.MNIVL.900016", "___::"], "output": "rule_16"}, {"methods": ["split"], "inputs": ["rule.MNIVL.900017", "___::"], "output": "rule_17"}, {"methods": ["split"], "inputs": ["rule.MNIVL.900018", "___::"], "output": "rule_18"}, {"methods": ["split"], "inputs": ["rule.MNIVL.900019", "___::"], "output": "rule_19"}, {"methods": ["flatten"], "inputs": ["rule_15", "rule_16", "rule_17", "rule_18", "rule_19"], "output": "all_rules"}], "inputs": ["all_rules"], "options": [], "output": "size"}, {"name": "if_else", "inputs": ["size", "___2__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "result"}]}, {"rule_code_name": "MNIVL.200057", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["vendor.monnai.lookup$$.response.data.identity.enrichment.persons.additionalDetails.incomeAmount"], "options": [], "output": "temp_result", "postprocessors": [{"methods": ["join", "str_len"], "inputs": ["temp_result", "___::"], "output": "len"}]}, {"name": "if_else", "inputs": ["len", "___0__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">"}], "output": "result"}]}, {"rule_code_name": "MNIVL.200059", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "count", "preprocessors": [{"methods": ["asArray"], "inputs": ["rule.MNIVL.900015", "rule.MNIVL.900016", "rule.MNIVL.900019", "rule.MNIVL.900021"], "output": "all_rules"}], "inputs": ["all_rules"], "options": [], "output": "num_entries"}, {"name": "if_else", "inputs": ["num_entries", "___2__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "result"}]}, {"rule_code_name": "MNIVL.200060", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "size", "preprocessors": [{"methods": ["split"], "inputs": ["rule.MNIVL.900013", "___::"], "output": "rule_13"}], "inputs": ["rule_13"], "options": [], "output": "size"}, {"name": "if_else", "inputs": ["size", "___1__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "result"}]}, {"rule_code_name": "MNIVL.200061", "type": "numerical", "default": "NA", "is_derived": true, "operators": [{"name": "size", "preprocessors": [{"methods": ["split"], "inputs": ["rule.MNIVL.900011", "___::"], "output": "rule_11"}], "inputs": ["rule_11"], "options": [], "output": "rule_11_entries"}, {"name": "size", "preprocessors": [{"methods": ["split"], "inputs": ["rule.MNIVL.900012", "___::"], "output": "rule_12"}], "inputs": ["rule_12"], "options": [], "output": "rule_12_entries"}, {"name": "if_else", "inputs": ["rule_11_entries", "___1__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "rule_11_result"}, {"name": "if_else", "inputs": ["rule_12_entries", "___1__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "double"}, {"name": "condition", "value": ">="}], "output": "rule_12_result"}, {"name": "if_else", "inputs": ["rule_11_result", "rule_12_result", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "dataType", "value": "boolean"}, {"name": "condition", "value": "&&"}], "output": "result"}]}]}