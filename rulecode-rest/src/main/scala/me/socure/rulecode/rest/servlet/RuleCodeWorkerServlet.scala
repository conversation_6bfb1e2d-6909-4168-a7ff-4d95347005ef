package me.socure.rulecode.rest.servlet

import com.google.inject.Inject
import io.swagger.v3.oas.models.OpenAPI
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.openapi3.scalatra.OpenApiScalatraSupport
import me.socure.common.openapi3.scalatra.model.{JsonRequestBody, JsonResponseBody}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.common.zio.error.DomainError
import me.socure.common.zio.service.Service
import me.socure.model.{ErrorResponse, Response}
import me.socure.rulecode.common.RuleCodeWorkflow
import me.socure.rulecode.common.models._
import me.socure.rulecode.workers.VendorRequest
import org.json4s.{Extraction, Formats}
import org.scalatra
import org.scalatra.json.JacksonJsonSupport
import org.scalatra.{FutureSupport, ScalatraServlet}
import org.slf4j.{Lo<PERSON>, LoggerFactory}
import zio.Runtime

import scala.concurrent.{ExecutionContext, Future}
import scala.reflect.runtime.{universe => ru}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

class RuleCodeWorkerServlet @Inject()(ruleCodeWorkflow: RuleCodeWorkflow,
                                      val hmacVerifier: HMACHttpVerifier,
                                      val openApi: OpenAPI,
                                      serviceRuntime: Runtime[_],
                                      rcService: Service[VendorRequest, RuleCodeResponse]
                                     )
                                     (implicit val executor: ExecutionContext) extends
  ScalatraServlet with JacksonJsonSupport with FutureSupport with AuthenticationSupport with OpenApiScalatraSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)
  private final val metrics: Metrics = JavaMetricsFactory.get("rulecodeworker." + getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = JsonFormats.RuleCodeWorkerFormats

  before() {
    contentType = formats("json")
    validateRequest()
  }

  val RuleCodeWorkerSpec: scalatra.RouteTransformer = endpointSpec(
    description = "Generate Rulecodes For Vendors - Unified Call",
    summary = "Returns rulecode for the requested vendors",
    params = Set.empty,
    requestBodies = Some(JsonRequestBody(`type` = ru.typeOf[RuleCodeWorkerRequest], required = true, description = Some("Triggers rulecodes"))),
    responseBodies = Some(Map(
      200 -> JsonResponseBody(`type` = ru.typeOf[RuleCodeWorkerResponse], description = Some("Rulecode Success Response")),
      400 -> JsonResponseBody(`type` = ru.typeOf[Response[ErrorResponse]], description = Some("Represents an error related to user input")),
      500 -> JsonResponseBody(`type` = ru.typeOf[Response[ErrorResponse]], description = Some("Represents an internal server error"))
    )
    ))

  post("/work", RuleCodeWorkerSpec) {
    Try(parsedBody.extract[RuleCodeWorkerRequest]) match {
      case Success(req) =>

        val rcRequest = req.ruleCodeRequest

        val vendorConfigs = req.rcVendorConfigs


        val generatedRequests = vendorConfigs.map { vendorConf =>
          VendorRequest(vendorConf.name, vendorConf.timeout, Extraction.decompose(rcRequest.copy(vendor = Some(vendorConf.name))))
        }

        val res = Service.foreachPar(generatedRequests) { req =>
          rcService.provide(req)
            .fold(
              failure = {
                case DomainError(vendor, _) =>
                  logger.error(s"$vendor timeout")
                  RuleCodeWorkerResponse(req.vendorName, errorMessage = Some(s"$vendor timeout"))
                case baseError =>
                  logger.error(s"Error while processing rulecode ${req.vendorName} ${baseError.fullMsg}")
                  RuleCodeWorkerResponse(req.vendorName, errorMessage = Some(baseError.fullMsg))
              },
              success = { rcResp =>
                RuleCodeWorkerResponse(req.vendorName, Some(rcResp))
              }
            )
        }


        ScalatraResponseFactory.get {
          serviceRuntime.unsafeRunToFuture(res).map(Right(_))
        }

      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
    }
  }

  post("/work/v2", RuleCodeWorkerSpec) {
    Try(parsedBody.extract[RuleCodeWorkerRequest]) match {
      case Success(req) =>

        val rcRequest = req.ruleCodeRequest

        val vendorConfigs = req.ruleCodeNames.getOrElse(Set.empty).map(vendorIdentifier=>RcVendorConfig(vendorIdentifier.split("\\.")(0),4000)).toSeq

        val generatedRequests = vendorConfigs.map { vendorConf =>
          VendorRequest(vendorConf.name, vendorConf.timeout, Extraction.decompose(rcRequest.copy(vendor = Some(vendorConf.name))))
        }

        val res = Service.foreachPar(generatedRequests) { req =>
          rcService.provide(req)
            .fold(
              failure = {
                case DomainError(vendor, _) =>
                  logger.error(s"$vendor timeout")
                  RuleCodeWorkerResponse(req.vendorName, errorMessage = Some(s"$vendor timeout"))
                case baseError =>
                  logger.error(s"Error while processing rulecode ${req.vendorName} ${baseError.fullMsg}")
                  RuleCodeWorkerResponse(req.vendorName, errorMessage = Some(baseError.fullMsg))
              },
              success = { rcResp =>
                RuleCodeWorkerResponse(req.vendorName, Some(rcResp))
              }
            )
        }


        ScalatraResponseFactory.get {
          serviceRuntime.unsafeRunToFuture(res).map(Right(_))
        }

      case Failure(NonFatal(ex)) =>
        logger.error("Invalid request format", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
    }
  }

}
