package me.socure.rulecode.rest.workflow

import com.typesafe.config.Config
import me.socure.common.clock.Clock
import me.socure.common.logger.{TransactionAwareLogger, TransactionAwareLoggerFactory}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId
import me.socure.dynamic.control.center.v2.response.EvaluateResponse
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.model.ErrorResponse
import me.socure.rulecode.common.bestmatch.util.BestMatchResolverLookupUtility
import me.socure.rulecode.common.models._
import me.socure.rulecode.common.utilities.{Json4sUtility, RulecodeServiceConstants}
import me.socure.rulecode.dao.{DbValue, DbValueStringList, ValueAndCondition}
import me.socure.rulecode.dynamo.database.DynamoQueryClient
import me.socure.rulecode.dynamo.factory.DynamoQueryFactory
import me.socure.rulecode.dynamo.model.{CustomErrorMessage, SimpleKeyBatchResult}
import me.socure.rulecode.filedata.RulecodeDataFileLookup
import me.socure.rulecode.http.plugin.RuleCodeHttpPlugin
import me.socure.rulecode.rest.model.Dependencies
import me.socure.rulecode.rest.service.impl.RuleCodeGeneratorV2
import me.socure.rulecode.rest.utilities.{DbValueUtility, RuleCodeDBAudit, RuleCodeFileAudit}
import me.socure.service.audit.client.TPAuditClient
import me.socure.service.audit.model.AuditInfo
import me.socure.thirdparty.auditing.client.ThirdPartyAuditor
import org.joda.time.DateTime
import org.json4s.JsonAST.{JArray, JNothing, JValue}
import org.json4s.jackson.JsonMethods._
import org.json4s.jackson.Serialization
import org.json4s.{DefaultFormats, Formats, _}
import software.amazon.awssdk.enhanced.dynamodb.Expression
import software.amazon.awssdk.services.dynamodb.model.{AttributeValue, QueryRequest, ReturnConsumedCapacity}

import java.util.concurrent.atomic.AtomicReference
import scala.collection.JavaConversions.collectionAsScalaIterable
import scala.collection.JavaConverters.mapAsJavaMapConverter
import scala.collection.immutable.ListMap
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

class RuleCodeDependencyResolver(ruleCodeDynamoClient: DynamoQueryClient,
                                 ruleCodeDynamoDbInfo: AtomicReference[RulecodeDynamoDbInfo],
                                 isDynamoFetchEnabled: Boolean, //Todo: Remove post control-center integration
                                 ruleCodeHttpPlugin: RuleCodeHttpPlugin,
                                 ruleCodeDataFileLookup: RulecodeDataFileLookup,
                                 tpAuditClient: TPAuditClient, clock: Clock,
                                 dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate,
                                 config: Config,
                                 thirdPartyAuditor: ThirdPartyAuditor[AuditInfo])(implicit ec: ExecutionContext) {
  val logger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(classOf[RuleCodeDependencyResolver])
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[RuleCodeDependencyResolver])
  private implicit val jsonFormats: Formats = DefaultFormats

  def constructKeysMap(requestJsonADT: JValue, keys: Seq[TableKeyDetails], preprocessedValues: Map[String, JValue])(implicit trxId: TrxId): ListMap[String, ValueAndCondition] = {

    try {
      keys.foldLeft(ListMap.empty[String, ValueAndCondition]) { (a, b) =>
        if (a.get(b.name).nonEmpty) {
          val newValue = a.get((b.name)).map(v => {
            val bValue = DbValue.toDBValueList(DbValue.toValue(DbValueUtility.jValueToDbValue(requestJsonADT, b.value, preprocessedValues)))
            val aValue = DbValue.toDBValueList(DbValue.toValue(v.value))
            DbValue.toDBValueList(List.concat(DbValue.toValue(aValue).asInstanceOf[List[DbValue]], DbValue.toValue(bValue).asInstanceOf[List[DbValue]]))
          }).get
          a ++ Map(b.name -> ValueAndCondition(newValue, b.condition, b.join))
        } else {
          val value = DbValueUtility.jValueToDbValue(requestJsonADT, b.value, preprocessedValues)
          a ++ Map(b.name -> ValueAndCondition(value, b.condition, b.join))
        }
      }
    } catch {
      case iae: IllegalArgumentException =>
        logger.error(s"IllegalArgumentException occured : ${iae.getMessage}")
        throw iae
    }
  }

  def processHttpDependency(requestJsonAST: JValue,
                            httpDefinition: JValue,
                            ruleDependency: RuleDependency)
                           (implicit trxId: TrxId): Future[Either[ErrorResponseMap, SuccessResponseMap]] = {
    try {
      val httpResponseFuture = ruleCodeHttpPlugin.lookup(requestJsonAST, httpDefinition).recover {
        case NonFatal(error) => Left(ErrorResponse(199, error.getMessage))
      }
      httpResponseFuture.flatMap {
        case Left(error: ErrorResponse) =>
          Future.successful(Left(ErrorResponseMap(Map(ruleDependency.toKey() -> error))))
        case Right(jValue: JValue) =>
          Future.successful(Right(SuccessResponseMap(Map(ruleDependency.toKey() -> jValue))))
      }
    } catch {
      case NonFatal(ex) =>
        logger.error("NonFatal Exception occured : ", ex)
        metrics.increment("rulecode.dependency.lookup.failure",
          "class:" + ex.getClass.getSimpleName, s"dependency:${ruleDependency.dependencyType}", s"dependency_name:${ruleDependency.name}")
        Future.successful(Left(ErrorResponseMap(Map(ruleDependency.toKey() ->
          ErrorResponse(500, s"NonFatal exception [ ${ex.getClass.getSimpleName} ] occured while performing http lookup : ${ex.getMessage}")))))
    }
  }

  def processTableDepForKYCTable(requestJsonAST: JValue,
                                 lookupToDynamoTableNameMap: Map[String, String],
                                 tableDefinitions: Seq[TableDefinition],
                                 ruleDependencies: Set[RuleDependency])
                                (implicit trxId: TrxId): Future[Either[ErrorResponseMap, SuccessResponseMap]] = {
    val startTime = clock.now()
    val dependencyTables = ruleDependencies.map(ruleDependency => {
      ruleDependency.dependencyType match {
        case "table" =>
          val tableDefinition = tableDefinitions.filter(_.name == ruleDependency.name).head
          val preprocessedValues = preOrPostProcessAll(requestJsonAST, tableDefinition.preprocessors)
          val keysMap: Map[String, String] = try {
            constructKeysMapForDynamo(requestJsonAST, tableDefinition.keys, preprocessedValues)
          } catch {
            case NonFatal(ex) =>
              RuleCodeDBAudit.audit(
                thirdPartyAuditor = thirdPartyAuditor,
                requestJsonAST = requestJsonAST,
                tableName = ruleDependency.name, //add table names separated by comma
                startTime = startTime.toDate,
                clock = clock,
                isError = true,
                response = s"Exception [${ex.getClass}] occured - ${ex.getMessage}")
              ex match {
                case _: DBQueryMissingFieldException if ruleDependency.optional =>
                  logger.debug(s"DBQueryMissingFieldException occurred while processing ${ruleDependency.toKey()}. Returning empty lookup since the dependency is optional.")
                  Map.empty[String, String]
                case dbqmfe: DBQueryMissingFieldException =>
                  //skip throwing stacktrace for known exceptions, just a warn message is enough for those.
                  val errorMessage = s"NonFatal exception [ ${dbqmfe.getClass.getSimpleName} ] occured while performing dblookup : ${dbqmfe.getMessage}"
                  logger.debug(s"DBQueryMissingFieldException occurred while processing ${ruleDependency.toKey()} - ${dbqmfe.getMessage}")
                  Map(s"DBQueryMissingFieldException-${ruleDependency.toKey()}" -> errorMessage)
                case _ =>
                  logger.debug("NonFatal Exception occured during constructing Key Map for Dynamo: ", ex)
                  metrics.increment("rulecode.dynamo.kyc.dependency.lookup.failure",
                    "class:" + ex.getClass.getSimpleName, s"dependency:${ruleDependency.dependencyType}", s"dependency_name:${ruleDependency.name}")
                  Map(s"DBQueryMissingFieldException-${ruleDependency.toKey()}" -> ex.getMessage)
              }
          }
          (tableDefinition, keysMap)
      }
    })


    val ruleDependencyTableDefMap = ruleDependencies.map(dependency => {
      dependency.dependencyType match {
        case "table" =>
          val tableDefinition = tableDefinitions.filter(_.name == dependency.name).head
          dependency -> tableDefinition
      }
    }).toMap

    val errorDeps = dependencyTables.filter(_._2.keySet.exists(_.startsWith("DBQueryMissingFieldException"))).toMap

    val errorResponseMap = ruleDependencyTableDefMap.map(ruleDependencyTableDef => {
      ruleDependencyTableDef._1.dependencyType match {
        case "table" =>
          val errorKey = s"DBQueryMissingFieldException-${ruleDependencyTableDef._1.toKey()}"
          errorDeps.filter(_._2.contains(errorKey)).map(errorDeps =>
            Map(ruleDependencyTableDef._1.toKey() ->
              ErrorResponse(500, errorDeps._2.getOrElse(errorKey, "DBQueryMissingFieldException-Unknown")))
          )

      }
    }).flatten.reduceLeftOption(_ ++ _).getOrElse(Map.empty[String, ErrorResponse])

    val tableListToParamMap = DynamoQueryFactory.getPKOnlyQueryParams(dependencyTables, lookupToDynamoTableNameMap)
    //preprocess the input
    try {
      ruleCodeDynamoClient.getBatchGetWithGSIForKYC(tableListToParamMap).flatMap {
        case Right(kycCompKeyBatchResult: SimpleKeyBatchResult) =>
          val successMap = ruleDependencyTableDefMap.flatMap {
            rdToTblDefn =>
              if (kycCompKeyBatchResult.dataPerTable.contains("table." + rdToTblDefn._2.name))
                kycCompKeyBatchResult.dataPerTable.get("table." + rdToTblDefn._2.name) match {
                  case Some(resultList) => resultList match {
                    case resultList: List[String] if resultList.nonEmpty => {
                      RuleCodeDBAudit.audit(thirdPartyAuditor = thirdPartyAuditor,
                        requestJsonAST = requestJsonAST,
                        tableName = rdToTblDefn._2.name,
                        startTime = startTime.toDate,
                        clock = clock,
                        isError = false,
                        response = resultList.mkString(","))

                      resultList.flatMap(s => processTableDependencyResponse(requestJsonAST, rdToTblDefn._2, rdToTblDefn._1, startTime, s))
                        .foldLeft(ListMap.empty[String, JValue]) { (a, b) =>
                          if (a.contains(b._1)) {
                            val newValue = a(b._1) merge b._2
                            a ++ Map(b._1 -> newValue)
                          } else {
                            a ++ Map(b._1 -> b._2)
                          }
                        }
                    }
                    case resultList: List[String] if resultList.isEmpty => {
                      RuleCodeDBAudit.audit(thirdPartyAuditor = thirdPartyAuditor,
                        requestJsonAST = requestJsonAST,
                        tableName = rdToTblDefn._2.name,
                        startTime = startTime.toDate,
                        clock = clock,
                        isError = false,
                        response = "")
                      Map.empty[String, JValue]
                    }
                    case _ =>
                      RuleCodeDBAudit.audit(thirdPartyAuditor = thirdPartyAuditor,
                        requestJsonAST = requestJsonAST,
                        tableName = rdToTblDefn._2.name,
                        startTime = startTime.toDate,
                        clock = clock,
                        isError = false,
                        response = "")
                      Map.empty[String, JValue]
                  }
                  case None => Map.empty[String, JValue]
                } else {
                Map.empty[String, JValue]
              }
          }

          val finalResponseMap = if (errorResponseMap.nonEmpty) {
            val errorMap = errorResponseMap.filter(_._2.message.nonEmpty).map(m => (s"ErrorResponse:${m._1}" -> JString(m._2.message)))
            successMap ++ errorMap
          } else successMap
          Future.successful(Right(SuccessResponseMap(finalResponseMap)))
        case Left(customErrorMessage: CustomErrorMessage) =>
          Future.successful(Left(ErrorResponseMap(Map("DynamoDB getCompositeKeyBatch Failure" -> ErrorResponse(199, customErrorMessage.errorMessage)))))
      }
    } catch {
      case NonFatal(ex) =>
        RuleCodeDBAudit.audit(
          thirdPartyAuditor = thirdPartyAuditor,
          requestJsonAST = requestJsonAST,
          tableName = "RC_DYNAMO_BATCH_LOOKUP_FAILURE", //add table names separated by comma
          startTime = startTime.toDate,
          clock = clock,
          isError = true,
          response = s"Exception [${ex.getClass}] occured - ${ex.getMessage}")
        logger.error("Dynamo lookup failed", ex.getMessage)
        metrics.increment("rulecode.dependency.dynamo.kyc.lookup.failure",
          "class:" + ex.getClass.getSimpleName)
        Future.successful(Left(ErrorResponseMap(Map("DynamoDBFetchFailure" ->
          ErrorResponse(500, s"NonFatal exception [ ${ex.getClass.getSimpleName} ] occured while performing dblookup : ${ex.getMessage}")))))
    }
  }

  def processTableDepForSimplePKTable(requestJsonAST: JValue,
                                      lookupToDynamoTableNameMap: Map[String, String],
                                      tableDefinitions: Seq[TableDefinition],
                                      ruleDependencies: Set[RuleDependency])
                                     (implicit trxId: TrxId): Future[Either[ErrorResponseMap, SuccessResponseMap]] = {

    val startTime = clock.now()
    val dependencyTables = ruleDependencies.map(ruleDependency => {
      ruleDependency.dependencyType match {
        case "table" =>
          val tableDefinition = tableDefinitions.filter(_.name == ruleDependency.name).head
          val preprocessedValues = preOrPostProcessAll(requestJsonAST, tableDefinition.preprocessors)
          val keysMap: Map[String, String] = try {
            constructKeysMapForDynamo(requestJsonAST, tableDefinition.keys, preprocessedValues)
          } catch {
            case NonFatal(ex) =>
              RuleCodeDBAudit.audit(
                thirdPartyAuditor = thirdPartyAuditor,
                requestJsonAST = requestJsonAST,
                tableName = ruleDependency.name, //add table names separated by comma
                startTime = startTime.toDate,
                clock = clock,
                isError = true,
                response = s"Exception [${ex.getClass}] occured - ${ex.getMessage}")
              ex match {
                case _: DBQueryMissingFieldException if ruleDependency.optional =>
                  logger.debug(s"DBQueryMissingFieldException occurred while processing ${ruleDependency.toKey()}. Returning empty lookup since the dependency is optional.")
                  Map.empty[String, String]
                case dbqmfe: DBQueryMissingFieldException =>
                  //skip throwing stacktrace for known exceptions, just a warn message is enough for those.
                  val errorMessage = s"NonFatal exception [ ${dbqmfe.getClass.getSimpleName} ] occured while performing dblookup : ${dbqmfe.getMessage}"
                  logger.debug(s"DBQueryMissingFieldException occurred while processing ${ruleDependency.toKey()} - ${dbqmfe.getMessage}")
                  Map(s"DBQueryMissingFieldException-${ruleDependency.toKey()}" -> errorMessage)
                case _ =>
                  logger.warn("NonFatal Exception occured during constructing Key Map for Dynamo: ", ex)
                  metrics.increment("rulecode.dynamo.dependency.lookup.failure",
                    "class:" + ex.getClass.getSimpleName, s"dependency:${ruleDependency.dependencyType}", s"dependency_name:${ruleDependency.name}")
                  Map(s"DBQueryMissingFieldException-${ruleDependency.toKey()}" -> ex.getMessage)
              }
          }
          (tableDefinition, keysMap)
      }
    })


    val ruleDependencyTableDefMap = ruleDependencies.map(dependency => {
      dependency.dependencyType match {
        case "table" =>
          val tableDefinition = tableDefinitions.filter(_.name == dependency.name).head
          dependency -> tableDefinition
      }
    }).toMap

    val errorDeps = dependencyTables.filter(_._2.keySet.exists(_.startsWith("DBQueryMissingFieldException"))).toMap

    val errorResponseMap = ruleDependencyTableDefMap.map(ruleDependencyTableDef => {
      ruleDependencyTableDef._1.dependencyType match {
        case "table" =>
          val errorKey = s"DBQueryMissingFieldException-${ruleDependencyTableDef._1.toKey()}"
          errorDeps.filter(_._2.contains(errorKey)).map(errorDeps =>
            Map(ruleDependencyTableDef._1.toKey() ->
              ErrorResponse(500, errorDeps._2.getOrElse(errorKey, "DBQueryMissingFieldException-Unknown")))
          )

      }
    }).flatten.reduceLeftOption(_ ++ _).getOrElse(Map.empty[String, ErrorResponse])

    val tableListToParamMap = DynamoQueryFactory.getPKOnlyQueryParams(dependencyTables, lookupToDynamoTableNameMap)
    //preprocess the input
    try {
      ruleCodeDynamoClient.getSimpleKeyBatch(tableListToParamMap, lookupToDynamoTableNameMap).flatMap {
        case Right(simpleKeyBatchResult: SimpleKeyBatchResult) =>
          val successMap = ruleDependencyTableDefMap.flatMap {
            rdToTblDefn =>
              if (simpleKeyBatchResult.dataPerTable.contains("table." + rdToTblDefn._2.name))
                simpleKeyBatchResult.dataPerTable.get("table." + rdToTblDefn._2.name) match {
                  case Some(resultList) => resultList match {
                    case resultList: List[String] if resultList.nonEmpty => {
                      RuleCodeDBAudit.audit(thirdPartyAuditor = thirdPartyAuditor,
                        requestJsonAST = requestJsonAST,
                        tableName = rdToTblDefn._2.name,
                        startTime = startTime.toDate,
                        clock = clock,
                        isError = false,
                        response = resultList.mkString(","))

                      resultList.flatMap(s => processTableDependencyResponse(requestJsonAST, rdToTblDefn._2, rdToTblDefn._1, startTime, s))
                        .foldLeft(ListMap.empty[String, JValue]) { (a, b) =>
                          if (a.contains(b._1)) {
                            val newValue = a(b._1) merge b._2
                            a ++ Map(b._1 -> newValue)
                          } else {
                            a ++ Map(b._1 -> b._2)
                          }
                        }
                    }
                    case resultList: List[String] if resultList.isEmpty => {
                      RuleCodeDBAudit.audit(thirdPartyAuditor = thirdPartyAuditor,
                        requestJsonAST = requestJsonAST,
                        tableName = rdToTblDefn._2.name,
                        startTime = startTime.toDate,
                        clock = clock,
                        isError = false,
                        response = "")
                      Map.empty[String, JValue]
                    }
                    case _ =>
                      RuleCodeDBAudit.audit(thirdPartyAuditor = thirdPartyAuditor,
                        requestJsonAST = requestJsonAST,
                        tableName = rdToTblDefn._2.name,
                        startTime = startTime.toDate,
                        clock = clock,
                        isError = false,
                        response = "")
                      Map.empty[String, JValue]
                  }
                  case None => Map.empty[String, JValue]
                } else {
                Map.empty[String, JValue]
              }
          }

          val finalResponseMap = if (errorResponseMap.nonEmpty) {
            val errorMap = errorResponseMap.filter(_._2.message.nonEmpty).map(m => (s"ErrorResponse:${m._1}" -> JString(m._2.message)))
            successMap ++ errorMap
          } else successMap
          Future.successful(Right(SuccessResponseMap(finalResponseMap)))
        case Left(customErrorMessage: CustomErrorMessage) =>
          Future.successful(Left(ErrorResponseMap(Map("DynamoDB getSimpleKeyBatch Failure" -> ErrorResponse(199, customErrorMessage.errorMessage)))))
      }
    } catch {
      case NonFatal(ex) =>
        RuleCodeDBAudit.audit(
          thirdPartyAuditor = thirdPartyAuditor,
          requestJsonAST = requestJsonAST,
          tableName = "RC_DYNAMO_BATCH_LOOKUP_FAILURE", //add table names separated by comma
          startTime = startTime.toDate,
          clock = clock,
          isError = true,
          response = s"Exception [${ex.getClass}] occured - ${ex.getMessage}")
        logger.error("Dynamo lookup failed", ex.getMessage)
        metrics.increment("rulecode.dependency.dynamo.lookup.failure",
          "class:" + ex.getClass.getSimpleName)
        Future.successful(Left(ErrorResponseMap(Map("DynamoDBFetchFailure" ->
          ErrorResponse(500, s"NonFatal exception [ ${ex.getClass.getSimpleName} ] occured while performing dblookup : ${ex.getMessage}")))))
    }
  }


  def processTableDepForSortKeyTable(requestJsonAST: JValue,
                                     lookupToDynamoTableNameMap: Map[String, String],
                                     tableDefinitions: Seq[TableDefinition],
                                     ruleDependencies: Set[RuleDependency])
                                    (implicit trxId: TrxId): Future[Dependencies] = {

    val startTime = clock.now()
    Future.sequence(ruleDependencies.filter(ruledependency => ruledependency.dependencyType.equalsIgnoreCase("table")
      && lookupToDynamoTableNameMap.contains(ruledependency.name)
      && tableDefinitions.exists(tableDef => tableDef.name.equalsIgnoreCase(ruledependency.name))).map(ruleDependency => {
      val tableDefinition = tableDefinitions.filter(_.name == ruleDependency.name).head
      try {
        val preprocessedValues = preOrPostProcessAll(requestJsonAST, tableDefinition.preprocessors)
        val keysMap =
          constructKeysMap(requestJsonAST, tableDefinition.keys, preprocessedValues)
        //construct the PK and SK
        val partKeys = tableDefinition.keys.filter(key => KeyType.PartitionKey.equalsIgnoreCase(key.keyType))
        val sortKeys = tableDefinition.keys.filter(key => KeyType.SortKey.equalsIgnoreCase(key.keyType))
        val filterKeys = tableDefinition.keys.filter(key => KeyType.FilterKey.equalsIgnoreCase(key.keyType))
        val partKeyVals = partKeys.map(pk => DbValue.toValue(keysMap.get(pk.name).get.value).toString)
        val partKeyName = partKeys.head.alias
        val partKeyReducedVal = if (partKeyVals.size > 1) partKeyVals.mkString("|") else partKeyVals.head

        //filter out the between
        val betweenParamsMap = (sortKeys ++ filterKeys).filter(key => key.condition.equalsIgnoreCase("between"))
          .map(betweenSortKey => {
            val value = keysMap.get(betweenSortKey.name).get.value
            value match {
              case DbValueStringList(list) =>
                Map(s":${betweenSortKey.name}_1" -> AttributeValue.builder.s(list(0)).build(),
                  s":${betweenSortKey.name}_2" -> AttributeValue.builder.s(list(1)).build())
            }
          }).flatten
        val nonBetweenParamsKeys = (sortKeys ++ filterKeys).filterNot(key => key.condition.equalsIgnoreCase("between"))

        val paramMap = (Map(s":$partKeyName" -> AttributeValue.builder.s(partKeyReducedVal).build())
          ++ betweenParamsMap ++
          nonBetweenParamsKeys.map(key => {
            val value = keysMap.get(key.name).get.value
            val attrValue = DbValue.toAttributeValueBuilder(value) match {
              case DbValue.DYNAMO_ATTR_TYPE_S => AttributeValue.builder.s(DbValue.toValue(value).toString).build()
              case DbValue.DYNAMO_ATTR_TYPE_N => AttributeValue.builder.n(DbValue.toValue(value).toString).build()
              case DbValue.DYNAMO_ATTR_TYPE_BOOL => AttributeValue.builder.bool(DbValue.toValue(value).toString.toBoolean).build()
              case _ => AttributeValue.builder.s(DbValue.toValue(value).toString).build()
            }
            s":${key.name}" -> attrValue
          })).asJava

        val sortKeyExpression = sortKeys.map(sk => {
          sk.condition match {
            case "BETWEEN" | "between" => s"${sk.alias} ${sk.condition} :${sk.name}_1 AND :${sk.name}_2"
            case _ => s"${sk.alias} ${sk.condition} :${sk.name}"
          }
        }).mkString(" AND ")
        val keyConditionExpression = s"$partKeyName = :$partKeyName AND " + sortKeyExpression

        val scanIndexForward = tableDefinition.orderBy.isDefined && tableDefinition.orderBy.get.equals("asc")
        val inputAsOfDate = if (paramMap.containsKey(":asOfDate")) Some(paramMap.get(":asOfDate").n().toLong) else None

        val baseRequest = QueryRequest.builder
          .tableName(lookupToDynamoTableNameMap.get(tableDefinition.name).get)
          .keyConditionExpression(keyConditionExpression)
          .expressionAttributeValues(paramMap)
          .returnConsumedCapacity(ReturnConsumedCapacity.TOTAL)
          .scanIndexForward(scanIndexForward)

        if (filterKeys.nonEmpty) {
          val filterExpression = filterKeys.map(filterKey => {
            filterKey.condition match {
              case "BETWEEN" | "between" => s"${filterKey.alias} ${filterKey.condition} :${filterKey.name}_1 AND :${filterKey.name}_2"
              case _ => s"${filterKey.alias} ${filterKey.condition} :${filterKey.name}"
            }
          }).mkString(" AND ")

          baseRequest.filterExpression(Expression.builder()
            .expression(filterExpression)
            .build().expression())
        }

        val queryRequest = if (tableDefinition.limit.isDefined) baseRequest.limit(tableDefinition.limit.get).build()
        else baseRequest.build()
        ruleCodeDynamoClient.querySortKeyTable(queryRequest, tableDefinition, inputAsOfDate).flatMap(res => res match {
          case Right(simpleBatchResult) => {
            val resultMap = if (simpleBatchResult._1.nonEmpty) {
              val auditString = if (simpleBatchResult._2 != null)
                List(simpleBatchResult._1.get, s""""asOfDate":${inputAsOfDate.get}""").mkString(",")
              else simpleBatchResult._1.get
              RuleCodeDBAudit.audit(thirdPartyAuditor = thirdPartyAuditor,
                requestJsonAST = requestJsonAST,
                tableName = tableDefinition.name,
                startTime = startTime.toDate,
                clock = clock,
                isError = false,
                response = auditString)
              SuccessResponseMap(processTableDependencyResponse(requestJsonAST, tableDefinition, ruleDependency, startTime, simpleBatchResult._1.get))
            } else {
              RuleCodeDBAudit.audit(thirdPartyAuditor = thirdPartyAuditor,
                requestJsonAST = requestJsonAST,
                tableName = tableDefinition.name,
                startTime = startTime.toDate,
                clock = clock,
                isError = false,
                response = "")
              SuccessResponseMap(Map.empty[String, JValue])
            }
            Future.successful(Right(resultMap))
          }
          case Left(customErrorMessage) =>
            RuleCodeDBAudit.audit(
              thirdPartyAuditor = thirdPartyAuditor,
              requestJsonAST = requestJsonAST,
              tableName = tableDefinition.name,
              startTime = startTime.toDate,
              clock = clock,
              isError = true,
              response = s"Error occured - ${customErrorMessage.errorMessage}")
            Future.successful(Left(ErrorResponseMap(Map(ruleDependency.toKey() -> ErrorResponse(199, customErrorMessage.errorMessage)))))
        })
      } catch {
        case NonFatal(ex) =>
          RuleCodeDBAudit.audit(
            thirdPartyAuditor = thirdPartyAuditor,
            requestJsonAST = requestJsonAST,
            tableName = tableDefinition.name,
            startTime = startTime.toDate,
            clock = clock,
            isError = true,
            response = s"Exception [${ex.getClass}] occured - ${ex.getMessage}")
          Future.successful(Left(ErrorResponseMap(Map(ruleDependency.toKey() -> ErrorResponse(199, ex.getMessage)))))
      }

    })).flatMap(
      response => {
        val resultTuple = response.foldLeft((Map.empty[String, ErrorResponse], Map.empty[String, JValue])) { case ((errorMap, successMap), current) =>
          current match {
            case Left(e: ErrorResponseMap) =>
              (errorMap ++ e.map, successMap)
            case Right(s: SuccessResponseMap) =>
              (errorMap, successMap ++ s.map)
          }
        }
        val emptyDeps = ruleDependencies.flatMap { dep =>
          if (!resultTuple._1.contains(dep.toKey()) && !resultTuple._2.contains(dep.toKey())) {
            Some(dep.toKey())
          } else Option.empty[String]
        }
        Future.successful(Dependencies(resultTuple._1, resultTuple._2, emptyDeps))
      })
  }

  private def processTableDependencyResponse(requestJsonAST: JValue, tableDefinition: TableDefinition, ruleDependency: RuleDependency, startTime: DateTime, s: String)(implicit trxId: TrxId) = {
    val dbJsonAST = Try(parse(s)) match {
      case Success(v) => v
      case Failure(f) => JString(s)
    }

    val bestMatchedNode = Json4sUtility.getBestMatchedNode(requestJsonAST, tableDefinition.bestIdentity, dbJsonAST)
    tableDefinition.bestIdentity match {
      case Some(_) =>
        bestMatchedNode.bestNode match {
          case JNothing =>
            logger.warn(s"Unable to find best matched index for table ${ruleDependency.toKey()}")
            metrics.increment("rulecode.invalid.best.matched.index", "table : " + ruleDependency.toKey())
          case _ =>
            logger.info(s"Best matched index for ${tableDefinition.name} is ${bestMatchedNode.bestIndex}")
        }
      case _ =>
    }
    val bestMatchedNodeKey = ruleDependency.toKey() + "_bestNode"

    val bestMatchResolveResults = tableDefinition.bestMatchMethods.flatMap { method =>
      BestMatchResolverLookupUtility.getResolver(method).resolve(requestJsonAST, dbJsonAST) match {
        case JNothing =>
          logger.debug(s"Unable to resolve best match for table ${ruleDependency.toKey()} and method $method")
          None
        case JArray(jArr) if jArr.nonEmpty => Some(s"${ruleDependency.toKey()}.${RulecodeServiceConstants.BestMatchMethodPrefix}$method" -> jArr.head)
        case result: JValue => Some(s"${ruleDependency.toKey()}.${RulecodeServiceConstants.BestMatchMethodPrefix}$method" -> result)
        case _ => None
      }
    }.toMap
    Map(
      ruleDependency.toKey() -> dbJsonAST,
      bestMatchedNodeKey -> bestMatchedNode.bestNode
    ) ++ bestMatchResolveResults
  }

  def processFileDependency(requestJsonAST: JValue,
                            fileDefinition: FileDefinition,
                            ruleDependency: RuleDependency): Future[Either[ErrorResponseMap, SuccessResponseMap]] = {
    val startTime = clock.now()
    val jVal = Json4sUtility.parseJValue(fileDefinition.keyPath, requestJsonAST, Map.empty)
    val lookupData = jVal match {
      case JString(value) =>
        val lookupResponse = ruleCodeDataFileLookup.get(fileDefinition.name, value)
        lookupResponse match {
          case Some(data) =>
            RuleCodeFileAudit.audit(thirdPartyAuditor = thirdPartyAuditor,
              requestJsonAST = requestJsonAST,
              tableName = fileDefinition.name,
              startTime = startTime.toDate,
              clock = clock,
              isError = false,
              response = Serialization.write(data))
            data
          case _ => JNothing
        }
      case _ => JNothing
    }
    val result = Right(SuccessResponseMap(
      Map(ruleDependency.toKey() -> lookupData)
    ))
    Future.successful(result)
  }

  private def preOrPostProcessAll(requestJsonAst: JValue, preprocessors: Seq[RuleCodeOperatorProcessor]): Map[String, JValue] = {
    val processorOutputMap = scala.collection.mutable.Map.empty[String, JValue]
    preprocessors.map(processor => {
      val processedJVal = preOrPostProcess(requestJsonAst, processor, processorOutputMap.toMap)
      val outputKey = processor.output
      processorOutputMap += (outputKey -> processedJVal)
      processorOutputMap
    }).toList.flatten.toMap
  }

  private def preOrPostProcess(requestJsonAst: JValue, processor: RuleCodeOperatorProcessor, processorOutputMap: Map[String, JValue] = Map.empty): JValue = {
    val dependencyInput = processor.inputs.map(Json4sUtility.parseJValue(_, requestJsonAst, Map.empty, processorOutputMap)).toList
    RuleCodeGeneratorV2.preOrPostProcess("", requestJsonAst, Map.empty, processor, processorOutputMap, dependencyInput)("<empty>")
  }

  def constructKeysMapForDynamo(requestJsonADT: JValue, keys: Seq[TableKeyDetails], preprocessedValues: Map[String, JValue])(implicit trxId: TrxId): ListMap[String, String] = {
    try {
      keys.foldLeft(ListMap.empty[String, String]) { (a, b) =>
        val value = DbValueUtility.jValueToDbValue(requestJsonADT, b.value, preprocessedValues)
        if (value.isInstanceOf[DbValueStringList])
          a ++ Map(b.name -> DbValue.toValue(value).asInstanceOf[List[String]].mkString(","))
        else
          a ++ Map(b.name -> DbValue.toValue(value).toString)
      }
    } catch {
      case iae: IllegalArgumentException =>
        logger.error(s"IllegalArgumentException occured : ${iae.getMessage}")
        throw iae
    }
  }

  def getDynamoAndRDSData(requestJsonAST: JValue,
                          tableDefinitions: Seq[TableDefinition],
                          httpDefinitions: Seq[JValue],
                          fileDefinitions: Seq[FileDefinition],
                          ruleDependencies: Set[RuleDependency],
                          checkForUnreleasedTables: Boolean,
                          unreleasedRDSTables: List[String]
                         )(implicit trxId: TrxId): Future[Dependencies] = {
    implicit val jsonFormats: Formats = DefaultFormats
    val dynamoDbEnv = config.getString("dynamodb.env")
    val lookupToDynamoTableNameMap = ruleCodeDynamoDbInfo.get().config.valid_lookup_names

    val kycRuleDependencies = ruleDependencies.filter(r => r.name.startsWith("asl_vendor_lookup") || r.name.startsWith("attom_vendor_lookup"))
    val neustarRuleDependencies = ruleDependencies.filter(r => r.name.startsWith("neustar_ipv4_geolocation_lookup"))
    val sortKeyDependencis = ruleDependencies.filter(ruleDependency => tableDefinitions
      .filter(tableDefinition => tableDefinition.keys.filter(key => KeyType.SortKey.equalsIgnoreCase(key.keyType)).nonEmpty)
      .map(td => td.name) contains (ruleDependency.name))
    val combinationsRuleDependencies = ruleDependencies.filter(r => r.name.startsWith("sv4"))
    val fraudRuleDependencies = ruleDependencies.filterNot(kycRuleDependencies).filterNot(neustarRuleDependencies).filterNot(sortKeyDependencis).filterNot(combinationsRuleDependencies)

    val kycDeps = if (lookupToDynamoTableNameMap.nonEmpty) {
      val validRuleDependencies = kycRuleDependencies.filter(ruleDep => lookupToDynamoTableNameMap.contains(ruleDep.name))
      val validDynamoLookup = tableDefinitions.filter(defn => lookupToDynamoTableNameMap.contains(defn.name))
      if (validRuleDependencies.nonEmpty) {
        processTableDepForKYCTable(requestJsonAST, lookupToDynamoTableNameMap, validDynamoLookup, validRuleDependencies)
      } else {
        Future.successful(Left(ErrorResponseMap(Map.empty[String, ErrorResponse])))
      }
    } else {
      Future.successful(Left(ErrorResponseMap(Map.empty[String, ErrorResponse])))
    }

    val dynamoDeps = if (lookupToDynamoTableNameMap.nonEmpty) {
      val validRuleDependencies = fraudRuleDependencies.filter(ruleDep => lookupToDynamoTableNameMap.contains(ruleDep.name))
      val validDynamoLookup = tableDefinitions.filter(defn => lookupToDynamoTableNameMap.contains(defn.name))
      if (validRuleDependencies.nonEmpty) {
        processTableDepForSimplePKTable(requestJsonAST, lookupToDynamoTableNameMap, validDynamoLookup, validRuleDependencies)
      } else {
        Future.successful(Left(ErrorResponseMap(Map.empty[String, ErrorResponse])))
      }
    } else {
      Future.successful(Left(ErrorResponseMap(Map.empty[String, ErrorResponse])))
    }

    val combinationsDeps = if (lookupToDynamoTableNameMap.nonEmpty) {
      val validRuleDependencies = combinationsRuleDependencies.filter(ruleDep => lookupToDynamoTableNameMap.contains(ruleDep.name))
      val validDynamoLookup = tableDefinitions.filter(defn => lookupToDynamoTableNameMap.contains(defn.name))
      if (validRuleDependencies.nonEmpty) {
        processTableDepForSimplePKTable(requestJsonAST, lookupToDynamoTableNameMap, validDynamoLookup, validRuleDependencies)
      } else {
        Future.successful(Left(ErrorResponseMap(Map.empty[String, ErrorResponse])))
      }
    } else {
      Future.successful(Left(ErrorResponseMap(Map.empty[String, ErrorResponse])))
    }

    val neustarDeps = if (lookupToDynamoTableNameMap.nonEmpty) {
      val validRuleDependencies = neustarRuleDependencies.filter(ruleDep => lookupToDynamoTableNameMap.contains(ruleDep.name))
      val validDynamoLookup = tableDefinitions.filter(defn => lookupToDynamoTableNameMap.contains(defn.name))
      if (validRuleDependencies.nonEmpty) {
        processTableDepForSimplePKTable(requestJsonAST, lookupToDynamoTableNameMap, validDynamoLookup, validRuleDependencies)
      } else {
        Future.successful(Left(ErrorResponseMap(Map.empty[String, ErrorResponse])))
      }
    } else {
      Future.successful(Left(ErrorResponseMap(Map.empty[String, ErrorResponse])))
    }
    val rds_RuleDependencies = if (isDynamoFetchEnabled) ruleDependencies.filterNot(ruleDep => lookupToDynamoTableNameMap.contains(ruleDep.name)) else ruleDependencies
    Future.sequence((rds_RuleDependencies.map(dependency => {
      dependency.dependencyType match {
        case "http" =>
          val result = httpDefinitions.filter { jValue: JValue =>
            (jValue \ "name").extractOpt[String] match {
              case Some(name) => name.equalsIgnoreCase(dependency.name)
              case _ => false
            }
          }
          result match {
            case Seq() => Future.successful(Right(SuccessResponseMap(Map.empty[String, JValue])))
            case _ => processHttpDependency(requestJsonAST, result.head, dependency)
          }
        case "file" =>
          val result = fileDefinitions.filter(_.name == dependency.name)
          result match {
            case Seq() => Future.successful(Right(SuccessResponseMap(Map.empty[String, JValue])))
            case _ => processFileDependency(requestJsonAST, result.head, dependency)
          }
        case _ =>
          logger.error(s"Unsupported dependency type : ${dependency.dependencyType}")
          Future.successful(Right(SuccessResponseMap(Map.empty[String, JValue])))
      }
    })) ++ Set(dynamoDeps, kycDeps, neustarDeps, combinationsDeps)).flatMap(
      response => {
        val resultTuple = response.foldLeft((Map.empty[String, ErrorResponse], Map.empty[String, JValue])) { case ((errorMap, successMap), current) =>
          current match {
            case Left(e: ErrorResponseMap) =>
              (errorMap ++ e.map, successMap)
            case Right(s: SuccessResponseMap) =>
              val dynamoErrorMap = s.map
                .filterKeys(_.startsWith("ErrorResponse:"))
                .map { e =>
                  val key = e._1.split(":")(1)
                  key -> ErrorResponse(500, e._2.extract[String])
                }
              val filteredSuccessMap = s.map.filterKeys(!_.startsWith("ErrorResponse:"))
              (errorMap ++ dynamoErrorMap, successMap ++ filteredSuccessMap)
          }
        }
        val emptyDeps = ruleDependencies.flatMap { dep =>
          if (!resultTuple._1.contains(dep.toKey()) && !resultTuple._2.contains(dep.toKey())) {
            Some(dep.toKey())
          } else Option.empty[String]
        }
        val sortKeyDepsFut = processTableDepForSortKeyTable(requestJsonAST, lookupToDynamoTableNameMap, tableDefinitions
          .filter(tableDef => tableDef.keys.filter(key => KeyType.SortKey.equalsIgnoreCase(key.keyType)).nonEmpty), ruleDependencies)
        sortKeyDepsFut.flatMap(sortKeyDeps => Future.successful(Dependencies(resultTuple._1 ++ sortKeyDeps.errorDependencies,
          resultTuple._2 ++ sortKeyDeps.successDependencies, emptyDeps ++ sortKeyDeps.emptyDependencies)))
      })
  }

  def resolve(requestJsonAST: JValue,
              tableDefinitions: Seq[TableDefinition],
              httpDefinitions: Seq[JValue],
              fileDefinitions: Seq[FileDefinition],
              ruleDependencies: Set[RuleDependency])(implicit trxId: TrxId): Future[Dependencies] = {

    val dynamoConfig = config.getConfig("dynamodb")
    val allowDynamoToggle = dynamoConfig.getBoolean("allowDynamoToggle")
    val unreleasedTables = dynamoConfig.getStringList("unreleasedTables").toList

    if (allowDynamoToggle) {
      dynamicControlCenterV2Evaluate.evaluate("RulecodeService", "enableDynamoDataFetch").flatMap(
        flagStatus => flagStatus match {
          case Right(evaluateResponse: EvaluateResponse) =>
            if (evaluateResponse.isFlagActive == true) {
              getDynamoAndRDSData(requestJsonAST, tableDefinitions, httpDefinitions, fileDefinitions, ruleDependencies, false, List.empty[String])
            } else {
              getDynamoAndRDSData(requestJsonAST, tableDefinitions, httpDefinitions, fileDefinitions, ruleDependencies, true, unreleasedTables)
            }
          case Left(errorResponse: ErrorResponse) =>
            logger.info("Dynamo : DCC returned error on evaluate enableDynamoDataFetch ", errorResponse.message)
            if (isDynamoFetchEnabled) {
              getDynamoAndRDSData(requestJsonAST, tableDefinitions, httpDefinitions, fileDefinitions, ruleDependencies, false, List.empty[String])
            } else {
              getDynamoAndRDSData(requestJsonAST, tableDefinitions, httpDefinitions, fileDefinitions, ruleDependencies, false, unreleasedTables)
            }
        }
      )
    } else {
      //For any issuess, use rollback of table
      getDynamoAndRDSData(requestJsonAST, tableDefinitions, httpDefinitions, fileDefinitions, ruleDependencies, false, List.empty[String])
    }
  }
}