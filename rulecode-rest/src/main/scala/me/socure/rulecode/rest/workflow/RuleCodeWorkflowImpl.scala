package me.socure.rulecode.rest.workflow

import com.google.inject.Inject
import com.typesafe.config.Config
import dispatch.{Http, Req, url}
import io.netty.handler.ssl.SslContextBuilder
import me.socure.common.clock.Clock
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import me.socure.rulecode.common.RuleCodeWorkflow
import me.socure.rulecode.common.models._
import me.socure.rulecode.common.utilities.{Json4sUtility, RulecodeServiceConstants}
import me.socure.rulecode.database.DatabasesHandler
import me.socure.rulecode.filedata.RulecodeDataFileLookup
import me.socure.rulecode.http.plugin.audit.RuleCodeHttpAudit
import me.socure.rulecode.operators.derived._
import me.socure.rulecode.operators.{<PERSON><PERSON>, RuleCodeOperators}
import me.socure.rulecode.rest.model.{Dependencies, RcHelperRequest, RcHelperResponseParser}
import me.socure.rulecode.rest.service.RuleCodeGenerationService
import me.socure.service.audit.client.TPAuditClient
import me.socure.service.audit.model.AuditInfo
import me.socure.thirdparty.auditing.client.ThirdPartyAuditor
import org.asynchttpclient.Response
import org.json4s.JsonAST._
import org.json4s.jackson.JsonMethods.parse
import org.json4s.native.Serialization
import org.json4s.{DefaultFormats, Formats}

import java.nio.charset.Charset
import java.nio.file.Paths
import java.util.concurrent.atomic.AtomicReference
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

class RuleCodeWorkflowImpl @Inject()(ruleCodeConfiguration: AtomicReference[RuleCodeConfiguration],
                                     ruleCodeConfigurationV2: AtomicReference[RuleCodeConfigurationV2],
                                     ruleCodeDependencyResolver: RuleCodeDependencyResolver,
                                     ruleCodeInputResolver: RuleCodeInputResolver[JValue],
                                     ruleCodeGeneratorV2: RuleCodeGenerationService[RuleCodeConfigurationV2],
                                     databaseHandler: DatabasesHandler,
                                     ruleCodeDataFileLookup: RulecodeDataFileLookup,
                                     config: Config,
                                     tpAuditClient: TPAuditClient,
                                     clock: Clock,
                                     thirdPartyAuditor: ThirdPartyAuditor[AuditInfo]
                                    )
                                    (implicit ec: ExecutionContext) extends RuleCodeWorkflow {

  private implicit def jsonFormats: Formats = DefaultFormats

  private val logger = TransactionAwareLoggerFactory.getLogger(classOf[RuleCodeWorkflowImpl])
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[RuleCodeWorkflowImpl])
  private val LOOKUP_MATCH_RULECODE_SUFFIX = "100000"

  val supportedOperators: Set[Operator] = RuleCodeOperators.getAllOperators

  val derivedRuleCodesResolver: Set[DerivedRuleCodesResolver] = DerivedRuleCodesResolvers.getAllResolvers

  private val http: Http = Http.withConfiguration {
    conf =>
      conf.setSslContext(
          SslContextBuilder
            .forClient()
            .build()
        )
        .setRequestTimeout(config.getInt("rcHelper.timeout"))
        .setReadTimeout(config.getInt("rcHelper.timeout"))
  }

  private val rcHelperVendors = config.getStringList("rcHelper.allowedVendors")

  def formatErrorString(item: String, errorMessage: String): String = s"Skipped ${item}. Reason - ${errorMessage}"

  def processRule(operator: Operator,
                  inputs: List[JValue],
                  ruleCode: String,
                  config: JValue,
                  vendor: Option[String]
                 )(implicit trxId: TrxId): Future[Either[ErrorResponse, Either[Option[NumericalRuleCode], Option[CategoricalRuleCode]]]] = {
    try {
      operator.process(inputs, ruleCode, config)
    } catch {
      case NonFatal(ex) => {
        logger.error(s"NonFatal Exception occurred while processing rulecode ${ruleCode} : ", ex)
        Future.successful(Left(ErrorResponse(400,
          formatErrorString(ruleCode, s"Exception message : ${ex.getClass} - ${ex.getMessage}"))))
      }
    }
  }

  def getRuleCodeDefinitionsByVendor(ruleCodeDefinitions: Seq[RuleCodeDefinition], vendor: Option[String]): Seq[RuleCodeDefinition] = {
    vendor match {
      case Some(v: String) =>
        ruleCodeDefinitions.filter(ruleCodeDefinition => (ruleCodeDefinition.rules.filter(rule => rule.ruleCodeName.startsWith(v)).nonEmpty))
      case None => ruleCodeDefinitions
    }
  }

  def getFilteredRuleCodes(ruleCodeDefinitions: Seq[RuleCodeDefinition],
                           successDependencies: Map[String, JValue],
                           errorDependencies: Map[String, ErrorResponse],
                           vendor: Option[String])
                          (implicit trxId: TrxId): (Seq[RuleOperator], JValue) = {

    //Removing rulecode definitions whose dependencies got errored out.
    val filteredRuleCodeDefinitions = if (errorDependencies.nonEmpty) {
      ruleCodeDefinitions.filterNot { ruleCodeDefinition =>
        val primaryDependencies = ruleCodeDefinition.dependencies.filter(dep => dep.primaryLookup)
        primaryDependencies.size == primaryDependencies.filter { ruleDependency =>
          errorDependencies.contains(ruleDependency.toKey())
        }.size
      }
    } else {
      ruleCodeDefinitions
    }

    if (filteredRuleCodeDefinitions.size != ruleCodeDefinitions.size) {
      logger.debug(s"Found ${filteredRuleCodeDefinitions.size} rulecode definitions after filtering out " +
        s"errored dependencies for vendor ${vendor.getOrElse("<empty>")}")
    }

    val vendorConfigJValue = if (filteredRuleCodeDefinitions.nonEmpty && filteredRuleCodeDefinitions.size == 1) {
      vendor match {
        case Some(_) => filteredRuleCodeDefinitions.head.config
        case None => JNothing
      }
    } else {
      JNothing
    }

    val filteredRules = filteredRuleCodeDefinitions.flatMap(_.rules)

    //Removing rulecode definitions for which there is no lookup match.
    //successDependencies would have values only when there is lookup match.
    val ruleCodeDefinitionsWithoutLookupData = filteredRuleCodeDefinitions.filter { ruleCodeDefinition =>
      ruleCodeDefinition.dependencies.filter(ruleDependency => successDependencies.contains(ruleDependency.toKey()) && ruleDependency.primaryLookup).isEmpty
    }

    if (ruleCodeDefinitionsWithoutLookupData.isEmpty) {
      (filteredRules, vendorConfigJValue)
    } else {
      logger.debug(s"Found ${ruleCodeDefinitionsWithoutLookupData.size} rulecode definitions without lookup data for vendor ${vendor.getOrElse("<empty>")}")
      val vendorsWithoutLookupData = ruleCodeDefinitionsWithoutLookupData.flatMap(_.rules).
        map(_.ruleCodeName.split("\\.").head).toSet
      logger.debug(s"Vendors without lookup data : ${vendorsWithoutLookupData}")

      val rules = filteredRules.filter { rule =>
        vendorsWithoutLookupData.contains(rule.ruleCodeName.split("\\.").head) && rule.ruleCodeName.endsWith(LOOKUP_MATCH_RULECODE_SUFFIX) ||
          (!vendorsWithoutLookupData.contains(rule.ruleCodeName.split("\\.").head))
      } ++ filteredRules.filter(_.computeOnEmptyLookup)
      (rules.distinct, vendorConfigJValue)
    }
  }

  def getDerivedRuleCodes(vendorOpt: Option[String],
                          requestJsonAST: JValue,
                          dbJsonMap: Map[String, JValue],
                          errorResponseMap: Map[String, ErrorResponse],
                          ruleCodeResponse: RuleCodeResponse
                         )(implicit trxId: TrxId): RuleCodeResponse = {
    vendorOpt match {
      case Some(vendor) =>
        val resolvers = derivedRuleCodesResolver.filter(r => r.vendor.equalsIgnoreCase(vendor))
        if (resolvers.isEmpty) {
          ruleCodeResponse
        } else if (resolvers.size == 1) {
          resolvers.head.generate(ruleCodeResponse, requestJsonAST, dbJsonMap, errorResponseMap, ruleCodeDataFileLookup)
        } else {
          throw new IllegalStateException(s"Multiple derived rulecode resolvers found for vendor ${vendor} : ${resolvers.size}")
        }
      case None => ruleCodeResponse
    }
  }

  override def generateRuleCodes(requestJsonAST: JValue): Future[Either[ErrorResponse, RuleCodeResponse]] = {
    val transactionId = Json4sUtility.getOptionalString(requestJsonAST, "transactionId")
    val accountId = Json4sUtility.getOptionalLong(requestJsonAST, "accountId")
    if (transactionId.isEmpty || accountId.isEmpty) {
      if (transactionId.isEmpty) {
        Future.successful(Left(ErrorResponse(400, "Bad Request - Invalid transactionId")))
      } else {
        Future.successful(Left(ErrorResponse(400, "Bad Request - Invalid accountId")))
      }
    } else {
      implicit val trxId: TrxId = TrxId(transactionId.get)
      try {
        val vendor = (requestJsonAST \ "vendor").extractOpt[String]

        val tableDefinitions = ruleCodeConfiguration.get.tableDefinitions
        val httpDefinitions = ruleCodeConfiguration.get.httpDefinitions
        val fileDefinitions = Seq.empty
        val ruleCodeDefinitions = getRuleCodeDefinitionsByVendor(ruleCodeConfiguration.get.ruleCodeDefinitions, vendor)
        val derivedRulecodes = vendor match {
          case Some(v) => derivedRuleCodesResolver.filter(r => r.vendor.equalsIgnoreCase(v))
          case None => Set.empty
        }
        if (ruleCodeDefinitions.isEmpty && derivedRulecodes.isEmpty) {
          val v = Vendor(vendor.getOrElse(""))
          if (!ruleCodeConfigurationV2.get().ruleCodeCollections.contains(v)) {
            throw new Exception(s"No matching rulecode definitions found for given vendor $vendor")
          }
        }
        val rules = ruleCodeDefinitions.flatMap(_.rules)
        val dependenciesSet = ruleCodeDefinitions.flatMap(_.dependencies).toSet

        logger.debug(s"Found ${ruleCodeDefinitions.size} rulecode definitions, " +
          s"${dependenciesSet.size} dependencies and ${rules.size} rules for vendor ${vendor.getOrElse("<empty>")}")

        val dependencyResolverFuture = ruleCodeDependencyResolver.resolve(requestJsonAST, tableDefinitions, httpDefinitions, fileDefinitions, dependenciesSet)
        dependencyResolverFuture flatMap { resolverMap =>
          val successDependencies = resolverMap.successDependencies
          val exactSuccessDependencies = resolverMap.successDependencies.filterNot(m => m._1.endsWith("_bestNode") || m._1.contains(RulecodeServiceConstants.BestMatchMethodPrefix))
          val errorDependencies = resolverMap.errorDependencies

          //When db lookup response is empty, successMap won't have entry for that dependency
          val dependencyJValue: JValue = JObject(exactSuccessDependencies.map { case (k, v) => JField(k, v) }.toList)
          val startTime = clock.now()
          val (rcHelperCalled, rcHelperReq, rcHelperFuture) = callRCHelper(requestJsonAST, vendor.getOrElse(""), dependencyJValue)

          logger.debug(s"Successfully resolved [${exactSuccessDependencies.size} / ${dependenciesSet.size}] dependencies for vendor ${vendor.getOrElse("<empty>")}")
          if (errorDependencies.nonEmpty) {
            logger.debug(s"Failed dependencies size ${errorDependencies.size} for vendor ${vendor.getOrElse("<empty>")}")
          }
          val errorDependencySequence = errorDependencies.foldLeft(Seq.empty[String]) { (errSeq, current) =>
            errSeq ++ Seq(formatErrorString(current._1, current._2.message))
          }

          val (filteredRules, vendorConfigJValue) = getFilteredRuleCodes(ruleCodeDefinitions, successDependencies, errorDependencies, vendor)

          val ruleCodesV1 = Future.sequence(
            filteredRules.map(rule => {
              val operator = supportedOperators.filter(_.id == rule.name)
              if (operator.nonEmpty) {
                try {
                  val jValueInputs = rule.inputs.map(input => {
                    Json4sUtility.parseJValue(input, requestJsonAST, successDependencies)
                  })
                  processRule(operator.head, jValueInputs.toList, rule.ruleCodeName, Json4sUtility.mergeJValue(vendorConfigJValue, rule.config), vendor)
                } catch {
                  case inputNotFoundException: InputNotFoundException => {
                    /*
                      we should send default rulecode value in case the specified parameter
                      doesn't exist in the input request.
                     */
                    logger.debug(s"InputNotFoundException occured for rulecode ${rule.ruleCodeName} - " +
                      s"${inputNotFoundException.getMessage}")
                    operator.head.getDefaultValue(rule.ruleCodeName) match {
                      case Left(n) => Future.successful(Right(Left(n)))
                      case Right(c) => Future.successful(Right(Right(c)))
                    }
                  }
                  case jsonFieldNotFoundException: JsonFieldNotFoundException => {
                    /*
                      we should send default value to the rulecode in case the specified json field
                      doesn't exist in the db lookup json response.
                     */
                    logger.debug(s"JsonFieldNotFoundException occurred for rulecode ${rule.ruleCodeName} - " +
                      s"${jsonFieldNotFoundException.getMessage}")
                    operator.head.getDefaultValue(rule.ruleCodeName) match {
                      case Left(n) => Future.successful(Right(Left(n)))
                      case Right(c) => Future.successful(Right(Right(c)))
                    }
                  }
                  case iae: IllegalArgumentException => {
                    logger.error(s"IllegalArgumentException occured for rulecode ${rule.ruleCodeName} ", iae)
                    Future.successful(Left(ErrorResponse(400, formatErrorString(rule.ruleCodeName,
                      s"IllegalArgumentException message : - ${iae.getMessage}"))))
                  }
                  case nse: NoSuchElementException => {
                    logger.error(s"NoSuchElementException occured for rulecode ${rule.ruleCodeName} ", nse)
                    Future.successful(Left(ErrorResponse(400, formatErrorString(rule.ruleCodeName,
                      s"NoSuchElementException message : - ${nse.getMessage}"))))
                  }
                  case lookupNotFoundException: LookupNotFoundException => {
                    //Not an error actually, empty db response.
                    Future.successful(Left(ErrorResponse(404, formatErrorString(rule.ruleCodeName,
                      s"LookupNotFoundException message : - ${lookupNotFoundException.getMessage}"))))
                  }
                  case bestIdentityNotFoundException: BestIdentityNotFoundException => {
                    logger.error(s"BestIdentityNotFoundException occured for rulecode ${rule.ruleCodeName} ", bestIdentityNotFoundException)
                    Future.successful(Left(ErrorResponse(400, formatErrorString(rule.ruleCodeName,
                      s"BestIdentityNotFoundException message : - ${bestIdentityNotFoundException.getMessage}"))))
                  }
                }
              } else {
                logger.error(s"Unsupported operator for the rule code ${rule.ruleCodeName}")
                Future.successful(Left(ErrorResponse(400, formatErrorString(rule.ruleCodeName, s"Unsupported operator : ${rule.name}"))))
              }
            })
          )

          val ruleCodesV2 = ruleCodeGeneratorV2.generate(requestJsonAST, ruleCodeConfigurationV2.get, Dependencies(errorDependencies, successDependencies, resolverMap.emptyDependencies))

          for {
            ruleCodesV1Response <- ruleCodesV1
            ruleCodesV2Response <- ruleCodesV2
            ruleCodesV3Response <- rcHelperFuture

            (numericRuleCodesV1, categoricalRuleCodesV1, errorsV1) = ruleCodesV1Response.foldLeft((Seq.empty[NumericalRuleCode], Seq.empty[CategoricalRuleCode], Seq.empty[String])) { case ((numSeq, catSeq, errSeq), item) =>
              item match {
                case Left(errorResponse: ErrorResponse) if errorResponse.code != 404 => (numSeq, catSeq, errSeq ++ Seq(errorResponse.message))
                case Right(Left(Some(numericalRuleCode: NumericalRuleCode))) => (numSeq ++ Seq(numericalRuleCode), catSeq, errSeq)
                case Right(Right(Some(categoricalRuleCode: CategoricalRuleCode))) => (numSeq, catSeq ++ Seq(categoricalRuleCode), errSeq)
                case _ => (numSeq, catSeq, errSeq)
              }
            }

            ruleCodeRespV2 = ruleCodesV2Response.response match {
              case Right(ruleCodeResponseV2) =>
                RuleCodeResponse(
                  numericalRuleCodes = numericRuleCodesV1 ++ ruleCodeResponseV2.numericalRuleCodes,
                  categoricalRuleCodes = categoricalRuleCodesV1 ++ ruleCodeResponseV2.categoricalRuleCodes,
                  errors = errorsV1 ++ errorDependencySequence ++ ruleCodeResponseV2.errors
                )
              case Left(errorResponse) if errorResponse.code != 404 =>
                logger.warn("V2: RuleCode Generation has returned an error response", errorResponse)
                RuleCodeResponse(
                  numericalRuleCodes = numericRuleCodesV1,
                  categoricalRuleCodes = categoricalRuleCodesV1,
                  errors = errorsV1 ++ errorDependencySequence :+ errorResponse.message
                )
              case _ =>
                RuleCodeResponse(
                  numericRuleCodesV1,
                  categoricalRuleCodesV1,
                  errorsV1 ++ errorDependencySequence
                )
            }
            combinedRuleCodeResponse = if (rcHelperCalled && ruleCodesV3Response != null) {
              RuleCodeHttpAudit.audit(
                thirdPartyAuditor = thirdPartyAuditor,
                vendorName = "rcHelper",
                requestURL = config.getString("rcHelper.endpoint"),
                requestBody = rcHelperReq.toRequest.getStringData,
                responseBody = ruleCodesV3Response.getResponseBody,
                startTime = startTime.toDate,
                clock = clock,
                isError = ruleCodesV3Response.getStatusCode != 200,
                accountId = accountId.getOrElse(0L),
                maskPii = false
              )
              ruleCodesV3Response.getStatusCode match {
              case 200 =>
                val rcHelperResponse = RcHelperResponseParser.parseRcHelperResponse(ruleCodesV3Response.getResponseBody)
                val newCategorical = rcHelperResponse.rulecodes.categorical.flatMap { entry =>
                  entry.rulecode.map { case (k, v) => CategoricalRuleCode(k, v) }
                }
                val newNumerical = rcHelperResponse.rulecodes.numerical.flatMap { entry =>
                  entry.rulecode.map { case (k, v) => NumericalRuleCode(k, v.toDouble) }
                }
                ruleCodeRespV2.copy(
                  numericalRuleCodes = ruleCodeRespV2.numericalRuleCodes ++ newNumerical,
                  categoricalRuleCodes = ruleCodeRespV2.categoricalRuleCodes ++ newCategorical
                )
              case _ =>
                logger.error(s"Error from rule code helper response, status code: ${ruleCodesV3Response.getStatusCode}")
                ruleCodeRespV2
              }
            } else {
              ruleCodeRespV2
            }
            conditionalDerivedResponse = ruleCodeGeneratorV2.generate(requestJsonAST, ruleCodeConfigurationV2.get, ruleCodesV2Response.dependencies, processDerivedRuleCodes = true, combinedRuleCodeResponse)
            combinedConditionalDerivedResponse <- conditionalDerivedResponse map { r =>
              r.response match {
                case Right(conditionalDerivedSuccessResponse) =>
                  RuleCodeResponse(
                    numericalRuleCodes = combinedRuleCodeResponse.numericalRuleCodes ++ conditionalDerivedSuccessResponse.numericalRuleCodes,
                    categoricalRuleCodes = combinedRuleCodeResponse.categoricalRuleCodes ++ conditionalDerivedSuccessResponse.categoricalRuleCodes,
                    errors = combinedRuleCodeResponse.errors ++ conditionalDerivedSuccessResponse.errors
                  )
                case Left(conditionalDerivedErrorResponse) if conditionalDerivedErrorResponse.code != 404 =>
                  logger.warn("V2: RuleCode Generation has returned an error response", conditionalDerivedErrorResponse)
                  RuleCodeResponse(
                    numericalRuleCodes = combinedRuleCodeResponse.numericalRuleCodes,
                    categoricalRuleCodes = combinedRuleCodeResponse.categoricalRuleCodes,
                    errors = combinedRuleCodeResponse.errors :+ conditionalDerivedErrorResponse.message
                  )
                case _ =>
                  combinedRuleCodeResponse
              }
            }
            result <- {
              try {
                Future.successful(Right(getDerivedRuleCodes(vendor, requestJsonAST,
                  successDependencies ++ ruleCodesV2Response.dependencies.successDependencies,
                  errorDependencies ++  ruleCodesV2Response.dependencies.errorDependencies,
                  combinedConditionalDerivedResponse)))
              } catch {
                case ex: Exception =>
                  logger.error(s"Exception in derived rulecodes for vendor ${vendor.getOrElse("null")}", ex)
                  Future.successful(Left(ErrorResponse(400, s"Exception in derived rulecodes for vendor ${vendor.getOrElse("null")} - ${ex.getMessage}")))
              }
            }
          } yield result
        }
      } catch {
        case iae: IllegalArgumentException =>
          logger.error("IllegalArgumentException during rulecode generation : ", iae)
          Future.successful(Left(ErrorResponse(400, iae.getMessage)))
        case NonFatal(ex) =>
          logger.error("NonFatal exception occurred during rulecode generation : ", ex)
          Future.failed(ex)
      }
    }
  }

  def updateRuleCodes(ruleCodeUpdate: RuleCodeUpdate): Future[Unit] = Future {
    ruleCodeUpdate.sources.foreach {
      case (source, vendorPrefixMap) => {
        vendorPrefixMap.foreach {
          case (vendorPrefix, vendorPrefixChanges) => {
            val s3BasePathV1 = "rulecode_definitions/" + source + "/" + vendorPrefix
            val s3BasePathV2 = "rulecode_definitions_v2/" + source + "/" + vendorPrefix

            if (vendorPrefixChanges.vendorConfig.isDefined && vendorPrefixChanges.vendorConfig.get.nonEmpty) {
              val vendorConfigPathV1 = s"$s3BasePathV1/vendor_config.json"
              val vendorConfigPathV2 = s"$s3BasePathV2/vendor_config.json"
              databaseHandler.uploadS3Object(Paths.get(vendorConfigPathV1), Serialization.write(vendorPrefixChanges.vendorConfig.get))
              databaseHandler.uploadS3Object(Paths.get(vendorConfigPathV2), Serialization.write(vendorPrefixChanges.vendorConfig.get))
            }

            if (vendorPrefixChanges.dependencies.nonEmpty) {
              val dependencyFilePathV1 = s3BasePathV1 + "/" + "dependency.json"
              val dependencyFilePathV2 = s3BasePathV2 + "/" + "dependency.json"
              val dependencyJson = JObject("dependencies" -> JArray(List.empty))
              val updatedDependencyNode = processDependencies(dependencyJson, vendorPrefixChanges.dependencies)
              databaseHandler.uploadS3Object(Paths.get(dependencyFilePathV1), Serialization.write(updatedDependencyNode))
              databaseHandler.uploadS3Object(Paths.get(dependencyFilePathV2), Serialization.write(updatedDependencyNode))
            }

            vendorPrefixChanges.rules.foreach {
              case (rulePrefix, rulePrefixChanges) => {

                val ruleFilePath = s3BasePathV1 + "/" + rulePrefix + ".json"
                val ruleFileJson = databaseHandler.getS3Object(Paths.get(ruleFilePath))

                val rulesNode: JValue = ruleFileJson match {
                  case Some(json) => parse(json)
                  case None => JObject("rules" -> JArray(List.empty))
                }

                val deletions = rulePrefixChanges.deletions
                val additions = rulePrefixChanges.additions
                if (deletions.nonEmpty || additions.nonEmpty) {
                  val rulesNodeWithDeletions = processDeletions(rulesNode, deletions)
                  val rulesNodeWithAdditions = processAdditions(rulesNodeWithDeletions, additions)
                  databaseHandler.uploadS3Object(Paths.get(ruleFilePath), Serialization.write(rulesNodeWithAdditions))
                }
              }
            }
            processNewRuleChanges(vendorPrefixChanges, s3BasePathV2)
          }
        }
      }
    }
  }

  private def processNewRuleChanges(vendorPrefixChanges: VendorPrefixChange, s3BasePathV2: String) = {
    vendorPrefixChanges.rulesV2.foreach {
      case (rulePrefix, rulePrefixChanges) => {
        val ruleFilePath = s3BasePathV2 + "/" + rulePrefix + ".json"
        val rulesNode: JValue = databaseHandler.getS3Object(Paths.get(ruleFilePath)) match {
          case Some(json) => parse(json)
          case None => JObject("rules" -> JArray(List.empty))
        }
        val deletions = rulePrefixChanges.deletions
        val additions = rulePrefixChanges.additions
        if (deletions.nonEmpty || additions.nonEmpty) {
          val rulesNodeWithDeletions = processDeletions(rulesNode, deletions)
          val rulesNodeWithAdditions = processAdditionsV2(rulesNodeWithDeletions, additions)
          databaseHandler.uploadS3Object(Paths.get(ruleFilePath), Serialization.write(rulesNodeWithAdditions))
        }
      }
    }
  }

  def processDependencies(dependencyNode: JValue, dependencies: List[VendorDependency]): JValue = {

    val newDependencyList = dependencies.map(dependency => dependency.dependencyType + ":" + dependency.name)
    val dependencyArray = (dependencyNode \ "dependencies").asInstanceOf[JArray]
    val existingDependencyList = dependencyArray.arr.map(obj => (obj \ "type").extract[String] + ":" + (obj \ "name").extract[String]);

    val dependenciesToBeAdded = newDependencyList.filterNot(existingDependencyList.toSet)

    val newList = dependenciesToBeAdded.map(dependency => {
      val values = dependency.split(":")
      JObject("type" -> JString(values(0)), "name" -> JString(values(1)))
    })
    dependencyNode transformField {
      case JField("dependencies", JArray(existingList)) => ("dependencies", JArray(existingList ::: newList))
    }
  }

  def processDeletions(rulesNode: JValue, deletions: List[String]): JValue = {

    if (deletions.nonEmpty) {
      val rulesArray = (rulesNode \ "rules").asInstanceOf[JArray]
      val objectsToBePersisted = rulesArray.arr.map(rulecode => {
        if (!deletions.contains((rulecode \ "rule_code_name").extract[String])) {
          Some(rulecode)
        } else {
          None
        }
      })
      rulesNode transformField {
        case JField("rules", _) => ("rules", JArray(objectsToBePersisted.flatten))
      }
    } else {
      rulesNode
    }
  }

  def processAdditions(rulesNode: JValue, additions: List[RuleCodeChange]): JValue = {

    if (additions.nonEmpty) {
      val newAdditons = additions.map(ruleCodeChange => {
        val operatorName = JObject("name" -> JString(ruleCodeChange.operator))
        val operatorInputs = JArray(ruleCodeChange.inputs.map(input => JString(input)))
        val newOperatorNode = operatorName merge JObject("inputs" -> operatorInputs)

        if (ruleCodeChange.config.isDefined) {
          val mergedConfigs = ruleCodeChange.config.get.map(c => JObject(c._1 -> JString(c._2))).reduce((x, y) => x merge y)
          val config = JObject("config" -> mergedConfigs)
          val operatorWithConfig = newOperatorNode merge config
          JObject("operator" -> operatorWithConfig, "rule_code_name" -> JString(ruleCodeChange.rulecodeName))
        }
        else
          JObject("operator" -> newOperatorNode, "rule_code_name" -> JString(ruleCodeChange.rulecodeName))
      })

      rulesNode transformField {
        case JField("rules", JArray(existingList)) => ("rules", JArray(existingList ::: newAdditons))
      }
    } else {
      rulesNode
    }
  }

  def processAdditionsV2(rulesNode: JValue, additions: List[RuleCodeDefinitionV2]): JValue = {
    if (additions.nonEmpty) {
      val newAdditons = additions.map(ruleCodeDefinitionV2 => {
        val operators = ruleCodeDefinitionV2.operators.map(operator => {
          val operatorOptions = operator.options.map(option => JObject("name" -> JString(option.name), "value" -> JString(option.value))).toList
          val preprocessors = operator.preprocessors.map(preprocessor => JObject("methods" -> JArray(preprocessor.methods.map(JString).toList), "inputs" -> JArray(preprocessor.inputs.map(JString).toList), "output" -> JString(preprocessor.output), "flattenInputs" -> JBool(preprocessor.flattenInputs))).toList
          val postprocessors = operator.postprocessors.map(preprocessor => JObject("methods" -> JArray(preprocessor.methods.map(JString).toList), "inputs" -> JArray(preprocessor.inputs.map(JString).toList), "output" -> JString(preprocessor.output), "flattenInputs" -> JBool(preprocessor.flattenInputs))).toList
          JObject("name" -> JString(operator.name), "inputs" -> JArray(operator.inputs.map(JString).toList), "output" -> JString(operator.output), "options" -> JArray(operatorOptions), "preprocessors" -> JArray(preprocessors), "postprocessors" -> JArray(postprocessors))
        }).toList
        JObject("rule_code_name" -> JString(ruleCodeDefinitionV2.name), "type" -> JString(ruleCodeDefinitionV2.`type`), "default" -> JString(ruleCodeDefinitionV2.default), "computeOnEmptyLookup" -> JBool(ruleCodeDefinitionV2.computeOnEmptyLookup), "operators" -> JArray(operators))
      })
      rulesNode transformField {
        case JField("rules", JArray(existingList)) => ("rules", JArray(existingList ::: newAdditons))
      }
    } else rulesNode
  }

  def callRCHelper(req: JValue, vendorName: String, featureData: JValue): (Boolean, Req, Future[Response]) = {
    implicit val trxId: TrxId = TrxId(Json4sUtility.getOptionalString(req, "transactionId").get)
    if (!rcHelperVendors.contains(vendorName)) {
      (false, null, Future.successful(null))
    } else {
      val endpoint = config.getString("rcHelper.endpoint")
      val requestData = RcHelperRequest(
        vendors = List(vendorName),
        userData = req,
        featureData = featureData
      )

      val request = url(endpoint + "/rulecodes")
        .POST
        .setContentType("application/json", Charset.forName("UTF-8"))
        .setBodyEncoding(Charset.forName("UTF-8"))
        .addHeader("Content-Type", "application/json")
        .addHeader("Accept", "application/json")
        .setBody(Serialization.write(requestData))

      val rcHelperFuture = metrics.timeFuture("rcHelper.duration")(http(request).recover({
        case NonFatal(ex) =>
          logger.error(s"Error while calling rcHelper for vendor ${vendorName}:", ex)
          null
      })
      )
      (true, request, rcHelperFuture)
    }
  }
}
