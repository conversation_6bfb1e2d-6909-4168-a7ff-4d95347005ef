package me.socure.rulecode.rest.service.impl

import com.google.inject.Inject
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import me.socure.rulecode.common.models._
import me.socure.rulecode.common.utilities.{ContextCache, Json4sUtility, PreAndPostProcessorUtility, RulecodeServiceConstants}
import me.socure.rulecode.operators.{OperatorV2Options, RuleCodeOperatorsV2}
import me.socure.rulecode.rest.model.Dependencies
import me.socure.rulecode.rest.service.impl.RuleCodeGeneratorV2.metrics
import me.socure.rulecode.rest.service.{RuleCodeGenerationService, RuleCodeV2Response}
import me.socure.rulecode.rest.workflow.{RuleCodeDependencyResolver, RuleCodeInputResolver}
import org.json4s.JsonAST.{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nt, JValue}
import org.json4s.{Formats, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>}
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

class RuleCodeGeneratorV2 @Inject()(contextCache: ContextCache[String, Map[String, JValue]], ruleCodeDependencyResolver: RuleCodeDependencyResolver, ruleCodeInputResolver: RuleCodeInputResolver[JValue])(implicit ec: ExecutionContext) extends RuleCodeGenerationService[RuleCodeConfigurationV2] {

  private implicit val formats: Formats = JsonFormats.RuleCodeWorkerFormats

  private final val logger = LoggerFactory.getLogger(this.getClass)

  private final val EmptyVendor = "<empty>"
  private final val TransactionIdPath = "transactionId"
  private final val AccountIdPath = "accountId"
  private final val VendorPath = "vendor"

  private final val LookupMatchRulecodeSuffix = "100000"

  private final val NumericalType = "numerical"
  private final val CategoricalType = "categorical"
  private final val Zero = "0"
  private final val NA = "na"
  private final val MINUS_ONE = "-1"


  private val emptyRuleCodeResponse: RuleCodeResponse = RuleCodeResponse(Seq.empty, Seq.empty, Seq.empty)

  override def generate(
                         request: JValue,
                         config: RuleCodeConfigurationV2,
                         existingDependencies: Dependencies, // Already existing dependencies computed during V1 generation.
                         processDerivedRuleCodes: Boolean = false,
                         ruleCodeResponse: RuleCodeResponse = emptyRuleCodeResponse
                       ): Future[RuleCodeV2Response] = {
    val transactionIdOption = Json4sUtility.getOptionalString(request, TransactionIdPath)
    val accountIdOption = Json4sUtility.getOptionalLong(request, AccountIdPath)

    (transactionIdOption, accountIdOption) match {
      case (None, None) | (Some(_), None) | (None, Some(_)) =>
        Future.successful(
          RuleCodeV2Response(
            Left(ErrorResponse(400, "Bad Request - Either transactionId or accountId is invalid")),
            existingDependencies
          )
        )
      case (Some(transactionId), Some(accountId)) =>
        try {
          val vendorOption = Json4sUtility.getOptionalString(request, VendorPath)
          implicit val trxId: TrxId = TrxId(transactionId)
          implicit val vendor: String = vendorOption.getOrElse(EmptyVendor)
          logger.debug(s"V2: Generating rulecodes for (trx id, vendor) - ($trxId, $vendor)")

          val ruleCodeCollections = getRuleCodeCollections(vendorOption, config)
          val tableDefinitions = config.tableDefinitions
          val httpDefinitions = config.httpDefinitions
          val fileDefinitions = config.fileDefinitions

          if (processDerivedRuleCodes && !ruleCodeCollections.exists(_.rules.exists(_.isDerivedRule))) {
            logger.debug(s"V2: No derived rulecodes present for the ($trxId, $vendor)")
            Future.successful(
              RuleCodeV2Response(
                Right(RuleCodeResponse(Seq.empty[NumericalRuleCode], Seq.empty[CategoricalRuleCode], Seq.empty[String])),
                existingDependencies
              )
            )
          } else {
            val dependencies = ruleCodeCollections.flatMap(_.dependencies).toSet
            val rules = ruleCodeCollections.flatMap(_.rules).toSet
            logger.debug(s"V2: Found ${rules.size} rulecode definitions, ${dependencies.size} dependencies for vendor $vendor")

            val processDependencyFuture = processDependency(request, dependencies, tableDefinitions, httpDefinitions, fileDefinitions, existingDependencies)
            for {
              allDependencies <- processDependencyFuture
              filteredRules = filterRules(vendorOption, ruleCodeCollections, allDependencies.successDependencies, allDependencies.errorDependencies)
              _ <- processVariables(request, ruleCodeCollections.flatMap(_.vendorVariables), allDependencies.successDependencies)
              result <- processRules(request, if (processDerivedRuleCodes) filteredRules.filter(r => r.isDerivedRule) else filteredRules.filterNot(r => r.isDerivedRule), allDependencies.successDependencies, ruleCodeResponse)
            } yield {
              RuleCodeV2Response(result, allDependencies)
            }
          }
        } catch {
          case ex: Exception =>
            logger.error("NonFatal exception occurred during rulecode generation : ", ex)
            metrics.increment("rulecode.v2.generate.failure", "class:" + ex.getClass.getSimpleName)
            Future.failed(ex)
        }
    }
  }

  def processDependency(
                         request: JValue,
                         dependencies: Set[RuleDependency],
                         tableDefinitions: Seq[TableDefinition],
                         httpDefinitions: Seq[JValue],
                         fileDefinitions: Seq[FileDefinition],
                         existingDependencies: Dependencies
                       )(implicit trxId: TrxId, vendor: String): Future[Dependencies] = {
      val filteredDependencies = dependencies.filterNot { dependency =>
        existingDependencies.successDependencies.contains(dependency.toKey()) ||
          existingDependencies.errorDependencies.contains(dependency.toKey()) ||
          existingDependencies.emptyDependencies.contains(dependency.toKey())
      }
      if (filteredDependencies.isEmpty) {
        logger.debug(s"V2: All dependencies are already present [success = ${existingDependencies.successDependencies.keys}, error = ${existingDependencies.errorDependencies.keys}, empty = ${existingDependencies.emptyDependencies}] for $trxId")
        Future.successful(existingDependencies)
      } else {
        logger.debug(s"V2: Processing Dependency Lookup for $trxId ${filteredDependencies.map(_.name)}")
        ruleCodeDependencyResolver.resolve(request, tableDefinitions, httpDefinitions, fileDefinitions, filteredDependencies).map { result =>
          val combinedSuccessDependencies = existingDependencies.successDependencies ++ result.successDependencies
          val combinedErrorDependencies = (existingDependencies.errorDependencies ++ result.errorDependencies) -- combinedSuccessDependencies.keys
          val combinedEmptyDependencies = (existingDependencies.emptyDependencies ++ result.emptyDependencies) -- combinedSuccessDependencies.keys -- combinedErrorDependencies.keys
          Dependencies(combinedErrorDependencies, combinedSuccessDependencies, combinedEmptyDependencies)
        }
      }
  }

  private def getRuleCodeCollections(vendor: Option[String], ruleCodeConfigurationV2: RuleCodeConfigurationV2): Seq[RuleCodeCollection] = {
    val result = vendor
      .map(name => Seq(ruleCodeConfigurationV2.ruleCodeCollections.getOrElse(Vendor(name), RuleCodeCollection(Seq.empty[RuleDependency], "v2", Seq.empty[RuleCodeDefinitionV2], Seq.empty[RuleCodeVendorVariable]))))
      .getOrElse(
        ruleCodeConfigurationV2.ruleCodeCollections
          .values
          .foldLeft(Seq.empty[RuleCodeCollection]) {
            (collection, ruleCode) => collection :+ ruleCode
          }
      )
    if (result.isEmpty)
      throw new Exception(s"V2: No matching rulecode definitions found for given vendor ${vendor}")
    else
      result
  }

  def filterRules(vendorOption: Option[String], ruleCodeCollections: Seq[RuleCodeCollection], successDependencies: Map[String, JValue], errorDependencies: Map[String, ErrorResponse]): Seq[RuleCodeDefinitionV2] = {
    val filteredRuleCodeCollections = if (errorDependencies.nonEmpty) {
      //Removing rulecode definitions for which there all dependencies (file/table/http) are failed.
      ruleCodeCollections
        .filterNot(
          definition => {
            val primaryDependencies = definition.dependencies.filter(dependency => dependency.primaryLookup)
            primaryDependencies.size == primaryDependencies.count(dependency => errorDependencies.contains(dependency.toKey()))
          }
        )
    } else {
      ruleCodeCollections
    }

    if (filteredRuleCodeCollections.size != ruleCodeCollections.size) {
      logger.debug(s"V2: Found ${filteredRuleCodeCollections.size} rulecode definitions after filtering out " +
        s"errored dependencies for vendor ${vendorOption.getOrElse("<empty>")}")
    }
    val filteredRules = filteredRuleCodeCollections.flatMap(_.rules)

    //Removing rulecode definitions for which there is no lookup match.
    //successDependencies would have values only when there is lookup match.
    val ruleCodeDefinitionsWithoutLookupData = filteredRuleCodeCollections.filter { ruleCodeDefinition =>
      ruleCodeDefinition.dependencies.nonEmpty &&
        !ruleCodeDefinition.dependencies.exists(ruleDependency => successDependencies.contains(ruleDependency.toKey()) && ruleDependency.primaryLookup)
    }

    if (ruleCodeDefinitionsWithoutLookupData.isEmpty) {
      filteredRules
    } else {
      logger.debug(s"V2: Found ${ruleCodeDefinitionsWithoutLookupData.size} rulecode definitions without lookup data for vendor ${vendorOption.getOrElse("<empty>")}")
      val vendorsWithoutLookupData = ruleCodeDefinitionsWithoutLookupData.flatMap(_.rules).
        map(_.name.split("\\.").head).toSet
      logger.debug(s"V2: Vendors without lookup data : ${vendorsWithoutLookupData}")
      val rules = filteredRules.filter { rule =>
        (vendorsWithoutLookupData.contains(rule.name.split("\\.").head) && rule.name.endsWith(LookupMatchRulecodeSuffix) ||
          (!vendorsWithoutLookupData.contains(rule.name.split("\\.").head)))
      } ++ filteredRules.filter(_.computeOnEmptyLookup)
      rules.distinct
    }
  }

  private final def getVariablesCacheKey(implicit trxId: TrxId, vendor: String): String = trxId.value concat "-variables-" concat vendor

  /**
   * Vendor variables can be defined in three ways:
   * 1. Only processors
   * 2. Only operators (can have pre/ post processors inside)
   * 3. Both processors (considered as pre-processor in this case) & operators (can have pre/ post processors inside)
   *
   * This function does the following:
   * 1. Processes each processor and collects the output of each of them.
   * 2. Processes each operator and collects the output of each of them.
   * 3. Joins both the above outputs and puts them against the key "txnid-variables" in the contextCache. Also each output key will be prefixed
   * with the vendor variable name.
   *
   * This method captures all the intermediate processor outputs ie if there are n processors & m operators, there will be n+1 entries in the output map in case of success.
   * (Operators are chained and will produce a single output)
   *
   * In case of any exception, no exception would be thrown and it will just be logged & we return an empty option as result. So, in the final map outputs of error'ed
   * operators & processors would not be present. So, during the actual rulecode processing it would result in an exception & appropriate error handling would be performed.
   *
   * @param ruleCodeVendorVariable
   * @param requestJsonAst
   * @param successDependencies
   * @param trxId
   */
  private def processVendorVariables(ruleCodeVendorVariable: RuleCodeVendorVariable, requestJsonAst: JValue, successDependencies: Map[String, JValue])(implicit trxId: TrxId, vendor: String): Future[Map[String, JValue]] = {
    Future {
      val cacheKey = getVariablesCacheKey concat "-" concat ruleCodeVendorVariable.name
      contextCache.put(cacheKey, Map.empty[String, JValue])

      val processorOutputs = ruleCodeVendorVariable.processors.flatMap { processor =>
        val outputKey = processor.output
        try {
          val processedJVal = preOrPostProcess(cacheKey, requestJsonAst, successDependencies, processor, emptyRuleCodeResponse)
          contextCache.put(cacheKey, contextCache.get(cacheKey).get + (outputKey -> processedJVal))
          Some(outputKey -> processedJVal)
        } catch {
          case e:InputNotFoundException =>
            logger.debug(s"InputNotFoundException - Error occurred while trying to evaluating processors in variable ${ruleCodeVendorVariable.name}",e.getMessage)
            Option.empty[(String, JValue)]
          case ex: Exception =>
            logger.error(s"Error occurred while trying to evaluating processors in variable ${ruleCodeVendorVariable.name}", ex)
            Option.empty[(String, JValue)]
        }
      }
      val operatorOutputs = ruleCodeVendorVariable.operators.flatMap { operator =>
        val outputKey = operator.output
        try {
          Some(outputKey -> processOperatorsV2(operator, cacheKey, requestJsonAst, successDependencies, emptyRuleCodeResponse).getOrElse(JNothing))
        } catch {
          case ex: Exception =>
            logger.error(s"Error occurred while trying to evaluating operators in variable ${ruleCodeVendorVariable.name}", ex)
            Option.empty[(String, JValue)]
        }
      }
      contextCache.delete(cacheKey)
      (processorOutputs ++ operatorOutputs).map(e => (s"${RulecodeServiceConstants.VariablesPrefix}${ruleCodeVendorVariable.name}.${e._1}", e._2)).toMap
    }
  }

  private def processVariables(request: JValue, variables: Seq[RuleCodeVendorVariable], successDependencies: Map[String, JValue])(implicit trxId: TrxId, vendor: String): Future[Unit] = {
    Future.sequence(
      variables.map(processVendorVariables(_, request, successDependencies))
    )
      .flatMap(response => {
        contextCache.put(getVariablesCacheKey, response.foldLeft(Map.empty[String, JValue])(_ ++ _))
        Future.successful()
      })
  }

  private def processRules(request: JValue, filteredRules: Seq[RuleCodeDefinitionV2], successDependencies: Map[String, JValue], ruleCodeResponse: RuleCodeResponse)(implicit trxId: TrxId, vendor: String): Future[Either[ErrorResponse, RuleCodeResponse]] = {
    Future.sequence(
      filteredRules.map(processRuleV2(_, request, successDependencies, ruleCodeResponse))
    )
      .flatMap(response => {
        // All variables could be removed since the rulecode generation is complete.
        contextCache.delete(getVariablesCacheKey)
        val (numericRuleCodes, categoricalRuleCodes, errors) = response.foldLeft(Seq.empty[NumericalRuleCode], Seq.empty[CategoricalRuleCode], Seq.empty[String]) {
          case ((numSeq, catSeq, errSeq), item) =>
            item match {
              case Left(errorResponse: ErrorResponse) if errorResponse.code != 404 => (numSeq, catSeq, errSeq ++ Seq(errorResponse.message))
              case Right(Left(Some(numericalRuleCode: NumericalRuleCode))) => (numSeq ++ Seq(numericalRuleCode), catSeq, errSeq)
              case Right(Right(Some(categoricalRuleCode: CategoricalRuleCode))) => (numSeq, catSeq ++ Seq(categoricalRuleCode), errSeq)
              case _ => (numSeq, catSeq, errSeq)
            }
        }
        Future.successful(Right(RuleCodeResponse(numericRuleCodes, categoricalRuleCodes, errors)))
      })
  }

  private def processRuleV2(ruleCodeDefinitionV2: RuleCodeDefinitionV2, requestJsonAst: JValue, successDependencies: Map[String, JValue], response: RuleCodeResponse)(implicit trxId: TrxId, vendor: String): Future[Either[ErrorResponse, Either[Option[NumericalRuleCode], Option[CategoricalRuleCode]]]] = {
    Future {
      val ruleCodeType = ruleCodeDefinitionV2.`type`
      val ruleCodeName = ruleCodeDefinitionV2.name
      val default = defaultValue(ruleCodeType, ruleCodeDefinitionV2.default)
      val cacheKey = trxId.value concat "-" concat ruleCodeName

      contextCache.put(cacheKey, Map.empty[String, JValue])

      val (processedValue, useEntryConditionDefault) =
        try {
          val evaluateEntryConditionResult = evaluateEntryCondition(ruleCodeDefinitionV2, requestJsonAst, successDependencies, response, cacheKey)
          if (evaluateEntryConditionResult) {
            (ruleCodeDefinitionV2.operators.foldLeft(Option.empty[JValue]) {
              (_, operator) => processOperatorsV2(operator, cacheKey, requestJsonAst, successDependencies, response)
            }, false)
          } else {
            (ruleCodeDefinitionV2.entryConditionDefault.map(v => Some(JString(v))).getOrElse(Some(JNothing)), true)
          }
        } catch {
          case iae: IllegalArgumentException =>
            logger.debug("Exception occurred while evaluating operator chain", iae.getMessage)
            (default, false)
          case e:NoSuchElementException =>
            logger.debug("NoSuchElementException  while evaluating operator chain ", e.getMessage)
            (default, false)
          case e:JsonFieldNotFoundException =>
            logger.debug("JsonFieldNotFoundException  while evaluating operator chain ", e.getMessage)
            (default, false)
          case e:LookupNotFoundException =>
            logger.debug("LookupNotFoundException  while evaluating operator chain ", e.getMessage)
            (default, false)
          case e:InputNotFoundException =>
            logger.debug("InputNotFoundException  while evaluating operator chain ", e.getMessage)
            (default, false)
          case ex: Exception =>
            logger.error("Error occurred while trying to evaluate operator chain", ex)
            (default, false)
        }
      contextCache.delete(cacheKey)
      ruleCodeResponse(ruleCodeType, ruleCodeName, processedValue, ruleCodeDefinitionV2.default, useEntryConditionDefault, ruleCodeDefinitionV2.entryConditionDefault)
    }
  }

  private def evaluateEntryCondition(ruleCodeDefinitionV2: RuleCodeDefinitionV2, requestJsonAst: JValue, successDependencies: Map[String, JValue], response: RuleCodeResponse, cacheKey: String)(implicit trxId: TrxId, vendor: String): Boolean = {
    if (ruleCodeDefinitionV2.entryConditionOperators.nonEmpty) {
      val entryConditionSatisfied = ruleCodeDefinitionV2.entryConditionOperators.foldLeft(Option.empty[JValue]) {
        (_, operator) => processOperatorsV2(operator, cacheKey, requestJsonAst, successDependencies, response)
      }
      entryConditionSatisfied match {
        case Some(JBool(v)) =>
          v
        case _ =>
          false
      }
    } else {
      true
    }
  }

  private def processOperatorsV2(operator: RuleCodeOperatorV2, cacheKey: String, requestJsonAst: JValue, successDependencies: Map[String, JValue], ruleCodeResponse: RuleCodeResponse)(implicit trxId: TrxId, vendor: String): Option[JValue] = {
    //pre-processing
    preOrPostProcess(cacheKey, requestJsonAst, successDependencies, operator.preprocessors, ruleCodeResponse)

    val variablesLookup = contextCache.get(getVariablesCacheKey).getOrElse(Map.empty[String, JValue])
    val inputsJValue: Seq[JValue] = operator.inputs.map(ruleCodeInputResolver.resolveInput(_, requestJsonAst, successDependencies, contextCache.get(cacheKey).get, variablesLookup, ruleCodeResponse))
    val operatorWithNameAndOptions = RuleCodeOperatorsV2.lookupByName(operator.name)
    val output: Option[JValue] = operatorWithNameAndOptions match {
      case Some(operatorV2) =>
        val options = operatorOptions(operator.options, operatorV2.optionsExtractor)
        operatorV2.operator.process(inputsJValue, options)
      case None =>
        logger.error(s"Unable to find specified operator with name ${operator.name} while evaluating operator chain")
        throw new IllegalArgumentException("V2 Operator Not Found")
    }
    val outputName = operator.output
    val updatedMap = contextCache.get(cacheKey).get + (outputName -> output.getOrElse(JNothing))
    contextCache.put(cacheKey, updatedMap)

    //post-processing
    preOrPostProcess(cacheKey, requestJsonAst, successDependencies, operator.postprocessors, ruleCodeResponse)
    val outputCacheKey = operator.postprocessors.lastOption.map(_.output).getOrElse(outputName)
    contextCache.get(cacheKey).get(outputCacheKey).toOption
  }

  /**
   * preOrPostProcess method is used for pre/post processing. Input and Output of the processor method is JValue.
   * Input JValue is wrapped in JArray to support multiple inputs to processor method.
   * The processor method computes the result (JValue) and the same is fed as input to subsequent processor methods.
   * The final JValue of the processor method is stored in cache using the outputName from processor.
   *
   * @param cacheKey            is the transactionId + rulecode name.
   * @param requestJsonAst      - id+ inputs.
   * @param successDependencies - resolved db input.
   * @param processors          - pre/post processors executed in order.
   */
  private def preOrPostProcess(cacheKey: String, requestJsonAst: JValue, successDependencies: Map[String, JValue], processors: Seq[RuleCodeOperatorProcessor], ruleCodeResponse: RuleCodeResponse)(implicit trxId: TrxId, vendor: String): Unit = {
    processors.foldLeft() {
      (_, processor) =>
        val processedJVal = preOrPostProcess(cacheKey, requestJsonAst, successDependencies, processor, ruleCodeResponse)
        val outputKey = processor.output
        contextCache.put(cacheKey, contextCache.get(cacheKey).get + (outputKey -> processedJVal))
    }
  }

  private def preOrPostProcess(cacheKey: String, requestJsonAst: JValue, successDependencies: Map[String, JValue], processor: RuleCodeOperatorProcessor, ruleCodeResponse: RuleCodeResponse)(implicit trxId: TrxId, vendor: String): JValue = {
    val variablesLookup = contextCache.get(getVariablesCacheKey).getOrElse(Map.empty[String, JValue])
    val dependencyInput = processor.inputs.map(ruleCodeInputResolver.resolveInput(_, requestJsonAst, successDependencies, contextCache.get(cacheKey).get, variablesLookup, ruleCodeResponse)).toList
    RuleCodeGeneratorV2.preOrPostProcess(cacheKey, requestJsonAst, successDependencies, processor, variablesLookup, dependencyInput)
  }

  private def defaultValue(`type`: String, default: String): Option[JValue] = {
    (`type`.toLowerCase, default.toLowerCase) match {
      case (_, NA) => None
      case (NumericalType, Zero) => Some(JDouble(Zero.toDouble))
      case (CategoricalType, Zero) => Some(JString("0.0"))
      case (NumericalType, MINUS_ONE) => Some(JDouble(MINUS_ONE.toDouble))
      case (CategoricalType, MINUS_ONE) => Some(JString("-1.0"))
      case _ => None
    }
  }

  private def ruleCodeResponse(`type`: String, ruleCodeName: String, value: Option[JValue], default: String, useEntryConditionDefault: Boolean, entryConditionDefault: Option[String]): Either[ErrorResponse, Either[Option[NumericalRuleCode], Option[CategoricalRuleCode]]] = {
    `type`.toLowerCase match {
      case NumericalType =>
        Right(Left(numericalRuleCode(ruleCodeName, value, default, useEntryConditionDefault, entryConditionDefault)))
      case CategoricalType =>
        Right(Right(categoricalRuleCode(ruleCodeName, value, default, useEntryConditionDefault, entryConditionDefault)))
      case _ =>
        logger.error(s"V2: Rulecode type not specified (numeric or categorical) or specified with an invalid value for rulecode $ruleCodeName")
        Left(ErrorResponse(400, "Rulecode Type Not Specified or Invalid Value Given for Type"))
    }
  }

  private def numericalRuleCode(ruleCodeName: String, value: Option[JValue], ruleCodeDefault: String, useEntryConditionDefault: Boolean, entryConditionDefault: Option[String]): Option[NumericalRuleCode] = {

    def getAsDouble(jValue: JValue): Option[Double] = {
      jValue.extractOpt[String] match {
        case Some(x) if x.nonEmpty =>
          Try(x.toDouble) match {
            case Success(value) => Some(value)
            case Failure(_) => Option.empty[Double]
          }
        case _ => Option.empty[Double]
      }
    }

    val default = if (useEntryConditionDefault) entryConditionDefault.getOrElse("") else ruleCodeDefault
    value flatMap {
      case JBool(b) =>
        val ruleCodeValue = if (b) 1.0 else 0.0
        Some(NumericalRuleCode(ruleCodeName, ruleCodeValue))
      case jValue: JValue =>
        getAsDouble(jValue) match {
          case Some(x) => Some(NumericalRuleCode(ruleCodeName, x))
          case None =>
            logger.debug(s"Unable to convert rulecode value to numeric type for (rulecodeName, ruleCodeValue): ($ruleCodeName, $value)")
            default match {
              case Zero =>
                Some(NumericalRuleCode(ruleCodeName, Zero.toDouble))
              case MINUS_ONE =>
                Some(NumericalRuleCode(ruleCodeName, MINUS_ONE.toDouble))
              case NA =>
                Option.empty[NumericalRuleCode]
              case _ =>
                Option.empty[NumericalRuleCode]
            }
        }
    }
  }

  private def categoricalRuleCode(ruleCodeName: String, value: Option[JValue], ruleCodeDefault: String, useEntryConditionDefault: Boolean, entryConditionDefault: Option[String]): Option[CategoricalRuleCode] = {
    val default = if (useEntryConditionDefault) entryConditionDefault.getOrElse("") else ruleCodeDefault
    value flatMap {
      case JString(s) if s.nonEmpty =>
        Some(CategoricalRuleCode(ruleCodeName, s))
      case JInt(i) =>
        Some(CategoricalRuleCode(ruleCodeName, i.toString))
      case JDouble(d) =>
        Some(CategoricalRuleCode(ruleCodeName, d.toString))
      case _ =>
        logger.debug(s"Unable to convert rulecode value to categorical type for (rulecodeName, ruleCodeValue): ($ruleCodeName, $value)")
        default match {
          case Zero =>
            Some(CategoricalRuleCode(ruleCodeName, "0.0"))
          case MINUS_ONE =>
            Some(CategoricalRuleCode(ruleCodeName, "-1.0"))
          case NA =>
            None
          case _ =>
            None
        }
    }
  }

  private def operatorOptions(options: Seq[RuleCodeOperatorOption], optionsExtractor: Map[String, String] => OperatorV2Options): OperatorV2Options = {
    try {
      val optionsMap = options.map(option => option.name -> option.value).toMap
      optionsExtractor(optionsMap)
    } catch {
      case NonFatal(ex) =>
        logger.error(s"Error occurred while parsing options for operator:", ex)
        throw new IllegalArgumentException("Options for Operator could not be initialised")
    }
  }

}


object RuleCodeGeneratorV2 {

  private val metrics: Metrics = JavaMetricsFactory.get(this.getClass)

  def preOrPostProcess(cacheKey: String, requestJsonAst: JValue, successDependencies: Map[String, JValue], processor: RuleCodeOperatorProcessor, variablesLookup: Map[String, JValue], dependencyInput: List[JValue])(implicit vendor: String): JValue = {
    val input = dependencyInput match {
      case jValList: List[JValue] => {
        val result = jValList.foldLeft(List.empty[JValue]) {
          (list, jValue) =>
            jValue match {
              case jArray: JArray if !processor.flattenInputs => list :+ jArray
              case jArrVal: JArray => list ++ jArrVal.arr
              case jVal: JValue => list :+ jVal
              case _ => list
            }
        }
        JArray(result)
      }
      case _ => JNothing
    }
    processor.methods.foldLeft(input: JValue) {
      (intermediateJVal, methodName) =>
        val processorMapping = PreAndPostProcessorUtility.getProcessor(methodName)
        processorMapping match {
          case Some(processor) => processor.process(intermediateJVal)
          case _ => throw new IllegalArgumentException(s"pre/post processor mapping not found: $methodName")
        }
    }
  }
}
