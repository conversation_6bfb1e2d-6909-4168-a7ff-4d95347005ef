package me.socure.rulecode.rest.workflow

import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.model.ErrorResponse
import me.socure.rulecode.common.models.{RuleCodeDBInfo, RulecodeDynamoDbInfo}
import me.socure.rulecode.database.DatabasesHandler
import me.socure.rulecode.dynamo.database.DynamoDBHandler
import org.json4s.{DefaultFormats, Formats}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class RuleCodeDBWorkflowImpl(databaseHandler: DatabasesHandler, dynamoDBHandler: DynamoDBHandler)
                            (implicit ec: ExecutionContext) extends RuleCodeDBWorkflow {

  private implicit def jsonFormats: Formats = DefaultFormats

  private val metrics: Metrics = JavaMetricsFactory.get(getClass)

  /**
   * fetch table info for rc_table_config key for config table
   */
  override def fetchDBInfoV2(): Future[Either[ErrorResponse, Map[String,RulecodeDynamoDbInfo]]] = {
    Future
      .fromTry(Try(dynamoDBHandler.getConfigTableInfo))
      .map(Right(_))
  }

  /**
   * fetch table info for rc_table_config_ingestion_success key and save it to rc_table_config key for config table
   */
  override def swapTableConfig() : Future[Either[ErrorResponse, Boolean]] = {
    val resTry = metrics.time("v2.swap.table.info")(
      Try(dynamoDBHandler.swapTableConfig())
        .flatMap { _ =>
          Try(dynamoDBHandler.refresh(failOnError = true)).map(_ => true)
        }
    )
    Future.fromTry(resTry).map(Right(_))
  }

  /**
   * fetch table info for rc_table_config key and save it to rc_table_config_rollback key for config table
   */
  override def rollbackTableConfig() : Future[Either[ErrorResponse, Boolean]] = {
    val resTry = metrics.time("v2.rollback.table.info")(
      Try(dynamoDBHandler.rollbackConfig())
        .flatMap { _ =>
          Try(dynamoDBHandler.refresh(failOnError = true)).map(_ => true)
        }
    )
    Future.fromTry(resTry).map(Right(_))
  }

  /**
   * update rc_table_config or rc_config_unified based on environment with new config
   */
  override def updateDynamoConfig(config: Map[String, String]) : Future[Either[ErrorResponse, Boolean]] = {
    if(config.isEmpty) {
      Future.successful(Left(ErrorResponse(199, "Config found to be empty, No update done.")))
    } else {
      val resTry = Try(dynamoDBHandler.updateDynamoConfig(config))
        .flatMap { _ =>
          Try(dynamoDBHandler.refresh(failOnError = true)).map(_ => Right(true))
        }.recover {
        case ex: Exception =>
          Left(ErrorResponse(code = 500, message = ex.getMessage))
      }
      Future.fromTry(resTry)
    }

  }

}