package me.socure.rulecode.rest.workflow

import me.socure.model.ErrorResponse
import me.socure.rulecode.common.models.RuleCodeResponse
import me.socure.rulecode.common.utilities.{Json4sUtility, RulecodeServiceConstants}
import org.json4s.{JDouble, JNothing}
import org.json4s.JsonAST.{JString, JValue}

import scala.util.Random

class RuleCodeInputResolverImpl extends RuleCodeInputResolver[JValue] {
  override def resolveInput(path: String, inputLookup: JValue, dependencyLookup: Map[String, JValue],
                            cacheLookup: Map[String, JValue], variablesLookup: Map[String, JValue],
                            ruleCodeResponse: RuleCodeResponse): JValue = {
    if (path.startsWith(RulecodeServiceConstants.VariablesPrefix)) {
      if (path.contains("$$")) {
        val splits = path.split("\\$\\$.")
        val lookupKey = splits(0)
        val lookupPath = splits(1)
        val randomDepPath = s"table.${System.nanoTime()}"
        val depMap = Map(randomDepPath -> variablesLookup(lookupKey))
        Json4sUtility.parseJValue(s"$randomDepPath.$lookupPath", inputLookup, depMap)
      } else variablesLookup(path)
    } else if (path.startsWith(RulecodeServiceConstants.RulePrefix)) {
      ruleCodeResponseLookup(path, ruleCodeResponse)
    } else if (cacheLookup.contains(path)) {
      cacheLookup(path)
    } else {
      Json4sUtility.parseJValue(path, inputLookup, dependencyLookup)
    }
  }

  private def ruleCodeResponseLookup(path: String, ruleCodeResponse: RuleCodeResponse) = {
      val ruleName: String = path.replace(RulecodeServiceConstants.RulePrefix, "")
      val matchingCategoricalRuleCodeResponse = ruleCodeResponse.categoricalRuleCodes.find(r => r.name.equals(ruleName))
      if (matchingCategoricalRuleCodeResponse.nonEmpty) {
        JString(matchingCategoricalRuleCodeResponse.get.value)
      }else {
        val matchingNumericalRuleCodeResponse = ruleCodeResponse.numericalRuleCodes.find(r => r.name.equals(ruleName))
        if (matchingNumericalRuleCodeResponse.nonEmpty) {
          JDouble(matchingNumericalRuleCodeResponse.get.value)
        } else {
          JNothing
        }
      }
  }
}
