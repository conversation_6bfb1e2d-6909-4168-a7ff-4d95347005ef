package me.socure.rulecode.rest.service.impl

import me.socure.rulecode.common.models.{CategoricalRuleCode, JsonFormats, NumericalRuleCode, RuleCodeRequest, RuleCodeResponse}
import me.socure.rulecode.common.utilities.DateUtility
import me.socure.rulecode.rest.utilities.RulecodeRequestUtilility
import me.socure.rulecode.rest.workflow.RuleCodeRequestCreator.getRuleCodeRequest
import org.apache.commons.csv.{CSVFormat, CSVParser}
import org.joda.time.DateTime
import org.json4s.Formats
import org.json4s.jackson.{JsonMethods, Serialization}
import org.json4s.native.JsonMethods.parse
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{FreeSpec, Matchers}
import org.scalatra.pass

import java.io.FileInputStream
import java.nio.charset.StandardCharsets
import scala.collection.JavaConversions.seqAsJavaList
import scala.collection.JavaConverters._
import scala.collection.mutable
import scala.io.Source

class MnivlRuleCodeGenerationTest extends FreeSpec with Matchers with ScalaFutures {

  private val Generator = new TestRuleCodeGeneratorV2

  private implicit val Formats: Formats = JsonFormats.RuleCodeWorkerFormats

  private val Rulecodes = List(
    ("MNIVL.100001","C"),
    ("MNIVL.100002","C"),
    ("MNIVL.100003","C"),
    ("MNIVL.100004","C"),
    ("MNIVL.100005","N"),
    ("MNIVL.100006","N"),
    ("MNIVL.100007","C"),
    ("MNIVL.100008","N"),
    ("MNIVL.100009","N"),
    ("MNIVL.100010","C"),
    ("MNIVL.100011","N"),
    ("MNIVL.100012","N"),
    ("MNIVL.100013","C"),
    ("MNIVL.100014","C"),
    ("MNIVL.100015","C"),
    ("MNIVL.100016","C"),
    ("MNIVL.100017","N"),
    ("MNIVL.100018","N"),
    ("MNIVL.100019","C"),
    ("MNIVL.100020","C"),
    ("MNIVL.100021","C"),
    ("MNIVL.100022","C"),
    ("MNIVL.100023","C"),
    ("MNIVL.100029","N"),
    ("MNIVL.100030","C"),
    ("MNIVL.100031","C"),
    ("MNIVL.100032","C"),
    ("MNIVL.100033","C")
  )

  "MNIVL rulecodes" - {

    Rulecodes foreach {
      case (ruleCode, rcType) => {
        val testStream = new FileInputStream(getClass.getClassLoader.getResource(s"test_files/MNIVL/MNIVL_1000XX.csv").getFile)
        val csvReader = CSVParser.parse(testStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
        csvReader
          .getRecords
          .asScala.map(row => {

            val firstName = Option(row.get(1)).filter(_ != "NA")
            val surName = Option(row.get(2)).filter(_ != "NA")
            val mobileNumber = Option(row.get(3)).filter(_ != "NA")
            val phoneNumber = Some(mobileNumber.get.substring(3))
            val phoneCountryCode = Some(mobileNumber.get.substring(0,3))
            val fullName = Option(row.get(4)).filter(_ != "NA")
            val response = Option(row.get(5)).filter(_ != "NA")
            var expected = Option(row.get(ruleCode)).filter(_ != "NA").filter(_!="").filter(_!=null)
            val submissionDate = getDateMillis("2025-04-20")

            val serviceResponse = response match {
              case Some(res) =>
                parse(
                  s"""{
                     |"responses": [{
                     |   "response" : $res
                     |}],
                     |}""".stripMargin)
              case _ =>
                val res = null
                parse(
                  s"""{
                     |"responses": [{
                     |   "response" : $res
                     |}],
                     |}""".stripMargin)
            }

            if(expected.isDefined && expected.get.contains("[")){
              // TODO : Check it
              expected = Some(expected.get.replace("[","").replace("]","").replace("\"","").replace(", ",",").replace("null","")).filter(_ != "NA").filter(_!="").filter(_!=null)
            }

            s"[ ${ruleCode} - ${row.getRecordNumber} ] - $expected" in {
              val request = RulecodeRequestUtilility.aRuleCodeRequest(firstName = firstName, lastName = surName, phoneNumber = phoneNumber,
                fullName = fullName, rawSubmissionDate = submissionDate
              )
              Generator.generate(request, ruleCode, Serialization.write(serviceResponse)) match {
                case Some(r) =>
                  r.rcType shouldBe rcType
                  println(r)
                  if(rcType.equalsIgnoreCase("N")){
                    r.value.toDouble shouldBe expected.get.toDouble
                  }else{
                    r.value shouldBe expected.get
                  }
                case None => expected shouldBe None
              }
            }
          }
          )
      }
      case _ => None
    }
  }

  "MNIVL Tests" - {
    val testDetails = Source.fromURL(getClass.getClassLoader.getResource(s"test_files/MNIVL/test_details.txt"))
    try {
      testDetails.getLines().foreach { line =>
        val rcData = line.split(",")
        test(rcData.head, rcData.last)
      }
    } finally {
      testDetails.close()
    }
  }

  "MNIVL Derived Tests" - {
    val testDetails = Source.fromURL(getClass.getClassLoader.getResource(s"test_files/MNIVL/test_details_derived.txt"))
    try {
      testDetails.getLines().foreach { line =>
        val rcData = line.split(",")
        testDerived(rcData.head, rcData.last)
      }
    } finally {
      testDetails.close()
    }
  }

  "MNIVL Date Tests" - {
    List("MNIVL.800005").foreach(testWithDates)
  }

  private def testWithDates(ruleCodeName: String): mutable.Seq[Unit] = {
    val testStream = new FileInputStream(getClass.getClassLoader.getResource(s"test_files/MNIVL/$ruleCodeName.csv").getFile)
    val csvReader = CSVParser.parse(testStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
    csvReader
      .getRecords
      .asScala.map(row => {
        val lookupStr = Option(row.get(0)).filter(_ != "NA")
        val submissionDateOpt = Option(row.get(1)).filter(_ != "NA")
        val transactionDateOpt = Option(row.get(2)).filter(_ != "NA")
        val expected = Option(row.get(3)).filter(_ != "NA")
        val serviceResponse = lookupStr match {
          case Some(res) =>
            parse(
              s"""{
                 |"responses": [{
                 |   "response" : $res
                 |}],
                 |}""".stripMargin)
          case _ =>
            val res = null
            parse(
              s"""{
                 |"responses": [{
                 |   "response" : $res
                 |}],
                 |}""".stripMargin)
        }
        s"[ $ruleCodeName - ${row.getRecordNumber} ] - $expected" in {
          val request = RulecodeRequestUtilility.aRuleCodeRequest(
            rawSubmissionDate = submissionDateOpt.map(DateUtility.getDateTime(_).get.getMillis),
            rawTransactionDate = transactionDateOpt.map(DateUtility.getDateTime(_).get.getMillis)
          )
          Generator.generate(request, ruleCodeName, Serialization.write(serviceResponse)) match {
            case Some(r) =>
              r.rcType shouldBe "N"
              r.value.toDouble shouldBe expected.get.toDouble
            case None => expected shouldBe None
          }
        }
      })
  }

  private def testDerived(ruleCodeName: String, rcType: String): mutable.Seq[Unit] = {
    val lookupMap = Map(
      "http.monnai" -> "{}"
    )
    val testStream = new FileInputStream(getClass.getClassLoader.getResource(s"test_files/MNIVL/$ruleCodeName.csv").getFile)
    val csvReader = CSVParser.parse(testStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
    val headers = csvReader.getHeaderNames.asScala.toList
    csvReader
      .getRecords
      .asScala.map(row => {
        val numericalRuleCodes = (0 until headers.size - 1).map { i =>
          val header = headers.get(i)
          val entry = row.get(i)
          if(entry != "NA" && header.startsWith("N_")) Seq(NumericalRuleCode(header.split("_").last, row.get(i).toDouble)) else Seq.empty[NumericalRuleCode]
        }.reduce(_ ++ _)
        val categoricalRuleCodes = (0 until headers.size - 1).map { i =>
          val header = headers.get(i)
          val entry = row.get(i)
          if(entry != "NA" && header.startsWith("C_")) Seq(CategoricalRuleCode(header.split("_").last, row.get(i))) else Seq.empty[CategoricalRuleCode]
        }.reduce(_ ++ _)
        val expected = Option(row.get(headers.size - 1)).filter(_ != "NA")
        val response = RuleCodeResponse(
          numericalRuleCodes,
          categoricalRuleCodes,
          Seq.empty[String]
        )
        val ruleCodeRequest = if (headers.contains("INPUT")) {
          Option(row.get("INPUT")).fold(RulecodeRequestUtilility.aRuleCodeRequest()) { s =>
            val parsedJson = JsonMethods.parse(s)
            val jsonWithMandatoryFields = parsedJson.merge(JsonMethods.parse(s"""
          {
            "transactionId": "test-txn",
            "accountId": 4665,
            "maskPii": false
          }
        """))
            jsonWithMandatoryFields.extract[RuleCodeRequest]
          }
        } else RulecodeRequestUtilility.aRuleCodeRequest()

        s"[ $ruleCodeName - ${row.getRecordNumber} ] - $expected" in {
          Generator.generate(ruleCodeRequest, ruleCodeName, "", lookupMap, processDerived = true, response) match {
            case Some(r) =>
              r.rcType shouldBe rcType
              rcType match {
                case "N" =>
                  expected.get.toDouble shouldBe r.value.toDouble
                case "C" =>
                  expected.get shouldBe r.value
                case _ => fail(s"Invalid rulecode type: $rcType")
              }
            case None => expected shouldBe None
          }
        }
      })
  }

  private def test(ruleCodeName: String, rcType: String): mutable.Seq[Unit] = {
    val testStream = new FileInputStream(getClass.getClassLoader.getResource(s"test_files/MNIVL/$ruleCodeName.csv").getFile)
    val csvReader = CSVParser.parse(testStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
    csvReader
      .getRecords
      .asScala.map(row => {
      val lookupStr = Option(row.get(0)).filter(_ != "NA")
      val expected = Option(row.get(1)).filter(_ != "NA")
      val inputStr = if (row.size() > 2) Option(row.get(2)).filter(_ != "NA") else Option.empty[String]
        val ruleCodeRequest = inputStr.fold(RulecodeRequestUtilility.aRuleCodeRequest()) { s =>
          val parsedJson = JsonMethods.parse(s)
          val jsonWithMandatoryFields = parsedJson.merge(JsonMethods.parse("""
          {
            "transactionId": "test-txn",
            "accountId": 4665,
            "maskPii": false
          }
        """))
          jsonWithMandatoryFields.extract[RuleCodeRequest]
        }
        val serviceResponse = lookupStr match {
          case Some(res) if res.nonEmpty =>
            parse(
              s"""{
                 |"responses": [{
                 |   "response" : $res
                 |}],
                 |}""".stripMargin)
          case _ =>
            parse(
              s"""{
                 |"responses": [],
                 |}""".stripMargin)
        }
      s"[ $ruleCodeName - ${row.getRecordNumber} ] - $expected" in {
        Generator.generate(ruleCodeRequest, ruleCodeName, Serialization.write(serviceResponse)) match {
          case Some(r) =>
            r.rcType shouldBe rcType
            rcType match {
              case "N" =>
                expected.get.toDouble shouldBe r.value.toDouble
              case "C" =>
                expected.get shouldBe r.value
              case _ => fail(s"Invalid rulecode type: $rcType")
            }
          case None => expected shouldBe None
        }
      }
    })
  }

  private def getDateMillis(dateStrVal: String): Option[Long] = {
    dateStrVal match {
      case dateStr if dateStr.trim.nonEmpty => DateUtility.getDateTime(dateStr.trim).map(_.getMillis)
      case _ => None
    }
  }
}