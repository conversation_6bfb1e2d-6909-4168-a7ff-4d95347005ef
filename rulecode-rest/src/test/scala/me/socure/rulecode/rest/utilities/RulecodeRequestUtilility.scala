package me.socure.rulecode.rest.utilities

import me.socure.pii.standardization.common.model.response.PIIStandardizationResponse
import me.socure.rulecode.common.models.{IdPlusRulecodes, JsonFormats, RuleCodeRequest, RuleCodeRequestRawParams}
import org.json4s.Formats
import org.json4s.native.JsonMethods

object RulecodeRequestUtilility {

  implicit val formats: Formats = JsonFormats.RuleCodeWorkerFormats

  private val BaseRequest = JsonMethods.parse(
    """
      |{
      |  "transactionId":"test-txn",
      |  "accountId":4665,
      |  "maskPii":false
      |}
      |""".stripMargin)

  def getRequest(value: String): RuleCodeRequest = {
    JsonMethods.parse(value).merge(BaseRequest).extract[RuleCodeRequest]
  }

  def aRuleCodeRequest(
                        transactionId: Option[String] = Some("test-txn"),
                        accountId: Option[Long] = Some(4665L),
                        firstName: Option[String] = None,
                        lastName: Option[String] = None,
                        countryCode: Option[Int] = None,
                        phoneNumber: Option[String] = None,
                        street: Option[String] = None,
                        city: Option[String] = None,
                        state: Option[String] = None,
                        zip: Option[String] = None,
                        zip4: Option[String] = None,
                        country: Option[String] = None,
                        vendor: Option[String] = None,
                        maskPii: Boolean = false,
                        ssn: Option[String] = None,
                        ip: Option[String] = None,
                        rawSubmissionDate: Option[Long] = None,
                        rawTransactionDate: Option[Long] = None,
                        rawEmail: Option[String] = None,
                        rawDob: Option[Long] = None,
                        rawPhysicalAddress: Option[String] = None,
                        rawPhysicalAddress2: Option[String] = None,
                        rawPhysicalAddressRaw: Option[String] = None,
                        rawCity: Option[String] = None,
                        rawState: Option[String] = None,
                        rawZip: Option[String] = None,
                        rawMobileNumber: Option[String] = None,
                        rawIp: Option[String] = None,
                        piiStdResponse: Option[PIIStandardizationResponse] = None,
                        idPlusRulecodes: Option[IdPlusRulecodes] = None,
                        latitude: Option[Double] = None,
                        longitude: Option[Double] = None,
                        prefillSsn: Option[String] = None,
                        fullName : Option[String] = None,
                        phoneCountryCode : Option[String] = None,
                        rawDobString : Option[String] = None
                      ): RuleCodeRequest = {
    RuleCodeRequest(
      transactionId = transactionId,
      accountId = accountId,
      first_name = firstName,
      last_name = lastName,
      country_code = countryCode,
      phone_number = phoneNumber,
      street = street,
      city = city,
      state = state,
      zip = zip,
      zip4 = zip4,
      country = country,
      vendor = vendor,
      maskPii = maskPii,
      ssn = ssn,
      ip = ip,
      latitude = latitude,
      longitude = longitude,
      idPlusRulecodes = idPlusRulecodes,
      prefillSsn = prefillSsn,
      piiStdResponse = piiStdResponse,
      fullName = fullName,
      phoneCountryCode = phoneCountryCode,
      raw = Some(RuleCodeRequestRawParams(
        submissionDate = rawSubmissionDate,
        transactionDate = rawTransactionDate,
        email = rawEmail,
        dob = rawDob,
        dobString = rawDobString,
        physicalAddress = rawPhysicalAddress,
        physicalAddress2 = rawPhysicalAddress2,
        physicalAddressRaw = rawPhysicalAddressRaw,
        city = rawCity,
        state = rawState,
        zip = rawZip,
        mobileNumber = rawMobileNumber,
        ip = rawIp,
        country = country
      ))
    )
  }
}
