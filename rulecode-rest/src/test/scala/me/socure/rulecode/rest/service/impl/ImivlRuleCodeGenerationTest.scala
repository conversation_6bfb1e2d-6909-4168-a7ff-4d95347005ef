package me.socure.rulecode.rest.service.impl

import me.socure.rulecode.common.models.{CategoricalRuleCode, NumericalRuleCode, RuleCodeResponse}
import me.socure.rulecode.common.utilities.DateUtility
import me.socure.rulecode.rest.utilities.RulecodeRequestUtilility
import org.apache.commons.csv.{CSVFormat, CSVParser}
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization
import org.json4s.native.JsonMethods.parse
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{FreeSpec, Matchers}

import java.io.FileInputStream
import java.nio.charset.StandardCharsets
import scala.collection.JavaConverters.asScalaBufferConverter
import scala.collection.immutable.Seq

class ImivlRuleCodeGenerationTest extends FreeSpec with Matchers with ScalaFutures {

  private val Generator = new TestRuleCodeGeneratorV2
  implicit val format = DefaultFormats



  private val RuleCodesSet1 = List(
   // ("IMIVL.100000", "N"), // UTs are missing
    ("IMIVL.100001", "C"),
    ("IMIVL.100002", "C"),
    ("IMIVL.100003", "C"),
    ("IMIVL.100004", "C"),
    ("IMIVL.100005", "C"),
    ("IMIVL.100006", "C"),
    ("IMIVL.100007", "C"),
    ("IMIVL.100008", "N"),
    ("IMIVL.100009", "C"),
    ("IMIVL.100010", "C"),
    ("IMIVL.100011", "C"),
    ("IMIVL.100012", "N"),
    ("IMIVL.100013", "N"),
    ("IMIVL.100014", "C"),
    ("IMIVL.100015", "C"),
    ("IMIVL.100016", "C"),
    ("IMIVL.100017", "C"),
    ("IMIVL.100019", "N"),
    ("IMIVL.100020", "C"),
    ("IMIVL.100021", "C"),
    ("IMIVL.100024", "C"),
    ("IMIVL.100025", "C"),
    ("IMIVL.100026", "C"),
    ("IMIVL.100027", "C"),
    ("IMIVL.100028", "C"),
    ("IMIVL.100029", "C"),
    ("IMIVL.100030", "C"),
    ("IMIVL.100031", "C"),
    ("IMIVL.100032", "C"),
    ("IMIVL.100033", "C"),
    ("IMIVL.100034", "C"),
    ("IMIVL.100035", "C"),
    ("IMIVL.100036", "C"),
    ("IMIVL.100037", "C"),
    ("IMIVL.100038", "C"),
    ("IMIVL.100039", "C"),
    ("IMIVL.100040", "N"),

    ("IMIVL.900001", "C"),
    ("IMIVL.900004", "C"),
    ("IMIVL.900005", "C"),
    ("IMIVL.900006", "C"),
    ("IMIVL.900007", "C"),
    ("IMIVL.900008", "C"),
    ("IMIVL.900009", "C"),
    ("IMIVL.900010", "C"),
    ("IMIVL.900011", "C"),
    ("IMIVL.900012", "C"),
    ("IMIVL.900013", "C"),
    ("IMIVL.900014", "C"),
    ("IMIVL.900015", "C"),
    ("IMIVL.900017", "C"),
    ("IMIVL.900018", "C")
  )

  "IMIVL rulecodes Set 1 " - {

    RuleCodesSet1 foreach {
      case (ruleCode, rcType) => {
        val testStream = new FileInputStream(getClass.getClassLoader.getResource(s"test_files/IMIVL/IMIVL_1000XX_9000XX.csv").getFile)
        val csvReader = CSVParser.parse(testStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
        csvReader
          .getRecords
          .asScala.map(row => {

            val firstName = Option(row.get(1)).filter(_ != "NA")
            val surName = Option(row.get(2)).filter(_ != "NA")
            val mobileNumber = Option(row.get(3)).filter(_ != "NA")
            val phoneNumber = Some(mobileNumber.get.substring(3))
            val phoneCountryCode = Some(mobileNumber.get.substring(0,3))
            val fullName = Option(row.get(4)).filter(_ != "NA")
            val response = Option(row.get(5)).filter(_ != "NA")
            // val submissionDate = Option(row.get(1)).filter(_ != "NA").flatMap(getDateMillis)
            // val txnDate = Option(row.get(2)).filter(_ != "NA").flatMap(getDateMillis)
            var expected = Option(row.get(ruleCode)).filter(_ != "NA").filter(_!="").filter(_!=null)

            val serviceResponse = response match {
              case Some(res) =>
                parse(
                  s"""{
                     |"responses": [{
                     |   "response" : $res
                     |}],
                     |}""".stripMargin)
              case _ =>
                val res = null
                parse(
                  s"""{
                     |"responses": [{
                     |   "response" : $res
                     |}],
                     |}""".stripMargin)
            }

            if(expected.isDefined && expected.get.contains("[")){
              // TODO : Check it
              expected = Some(expected.get.replace("[","").replace("]","").replace("\"","").replace(", ",",")).filter(_ != "NA").filter(_!="").filter(_!=null)
              if(expected.isDefined && expected.get.equalsIgnoreCase(",")) {
                expected = None
              }
              if(expected.isDefined && expected.get.startsWith(",")) {
                expected = Some(expected.get.substring(1))
              }
            }

            s"[ ${ruleCode} - ${row.getRecordNumber} ] - $expected" in {
               val request = RulecodeRequestUtilility.aRuleCodeRequest(firstName = firstName, lastName = surName, phoneNumber = phoneNumber,
                 phoneCountryCode = phoneCountryCode, fullName = fullName
               )
               Generator.generate(request, ruleCode, Serialization.write(serviceResponse)) match {
                 case Some(r) =>
                   r.rcType shouldBe rcType
                   println(r)
                   if(rcType.equalsIgnoreCase("N")){
                     r.value.toDouble shouldBe expected.get.toDouble
                   }else{
                     r.value shouldBe expected.get
                   }
                 case None => expected shouldBe None
               }
             }
           }
          )
      }
      case _ => None
    }
  }

  private val RulecodesSet2 = List(
    ("IMIVL.200005", "N"),
    ("IMIVL.900002", "C"),
    ("IMIVL.900003", "C"),
    ("IMIVL.900019", "C")

    //("IMIVL.200012", "N") // UTs are missing
    //("IMIVL.200013", "N") // UTs are missing
    // ("IMIVL.200006", "N") // Issue with UTs. For first case, lastName will be "da silva" not "silva". same for some other cases as well
    )

  "IMIVL rulecodes Set 2" - {

    RulecodesSet2 foreach {
      case (ruleCode, rcType) => {
        val testStream = new FileInputStream(getClass.getClassLoader.getResource(s"test_files/IMIVL/IMIVL_2000XX.csv").getFile)
        val csvReader = CSVParser.parse(testStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
        csvReader
          .getRecords
          .asScala.map(row => {

            val submissionDate = Option(row.get(1)).filter(_ != "NA").flatMap(getDateMillis)
            val firstName = Option(row.get(3)).filter(_ != "NA")
            val surName = Option(row.get(4)).filter(_ != "NA")
            val email = Option(row.get(5)).filter(_ != "NA")
            val mobileNumber = Option(row.get(6)).filter(_ != "NA")
            val physicalAddress = Option(row.get(7)).filter(_ != "NA")
            val physicalAddress2 = Option(row.get(8)).filter(_ != "NA")
            val city = Option(row.get(9)).filter(_ != "NA")
            val state = Option(row.get(10)).filter(_ != "NA")
            val zip = Option(row.get(11)).filter(_ != "NA")
            val country = Option(row.get(12)).filter(_ != "NA")
            val ipAddress = Option(row.get(13)).filter(_ != "NA")
            val nationalID = Option(row.get(14)).filter(_ != "NA")
            val dob = Option(row.get(15)).filter(_ != "NA")
            val phoneNumber = Option(row.get(16)).filter(_ != "NA")
            val phoneCountryCode = Option(row.get(17)).filter(_ != "NA")
            val fullName = Option(row.get(21)).filter(_ != "NA")

            val response = Option(row.get(22)).filter(_ != "NA")


            val serviceResponse = response match {
              case Some(res) =>
                parse(
                  s"""{
                     |"responses": [{
                     |   "response" : $res
                     |}],
                     |}""".stripMargin)
              case _ =>
                val res = null
                parse(
                  s"""{
                     |"responses": [{
                     |   "response" : $res
                     |}],
                     |}""".stripMargin)
            }

            var expected = Option(row.get(ruleCode)).filter(_ != "NA").filter(_!="").filter(_!=null)

            if(expected.isDefined && expected.get.contains("[")){
              expected = Some(expected.get.replace("[","").replace("]","").replace("\"","").replace(", ",",")).filter(_ != "NA").filter(_!="").filter(_!=null)
              if(expected.isDefined && expected.get.equalsIgnoreCase(",")) {
                expected = None
              }
              if(expected.isDefined && expected.get.startsWith(",")) {
                expected = Some(expected.get.substring(1))
              }
            }

            s"[ ${ruleCode} - ${row.getRecordNumber} ] - $expected" in {
              val request = RulecodeRequestUtilility.aRuleCodeRequest(
                rawSubmissionDate = submissionDate,
                firstName = firstName,
                lastName = surName,
                rawEmail = email,
                rawMobileNumber = mobileNumber,
                rawPhysicalAddress = physicalAddress,
                rawPhysicalAddress2 = physicalAddress2,
                city = city,
                state = state,
                zip = zip,
                country = country,
                ip = ipAddress,
                ssn = nationalID,
                rawDobString = dob,
                phoneNumber = phoneNumber,
                phoneCountryCode = phoneCountryCode,
                fullName = fullName
              )
              Generator.generate(request, ruleCode, Serialization.write(serviceResponse)) match {
                case Some(r) =>
                  expected match {
                    case Some(ex) =>
                      if(rcType.equalsIgnoreCase("N")){
                        r.value.toDouble shouldBe ex.toDouble
                      }else{
                        r.value shouldBe ex
                      }
                    case None => expected shouldBe None
                  }
                case None => expected shouldBe None
              }
            }
          }
          )
      }
      case _ => None
    }
  }


  private val DerivedRuleCodes = List(
    Seq(("IMIVL.200002", "N"),("IMIVL.900010", "C")),
    Seq(("IMIVL.200004", "N"),("IMIVL.900012", "C")),
    Seq(("IMIVL.200007", "N"),("IMIVL.900017", "C")),
    Seq(("IMIVL.200008", "N"),("IMIVL.900018", "C")) ,
      Seq(("IMIVL.200009", "N"),("IMIVL.900011", "C")), // Check other rulecodes as well, convert them into lowercase. check IMIVL.200009
    Seq(("IMIVL.200010", "N"),("IMIVL.900013", "C")),
    Seq(("IMIVL.200015", "N"),("IMIVL.900014", "C")),
      Seq(("IMIVL.100018", "N"),("IMIVL.100017", "C")),
      Seq(("IMIVL.100022", "N"),("IMIVL.100020", "C")),
      Seq(("IMIVL.100023", "N"),("IMIVL.100021", "C")),
      Seq(("IMIVL.100042", "N"),("IMIVL.100010", "C"))

      // Seq(("IMIVL.100041", "N"),("IMIVL.200002", "N")), // There are some failures which are expected
      //**  Seq(("IMIVL.200017", "N"),("IMIVL.900001", "C")) // getting significant change in the given expected result in csv and actual Maximum Ratcliff/Obershelp score
      //**  Seq(("IMIVL.200019", "N"),("IMIVL.900004", "C")) // getting significant change in the given expected result in csv and actual Maximum Ratcliff/Obershelp score
      //**  Seq(("IMIVL.200021", "N"),("IMIVL.900006", "C")) // getting significant change in the given expected result in csv and actual Maximum Ratcliff/Obershelp score
      // Seq(("IMIVL.200022", "N"),("IMIVL.900004", "C")) // looks like UTs are wrong. expected output for IMIVL.200022 is 0.0 or 1.0
      // Seq(("IMIVL.200023", "N"),("IMIVL.900006", "C")) // looks like UTs are wrong. expected output for IMIVL.200023 is 0.0 or 1.0
      // Seq(("IMIVL.200016", "N"),("IMIVL.200012", "N"),("IMIVL.200001", "N")),  //  IMIVL.200012 Missing in the UT file
      // Seq(("IMIVL.200018", "N"),("IMIVL.200012", "N"),("IMIVL.200005", "N")),  //  IMIVL.200012 Missing in the UT file
      // Seq(("IMIVL.200020", "N"),("IMIVL.200012", "N"),("IMIVL.200006", "N")),  //  IMIVL.200012 Missing in the UT file

    )
  "IMIVL Derived rulecodes" - {

    DerivedRuleCodes foreach {
      case seq => {
        val ruleCode = seq.head._1
        val rcType = seq.head._2
        val dependeeRuleCodes = seq.tail
        val testStream = new FileInputStream(getClass.getClassLoader.getResource(s"test_files/IMIVL/IMIVL_2000XX.csv").getFile)
        val csvReader = CSVParser.parse(testStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
        csvReader
          .getRecords
          .asScala.map(row => {

            val submissionDate = Option(row.get(1)).filter(_ != "NA").flatMap(getDateMillis)
            val firstName = Option(row.get(3)).filter(_ != "NA")
            val surName = Option(row.get(4)).filter(_ != "NA")
            val email = Option(row.get(5)).filter(_ != "NA")
            val mobileNumber = Option(row.get(6)).filter(_ != "NA")
            val physicalAddress = Option(row.get(7)).filter(_ != "NA")
            val physicalAddress2 = Option(row.get(8)).filter(_ != "NA")
            val city = Option(row.get(9)).filter(_ != "NA")
            val state = Option(row.get(10)).filter(_ != "NA")
            val zip = Option(row.get(11)).filter(_ != "NA")
            val country = Option(row.get(12)).filter(_ != "NA")
            val ipAddress = Option(row.get(13)).filter(_ != "NA")
            val nationalID = Option(row.get(14)).filter(_ != "NA")
            val dob = Option(row.get(15)).filter(_ != "NA")
            val phoneNumber = Option(row.get(16)).filter(_ != "NA")
            val phoneCountryCode = Option(row.get(17)).filter(_ != "NA")
            val fullName = Option(row.get(21)).filter(_ != "NA")

            val response = Option(row.get(22)).filter(_ != "NA").getOrElse("{}")

            val lookupMap = Map(
              "http.idmerit" -> response
            )
            var expected = Option(row.get(ruleCode)).filter(_ != "NA").filter(_!="").filter(_!=null)

            if(expected.isDefined && expected.get.contains("[")){
              expected = Some(expected.get.replace("[","").replace("]","").replace("\"","").replace(", ",",")).filter(_ != "NA").filter(_!="").filter(_!=null)
              if(expected.isDefined && expected.get.equalsIgnoreCase(",")) {
                expected = None
              }
              if(expected.isDefined && expected.get.startsWith(",")) {
                expected = Some(expected.get.substring(1))
              }
            }

            var numericalRuleCodes = Seq.empty[NumericalRuleCode]
            var categoricalRuleCodes = Seq.empty[CategoricalRuleCode]

            dependeeRuleCodes.foreach{ rc =>
              var value = Option(row.get(rc._1)).filter(_ != "NA").filter(_!="").filter(_!=null)
              if(rc._2.equalsIgnoreCase("N")){
                numericalRuleCodes = numericalRuleCodes :+ NumericalRuleCode(rc._1,value.get.toDouble)
              }else{
                if(value.isDefined && value.get.contains("[")){
                  value = Some(value.get.replace("[","").replace("]","").replace("\"","").replace(", ",",")).filter(_ != "NA").filter(_!="").filter(_!=null)
                  if(value.isDefined && value.get.equalsIgnoreCase(",")) {
                    value = None
                  }
                  if(value.isDefined && value.get.startsWith(",")) {
                    value = Some(value.get.substring(1))
                  }
                }
                if(value.isDefined) {
                  categoricalRuleCodes = categoricalRuleCodes :+ CategoricalRuleCode(rc._1, value.get)
                }
              }
            }

            val ruleCodeResponse = RuleCodeResponse(
              numericalRuleCodes,
              categoricalRuleCodes,
              Seq.empty[String]
            )

            s"[ ${ruleCode} - ${row.getRecordNumber} ] - $expected" in {
              val request = RulecodeRequestUtilility.aRuleCodeRequest(
                rawSubmissionDate = submissionDate,
                firstName = firstName,
                lastName = surName,
                rawEmail = email,
                rawMobileNumber = mobileNumber,
                rawPhysicalAddress = physicalAddress,
                rawPhysicalAddress2 = physicalAddress2,
                city = city,
                state = state,
                zip = zip,
                country = country,
                ip = ipAddress,
                ssn = nationalID,
                rawDobString = dob,
                phoneNumber = phoneNumber,
                phoneCountryCode = phoneCountryCode,
                fullName = fullName
              )
              Generator.generate(request, ruleCode, "", lookupMap, processDerived = true, combinedRuleCodeResponse = ruleCodeResponse) match {
                case Some(r) =>
                  r.rcType shouldBe rcType
                  expected match {
                    case Some(ex) =>
                      if(rcType.equalsIgnoreCase("N")){
                        r.value.toDouble shouldBe ex.toDouble
                      }else{
                        r.value shouldBe ex
                      }
                    case None => expected shouldBe None
                  }
                case None => expected shouldBe None
              }
            }
          }
          )
      }
      case _ => None
    }
  }

  private def getDateMillis(dateStrVal: String): Option[Long] = {
    dateStrVal match {
      case dateStr if dateStr.trim.nonEmpty => DateUtility.getDateTime(dateStr.trim).map(_.getMillis)
      case _ => None
    }
  }
}
