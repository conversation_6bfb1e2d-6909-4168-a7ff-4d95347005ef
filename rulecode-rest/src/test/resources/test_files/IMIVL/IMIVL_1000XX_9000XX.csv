customerUserId,firstName,surName,mobileNumber,fullName,response,response_time_ms,result,status_code,message,profile,trans_id,request_id,profile.0.result,profile.0.status_code,profile.0.data.persondetails,profile.0.data.fullname,profile.0.data.phone_number,profile.0.data.risk_score,profile.0.data.signals,profile.0.data.phone_details.type1,profile.0.data.phone_details.type2,profile.0.data.phone_details.country_code,profile.0.data.phone_details.status,profile.0.data.phone_details.disposable,profile.0.data.phone_details.disposable_provider,profile.0.data.phone_details.valid,profile.0.data.phone_details.suspicious_format,profile.0.data.phone_details.valid_format,profile.0.data.phone_details.portability_details.status,profile.0.data.phone_details.portability_details.first,profile.0.data.phone_details.portability_details.last,profile.0.data.phone_details.portability_details.times,profile.0.data.phone_details.original_carrier,profile.0.data.phone_details.current_carrier,profile.0.data.phone_details.first_seen,profile.0.data.phone_details.breach_details.count,profile.0.data.phone_details.breach_details.first_breach,profile.0.data.phone_details.breach_details.last_breach,profile.0.data.phone_details.breach_details.list,profile.0.data.phone_details.breach_details.data,profile.0.data.phone_details.breach_details.data_lists,profile.0.source,profile.0.data.persondetails.0.address1,profile.0.data.persondetails.0.address2,profile.0.data.persondetails.0.address3,profile.0.data.persondetails.0.city,profile.0.data.persondetails.0.dob,profile.0.data.persondetails.0.gender,profile.0.data.persondetails.0.id_num,profile.0.data.persondetails.0.name,profile.0.data.persondetails.0.opening_at,profile.0.data.persondetails.0.state,profile.0.data.persondetails.0.type,profile.0.data.persondetails.0.zip,profile.0.data.persondetails.1.address1,profile.0.data.persondetails.1.address2,profile.0.data.persondetails.1.address3,profile.0.data.persondetails.1.city,profile.0.data.persondetails.1.dob,profile.0.data.persondetails.1.gender,profile.0.data.persondetails.1.id_num,profile.0.data.persondetails.1.name,profile.0.data.persondetails.1.opening_at,profile.0.data.persondetails.1.state,profile.0.data.persondetails.1.type,profile.0.data.persondetails.1.zip,profile.0.data.persondetails.2.city,profile.0.data.persondetails.2.dob,profile.0.data.persondetails.2.gender,profile.0.data.persondetails.2.id_num,profile.0.data.persondetails.2.name,profile.0.data.persondetails.2.state,profile.0.data.persondetails.2.type,transactionDate,IMIVL.100001,IMIVL.100002,IMIVL.100003,IMIVL.100004,IMIVL.100005,IMIVL.100006,IMIVL.100007,IMIVL.100008,IMIVL.100009,IMIVL.100010,IMIVL.100011,IMIVL.100042,IMIVL.100012,IMIVL.100013,IMIVL.100014,IMIVL.100015,IMIVL.100016,IMIVL.100017,IMIVL.100018,IMIVL.100019,IMIVL.100020,IMIVL.100021,IMIVL.100022,IMIVL.100023,IMIVL.100024,IMIVL.100025,IMIVL.100026,IMIVL.100027,IMIVL.100028,IMIVL.100029,IMIVL.100030,IMIVL.100031,IMIVL.100032,IMIVL.100033,IMIVL.100034,IMIVL.100035,IMIVL.100036,IMIVL.100037,IMIVL.100038,IMIVL.100039,IMIVL.100040,IMIVL.900001,IMIVL.900002,IMIVL.900003,IMIVL.900004,IMIVL.900005,IMIVL.900006,IMIVL.900007,IMIVL.900008,IMIVL.900009,IMIVL.900010,IMIVL.900011,IMIVL.900012,IMIVL.900013,IMIVL.900014,IMIVL.900015,IMIVL.900017,IMIVL.900018,IMIVL.900019,IMIVL.200016,IMIVL.200017,IMIVL.200018,IMIVL.200019,IMIVL.200020,IMIVL.200021,IMIVL.200022,IMIVL.200023,IMIVL.200024,IMIVL.200025,IMIVL.200026
c91eeb45-fb31-449d-880c-1d11e83fef42,Luciano,Rocha,+5592984664238,Luciano Rocha,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""LUCIANO OLIVEIRA ROCHA"", ""dob"": ""1988-06-13"", ""gender"": ""M"", ""city"": ""CAMOCIM"", ""state"": ""CE"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555592984664238"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""e2d7739e-c844-417a-9214-95573339d291"", ""request_id"": ""c91eeb45-fb31-449d-880c-1d11e83fef42""}",3209.18,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""LUCIANO OLIVEIRA ROCHA"", ""dob"": ""1988-06-13"", ""gender"": ""M"", ""city"": ""CAMOCIM"", ""state"": ""CE"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555592984664238"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",e2d7739e-c844-417a-9214-95573339d291,c91eeb45-fb31-449d-880c-1d11e83fef42,true,200,"[{""id_num"": ""**********"", ""name"": ""LUCIANO OLIVEIRA ROCHA"", ""dob"": ""1988-06-13"", ""gender"": ""M"", ""city"": ""CAMOCIM"", ""state"": ""CE"", ""type"": ""person""}]",,555592984664238,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,CAMOCIM,1988-06-13,M,**********,LUCIANO OLIVEIRA ROCHA,,CE,person,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[LUCIANO OLIVEIRA ROCHA],[LUCIANO OLIVEIRA ROCHA],[],[LUCIANO],[OLIVEIRA],[ROCHA],,,,[1988-06-13],[CAMOCIM],[],[CE],[**********],[M],[],[],[**********],0.0,0.74,1.0,1.0,1.0,1.0,1.0,1.0,0.95,2.0,2.0
4bf1a3fa-9bb9-4768-a737-8150f930028d,Felipe,Oliveira,+5521967264849,Felipe Oliveira,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555521967264849"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""26c95cc8-1b11-4397-b8d1-09b4b9c19381"", ""request_id"": ""4bf1a3fa-9bb9-4768-a737-8150f930028d""}",3086.51,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555521967264849"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",26c95cc8-1b11-4397-b8d1-09b4b9c19381,4bf1a3fa-9bb9-4768-a737-8150f930028d,true,200,[],,555521967264849,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0
fb8d2efb-15ca-4c57-8794-9fb9f8ec0075,Debora,Silveira,+5548999264556,Debora Silveira,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""80059643900"", ""name"": ""DEBORA REGINA SILVEIRA"", ""dob"": ""1973-01-07"", ""gender"": ""F"", ""city"": ""FLORIANOPOLIS"", ""state"": ""SC"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555548999264556"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""d8010d29-85f0-4ac2-b34c-83872ee8e93a"", ""request_id"": ""fb8d2efb-15ca-4c57-8794-9fb9f8ec0075""}",3035.24,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""80059643900"", ""name"": ""DEBORA REGINA SILVEIRA"", ""dob"": ""1973-01-07"", ""gender"": ""F"", ""city"": ""FLORIANOPOLIS"", ""state"": ""SC"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555548999264556"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",d8010d29-85f0-4ac2-b34c-83872ee8e93a,fb8d2efb-15ca-4c57-8794-9fb9f8ec0075,true,200,"[{""id_num"": ""80059643900"", ""name"": ""DEBORA REGINA SILVEIRA"", ""dob"": ""1973-01-07"", ""gender"": ""F"", ""city"": ""FLORIANOPOLIS"", ""state"": ""SC"", ""type"": ""person""}]",,555548999264556,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,FLORIANOPOLIS,1973-01-07,F,80059643900,DEBORA REGINA SILVEIRA,,SC,person,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[DEBORA REGINA SILVEIRA],[DEBORA REGINA SILVEIRA],[],[DEBORA],[REGINA],[SILVEIRA],,,,[1973-01-07],[FLORIANOPOLIS],[],[SC],[80059643900],[F],[],[],[80059643900],0.0,0.81,1.0,1.0,1.0,1.0,1.0,1.0,0.95,2.0,2.0
8ddbed9c-9a47-43a2-8505-1ae027c55b48,Clea,Araújo,+5582999385928,Clea Araújo,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""ALDICLEIA DA SILVA ARAUJO"", ""dob"": ""1990-11-11"", ""gender"": ""F"", ""city"": ""ARAPIRACA"", ""state"": ""AL"", ""type"": ""person""}, {""id_num"": ""42961040000140"", ""name"": ""ALDICLEIA DA SILVA ARAUJO ***********"", ""opening_at"": ""2021-08-02"", ""address1"": ""R ARAPIRACA CJ H JD DAS PAINEIRAS"", ""address2"": ""320"", ""address3"": ""SENADOR NILO COELHO"", ""city"": ""ARAPIRACA"", ""state"": ""AL"", ""zip"": ""57309504"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555582999385928"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""43c976d3-6c7b-4a94-946c-24dd8ff39f71"", ""request_id"": ""8ddbed9c-9a47-43a2-8505-1ae027c55b48""}",3230.88,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""ALDICLEIA DA SILVA ARAUJO"", ""dob"": ""1990-11-11"", ""gender"": ""F"", ""city"": ""ARAPIRACA"", ""state"": ""AL"", ""type"": ""person""}, {""id_num"": ""42961040000140"", ""name"": ""ALDICLEIA DA SILVA ARAUJO ***********"", ""opening_at"": ""2021-08-02"", ""address1"": ""R ARAPIRACA CJ H JD DAS PAINEIRAS"", ""address2"": ""320"", ""address3"": ""SENADOR NILO COELHO"", ""city"": ""ARAPIRACA"", ""state"": ""AL"", ""zip"": ""57309504"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555582999385928"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",43c976d3-6c7b-4a94-946c-24dd8ff39f71,8ddbed9c-9a47-43a2-8505-1ae027c55b48,true,200,"[{""id_num"": ""**********"", ""name"": ""ALDICLEIA DA SILVA ARAUJO"", ""dob"": ""1990-11-11"", ""gender"": ""F"", ""city"": ""ARAPIRACA"", ""state"": ""AL"", ""type"": ""person""}, {""id_num"": ""42961040000140"", ""name"": ""ALDICLEIA DA SILVA ARAUJO ***********"", ""opening_at"": ""2021-08-02"", ""address1"": ""R ARAPIRACA CJ H JD DAS PAINEIRAS"", ""address2"": ""320"", ""address3"": ""SENADOR NILO COELHO"", ""city"": ""ARAPIRACA"", ""state"": ""AL"", ""zip"": ""57309504"", ""type"": ""business""}]",,555582999385928,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,ARAPIRACA,1990-11-11,F,**********,ALDICLEIA DA SILVA ARAUJO,,AL,person,,R ARAPIRACA CJ H JD DAS PAINEIRAS,320,SENADOR NILO COELHO,ARAPIRACA,,,42961040000140,ALDICLEIA DA SILVA ARAUJO ***********,2021-08-02,AL,business,57309504,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[ALDICLEIA DA SILVA ARAUJO"", ""ALDICLEIA DA SILVA ARAUJO ***********]",[ALDICLEIA DA SILVA ARAUJO],[ALDICLEIA DA SILVA ARAUJO ***********],"[ALDICLEIA"", ""ALDICLEIA]","["", ""]","[DA SILVA ARAUJO"", ""DA SILVA ARAUJO ***********]",,,,[1990-11-11],"[ARAPIRACA"", ""ARAPIRACA]",[57309504],"[AL"", ""AL]","[**********"", ""42961040000140]",[F],[R ARAPIRACA CJ H JD DAS PAINEIRAS],[320],[**********],0.0,0.57,0.0,0.62,0.0,0.5,0.62,0.5,0.03,,
9300b96b-82f3-4986-a0e5-babb794cdb7a,Lígia,Miranda,+5512991472361,Lígia Miranda,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555512991472361"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""3450b060-eaef-4dd3-a73f-c7ac6d0aec42"", ""request_id"": ""9300b96b-82f3-4986-a0e5-babb794cdb7a""}",2535.2,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555512991472361"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",3450b060-eaef-4dd3-a73f-c7ac6d0aec42,9300b96b-82f3-4986-a0e5-babb794cdb7a,true,200,[],,555512991472361,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0
e552952c-405c-4266-9ece-99397d381ca5,Talita,Bernardo,+5511975876331,Talita Bernardo,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""JOANA LIBANIO DOS SANTOS"", ""dob"": ""1945-03-15"", ""gender"": ""F"", ""city"": ""FERRAZ DE VASCONCELOS"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""30509471870"", ""name"": ""TALITA APARECIDA DA SILVA BERNARDO"", ""dob"": ""1984-03-17"", ""gender"": ""F"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511975876331"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""e7322335-30c5-4d50-bad0-3fb599388285"", ""request_id"": ""e552952c-405c-4266-9ece-99397d381ca5""}",3060.64,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""JOANA LIBANIO DOS SANTOS"", ""dob"": ""1945-03-15"", ""gender"": ""F"", ""city"": ""FERRAZ DE VASCONCELOS"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""30509471870"", ""name"": ""TALITA APARECIDA DA SILVA BERNARDO"", ""dob"": ""1984-03-17"", ""gender"": ""F"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511975876331"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",e7322335-30c5-4d50-bad0-3fb599388285,e552952c-405c-4266-9ece-99397d381ca5,true,200,"[{""id_num"": ""**********"", ""name"": ""JOANA LIBANIO DOS SANTOS"", ""dob"": ""1945-03-15"", ""gender"": ""F"", ""city"": ""FERRAZ DE VASCONCELOS"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""30509471870"", ""name"": ""TALITA APARECIDA DA SILVA BERNARDO"", ""dob"": ""1984-03-17"", ""gender"": ""F"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}]",,555511975876331,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,FERRAZ DE VASCONCELOS,1945-03-15,F,**********,JOANA LIBANIO DOS SANTOS,,SP,person,,,,,SAO PAULO,1984-03-17,F,30509471870,TALITA APARECIDA DA SILVA BERNARDO,,SP,person,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[JOANA LIBANIO DOS SANTOS"", ""TALITA APARECIDA DA SILVA BERNARDO]","[JOANA LIBANIO DOS SANTOS"", ""TALITA APARECIDA DA SILVA BERNARDO]",[],"[JOANA"", ""TALITA]","[LIBANIO"", ""APARECIDA]","[DOS SANTOS"", ""DA SILVA BERNARDO]",,,,"[1945-03-15"", ""1984-03-17]","[FERRAZ DE VASCONCELOS"", ""SAO PAULO]",[],"[SP"", ""SP]","[**********"", ""30509471870]","[F"", ""F]",[],[],"[**********"", ""30509471870]",0.0,0.61,1.0,1.0,1.0,0.64,1.0,0.64,0.95,2.0,2.0
8a5c48b5-d290-422d-a45b-b03890386788,Alan,Alves Dos Santos,+5521981489283,Alan Alves Dos Santos,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""67812236749"", ""name"": ""ALVAN ALVARES XAVIER"", ""dob"": ""1962-05-05"", ""gender"": null, ""city"": ""SAO GONCALO"", ""state"": ""RJ"", ""type"": ""person""}, {""id_num"": ""76203085391"", ""name"": ""FRANCISCO DEMONTIEUX PEREIRA DA SILVA"", ""dob"": ""1974-10-05"", ""gender"": ""M"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555521981489283"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""24d3bbc9-f043-4d9f-a78f-3bfa0a0f3bf7"", ""request_id"": ""8a5c48b5-d290-422d-a45b-b03890386788""}",2628.8,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""67812236749"", ""name"": ""ALVAN ALVARES XAVIER"", ""dob"": ""1962-05-05"", ""gender"": null, ""city"": ""SAO GONCALO"", ""state"": ""RJ"", ""type"": ""person""}, {""id_num"": ""76203085391"", ""name"": ""FRANCISCO DEMONTIEUX PEREIRA DA SILVA"", ""dob"": ""1974-10-05"", ""gender"": ""M"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555521981489283"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",24d3bbc9-f043-4d9f-a78f-3bfa0a0f3bf7,8a5c48b5-d290-422d-a45b-b03890386788,true,200,"[{""id_num"": ""67812236749"", ""name"": ""ALVAN ALVARES XAVIER"", ""dob"": ""1962-05-05"", ""gender"": null, ""city"": ""SAO GONCALO"", ""state"": ""RJ"", ""type"": ""person""}, {""id_num"": ""76203085391"", ""name"": ""FRANCISCO DEMONTIEUX PEREIRA DA SILVA"", ""dob"": ""1974-10-05"", ""gender"": ""M"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}]",,555521981489283,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,SAO GONCALO,1962-05-05,,67812236749,ALVAN ALVARES XAVIER,,RJ,person,,,,,RIO DE JANEIRO,1974-10-05,M,76203085391,FRANCISCO DEMONTIEUX PEREIRA DA SILVA,,RJ,person,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[ALVAN ALVARES XAVIER"", ""FRANCISCO DEMONTIEUX PEREIRA DA SILVA]","[ALVAN ALVARES XAVIER"", ""FRANCISCO DEMONTIEUX PEREIRA DA SILVA]",[],"[ALVAN"", ""FRANCISCO]","[ALVARES"", ""DEMONTIEUX PEREIRA]","[XAVIER"", ""DA SILVA]",,,,"[1962-05-05"", ""1974-10-05]","[SAO GONCALO"", ""RIO DE JANEIRO]",[],"[RJ"", ""RJ]","[67812236749"", ""76203085391]",[M],[],[],"[67812236749"", ""76203085391]",0.0,0.59,1.0,0.89,0.0,0.33,0.89,0.33,0.72,2.0,
2b67a6ba-2da7-4e29-a8b6-3952bd9482aa,Carine,Medina,+5551980504928,Carine Medina,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""46412050"", ""name"": ""CARINE SCHULTZ GOULART MEDINA"", ""dob"": ""1977-12-20"", ""gender"": ""F"", ""city"": ""GRAVATAI"", ""state"": ""RS"", ""type"": ""person""}, {""id_num"": ""**********"", ""name"": ""GUSTAVO GOULART MEDINA"", ""dob"": ""2005-09-20"", ""gender"": ""M"", ""city"": ""GRAVATAI"", ""state"": ""RS"", ""type"": ""person""}, {""id_num"": ""**********"", ""name"": ""GUILHERME GOULART MEDINA"", ""dob"": ""2018-01-10"", ""gender"": ""M"", ""city"": ""GRAVATAI"", ""state"": ""RS"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555551980504928"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""dba8627b-2ecb-427f-b8fd-7d9506abf6ea"", ""request_id"": ""2b67a6ba-2da7-4e29-a8b6-3952bd9482aa""}",2672.19,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""46412050"", ""name"": ""CARINE SCHULTZ GOULART MEDINA"", ""dob"": ""1977-12-20"", ""gender"": ""F"", ""city"": ""GRAVATAI"", ""state"": ""RS"", ""type"": ""person""}, {""id_num"": ""**********"", ""name"": ""GUSTAVO GOULART MEDINA"", ""dob"": ""2005-09-20"", ""gender"": ""M"", ""city"": ""GRAVATAI"", ""state"": ""RS"", ""type"": ""person""}, {""id_num"": ""**********"", ""name"": ""GUILHERME GOULART MEDINA"", ""dob"": ""2018-01-10"", ""gender"": ""M"", ""city"": ""GRAVATAI"", ""state"": ""RS"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555551980504928"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",dba8627b-2ecb-427f-b8fd-7d9506abf6ea,2b67a6ba-2da7-4e29-a8b6-3952bd9482aa,true,200,"[{""id_num"": ""46412050"", ""name"": ""CARINE SCHULTZ GOULART MEDINA"", ""dob"": ""1977-12-20"", ""gender"": ""F"", ""city"": ""GRAVATAI"", ""state"": ""RS"", ""type"": ""person""}, {""id_num"": ""**********"", ""name"": ""GUSTAVO GOULART MEDINA"", ""dob"": ""2005-09-20"", ""gender"": ""M"", ""city"": ""GRAVATAI"", ""state"": ""RS"", ""type"": ""person""}, {""id_num"": ""**********"", ""name"": ""GUILHERME GOULART MEDINA"", ""dob"": ""2018-01-10"", ""gender"": ""M"", ""city"": ""GRAVATAI"", ""state"": ""RS"", ""type"": ""person""}]",,555551980504928,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,GRAVATAI,1977-12-20,F,46412050,CARINE SCHULTZ GOULART MEDINA,,RS,person,,,,,GRAVATAI,2005-09-20,M,**********,GUSTAVO GOULART MEDINA,,RS,person,,GRAVATAI,2018-01-10,M,**********,GUILHERME GOULART MEDINA,RS,person,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[CARINE SCHULTZ GOULART MEDINA"", ""GUSTAVO GOULART MEDINA"", ""GUILHERME GOULART MEDINA]","[CARINE SCHULTZ GOULART MEDINA"", ""GUSTAVO GOULART MEDINA"", ""GUILHERME GOULART MEDINA]",[],"[CARINE"", ""GUSTAVO"", ""GUILHERME]","[SCHULTZ GOULART"", ""GOULART"", ""GOULART]","[MEDINA"", ""MEDINA"", ""MEDINA]",,,,"[1977-12-20"", ""2005-09-20"", ""2018-01-10]","[GRAVATAI"", ""GRAVATAI"", ""GRAVATAI]",[],"[RS"", ""RS"", ""RS]","[46412050"", ""**********"", ""**********]","[F"", ""M"", ""M]",[],[],"[46412050"", ""**********"", ""**********]",0.0,0.62,1.0,1.0,1.0,1.0,1.0,1.0,0.95,2.0,2.0
851a9c8c-17f9-4627-a967-6cb9a3b90f74,Vera,Lucia,+5519981103880,Vera Lucia,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""48510834830"", ""name"": ""LARISSA NUNES DUTRA"", ""dob"": ""1997-04-13"", ""gender"": ""F"", ""city"": ""CAMPINAS"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555519981103880"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""9b36f5a8-963e-4a36-9c53-16749914a737"", ""request_id"": ""851a9c8c-17f9-4627-a967-6cb9a3b90f74""}",2627.78,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""48510834830"", ""name"": ""LARISSA NUNES DUTRA"", ""dob"": ""1997-04-13"", ""gender"": ""F"", ""city"": ""CAMPINAS"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555519981103880"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",9b36f5a8-963e-4a36-9c53-16749914a737,851a9c8c-17f9-4627-a967-6cb9a3b90f74,true,200,"[{""id_num"": ""48510834830"", ""name"": ""LARISSA NUNES DUTRA"", ""dob"": ""1997-04-13"", ""gender"": ""F"", ""city"": ""CAMPINAS"", ""state"": ""SP"", ""type"": ""person""}]",,555519981103880,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,CAMPINAS,1997-04-13,F,48510834830,LARISSA NUNES DUTRA,,SP,person,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[LARISSA NUNES DUTRA],[LARISSA NUNES DUTRA],[],[LARISSA],[NUNES],[DUTRA],,,,[1997-04-13],[CAMPINAS],[],[SP],[48510834830],[F],[],[],[48510834830],0.0,0.34,0.0,0.36,0.0,0.4,0.36,0.4,0.03,,
c72ca8f9-bad8-4b70-8428-3119f8b9efd3,Bianca,Soares,+5531992176205,Bianca Soares,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""55749810000137"", ""name"": ""55 749 810 BIANCA SOARES AZEVEDO"", ""opening_at"": ""2024-07-01"", ""address1"": ""R PAULO PAPINI"", ""address2"": ""416"", ""address3"": ""PARAISO"", ""city"": ""BELO HORIZONTE"", ""state"": ""MG"", ""zip"": ""30270400"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555531992176205"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""1739dd5e-287f-4e7a-9234-322a05cb88dd"", ""request_id"": ""c72ca8f9-bad8-4b70-8428-3119f8b9efd3""}",2600.3,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""55749810000137"", ""name"": ""55 749 810 BIANCA SOARES AZEVEDO"", ""opening_at"": ""2024-07-01"", ""address1"": ""R PAULO PAPINI"", ""address2"": ""416"", ""address3"": ""PARAISO"", ""city"": ""BELO HORIZONTE"", ""state"": ""MG"", ""zip"": ""30270400"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555531992176205"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",1739dd5e-287f-4e7a-9234-322a05cb88dd,c72ca8f9-bad8-4b70-8428-3119f8b9efd3,true,200,"[{""id_num"": ""55749810000137"", ""name"": ""55 749 810 BIANCA SOARES AZEVEDO"", ""opening_at"": ""2024-07-01"", ""address1"": ""R PAULO PAPINI"", ""address2"": ""416"", ""address3"": ""PARAISO"", ""city"": ""BELO HORIZONTE"", ""state"": ""MG"", ""zip"": ""30270400"", ""type"": ""business""}]",,555531992176205,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,R PAULO PAPINI,416,PARAISO,BELO HORIZONTE,,,55749810000137,55 749 810 BIANCA SOARES AZEVEDO,2024-07-01,MG,business,30270400,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[55 749 810 BIANCA SOARES AZEVEDO],[],[55 749 810 BIANCA SOARES AZEVEDO],[55],[749 810 BIANCA SOARES],[AZEVEDO],,,,[],[BELO HORIZONTE],[30270400],[MG],[55749810000137],[],[R PAULO PAPINI],[416],[],1.0,0.58,0.0,0.0,0.0,0.31,0.0,0.31,0.98,-1.0,
e2fa7816-9933-4ba7-9457-555ebbfff418,Rosemeire,Castro,+5511992733455,Rosemeire Castro,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""***********"", ""name"": ""DALVA DE ALMEIDA"", ""dob"": ""1955-05-08"", ""gender"": ""F"", ""city"": ""OSASCO"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""***********"", ""name"": ""ROSEMEIRE DE CASTRO PEREIRA"", ""dob"": ""1978-11-23"", ""gender"": ""F"", ""city"": ""JANDIRA"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511992733455"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""0f84c1a8-0e6f-4f0f-ace4-3a9bfa75ec9c"", ""request_id"": ""e2fa7816-9933-4ba7-9457-555ebbfff418""}",2511.35,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""***********"", ""name"": ""DALVA DE ALMEIDA"", ""dob"": ""1955-05-08"", ""gender"": ""F"", ""city"": ""OSASCO"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""***********"", ""name"": ""ROSEMEIRE DE CASTRO PEREIRA"", ""dob"": ""1978-11-23"", ""gender"": ""F"", ""city"": ""JANDIRA"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511992733455"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",0f84c1a8-0e6f-4f0f-ace4-3a9bfa75ec9c,e2fa7816-9933-4ba7-9457-555ebbfff418,true,200,"[{""id_num"": ""***********"", ""name"": ""DALVA DE ALMEIDA"", ""dob"": ""1955-05-08"", ""gender"": ""F"", ""city"": ""OSASCO"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""***********"", ""name"": ""ROSEMEIRE DE CASTRO PEREIRA"", ""dob"": ""1978-11-23"", ""gender"": ""F"", ""city"": ""JANDIRA"", ""state"": ""SP"", ""type"": ""person""}]",,555511992733455,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,OSASCO,1955-05-08,F,***********,DALVA DE ALMEIDA,,SP,person,,,,,JANDIRA,1978-11-23,F,***********,ROSEMEIRE DE CASTRO PEREIRA,,SP,person,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[DALVA DE ALMEIDA"", ""ROSEMEIRE DE CASTRO PEREIRA]","[DALVA DE ALMEIDA"", ""ROSEMEIRE DE CASTRO PEREIRA]",[],"[DALVA"", ""ROSEMEIRE]","["", ""]","[DE ALMEIDA"", ""DE CASTRO PEREIRA]",,,,"[1955-05-08"", ""1978-11-23]","[OSASCO"", ""JANDIRA]",[],"[SP"", ""SP]","[***********"", ""***********]","[F"", ""F]",[],[],"[***********"", ""***********]",0.0,0.74,1.0,1.0,1.0,0.52,1.0,0.52,0.95,2.0,2.0
1bc5bdcd-cd32-427a-8bc1-40e25420c2db,Lucas,Santos,+5588981978606,Lucas Santos,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555588981978606"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""b5309dfb-e373-4789-89b4-eedaca66ed1a"", ""request_id"": ""1bc5bdcd-cd32-427a-8bc1-40e25420c2db""}",2634.02,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555588981978606"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",b5309dfb-e373-4789-89b4-eedaca66ed1a,1bc5bdcd-cd32-427a-8bc1-40e25420c2db,true,200,[],,555588981978606,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0
16dbca2c-a447-4271-b9fa-6159e174380c,Rosimeire,Ferreira,+5561991261712,Rosimeire Ferreira,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""JOSE ALVES SOBRINHO"", ""dob"": ""1948-04-14"", ""gender"": ""M"", ""city"": ""SANTA MARIA"", ""state"": ""DF"", ""type"": ""person""}, {""id_num"": ""64550524120"", ""name"": ""ROSIMEIRE FERREIRA GOMES"", ""dob"": ""1971-12-11"", ""gender"": ""F"", ""city"": ""BRASILIA"", ""state"": ""DF"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555561991261712"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""92b7b384-3949-4da3-b4f7-402812c85c6f"", ""request_id"": ""16dbca2c-a447-4271-b9fa-6159e174380c""}",2500.21,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""JOSE ALVES SOBRINHO"", ""dob"": ""1948-04-14"", ""gender"": ""M"", ""city"": ""SANTA MARIA"", ""state"": ""DF"", ""type"": ""person""}, {""id_num"": ""64550524120"", ""name"": ""ROSIMEIRE FERREIRA GOMES"", ""dob"": ""1971-12-11"", ""gender"": ""F"", ""city"": ""BRASILIA"", ""state"": ""DF"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555561991261712"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",92b7b384-3949-4da3-b4f7-402812c85c6f,16dbca2c-a447-4271-b9fa-6159e174380c,true,200,"[{""id_num"": ""**********"", ""name"": ""JOSE ALVES SOBRINHO"", ""dob"": ""1948-04-14"", ""gender"": ""M"", ""city"": ""SANTA MARIA"", ""state"": ""DF"", ""type"": ""person""}, {""id_num"": ""64550524120"", ""name"": ""ROSIMEIRE FERREIRA GOMES"", ""dob"": ""1971-12-11"", ""gender"": ""F"", ""city"": ""BRASILIA"", ""state"": ""DF"", ""type"": ""person""}]",,555561991261712,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,SANTA MARIA,1948-04-14,M,**********,JOSE ALVES SOBRINHO,,DF,person,,,,,BRASILIA,1971-12-11,F,64550524120,ROSIMEIRE FERREIRA GOMES,,DF,person,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[JOSE ALVES SOBRINHO"", ""ROSIMEIRE FERREIRA GOMES]","[JOSE ALVES SOBRINHO"", ""ROSIMEIRE FERREIRA GOMES]",[],"[JOSE"", ""ROSIMEIRE]","[""ALVES"", ""FERREIRA]","[SOBRINHO"", ""GOMES]",,,,"[1948-04-14"", ""1971-12-11]","[SANTA MARIA"", ""BRASILIA]",[],"[DF"", ""DF]","[**********"", ""64550524120]","[M"", ""F]",[],[],"[**********"", ""64550524120]",1.0,0.86,1.0,1.0,0.0,0.27,1.0,0.27,0.98,2.0,
2f93404c-e1de-4dc9-bc84-5071e0df5944,Ines,Guidolin,+5519982437845,Ines Guidolin,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""37233195803"", ""name"": ""LARA STEPHANI GUIDOLIN DE BRITO"", ""dob"": ""2001-11-06"", ""gender"": ""F"", ""city"": ""HORTOLANDIA"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555519982437845"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""4ee13adb-bd36-40ca-9085-54329489611b"", ""request_id"": ""2f93404c-e1de-4dc9-bc84-5071e0df5944""}",2657.26,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""37233195803"", ""name"": ""LARA STEPHANI GUIDOLIN DE BRITO"", ""dob"": ""2001-11-06"", ""gender"": ""F"", ""city"": ""HORTOLANDIA"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555519982437845"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",4ee13adb-bd36-40ca-9085-54329489611b,2f93404c-e1de-4dc9-bc84-5071e0df5944,true,200,"[{""id_num"": ""37233195803"", ""name"": ""LARA STEPHANI GUIDOLIN DE BRITO"", ""dob"": ""2001-11-06"", ""gender"": ""F"", ""city"": ""HORTOLANDIA"", ""state"": ""SP"", ""type"": ""person""}]",,555519982437845,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,HORTOLANDIA,2001-11-06,F,37233195803,LARA STEPHANI GUIDOLIN DE BRITO,,SP,person,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[LARA STEPHANI GUIDOLIN DE BRITO],[LARA STEPHANI GUIDOLIN DE BRITO],[],[LARA],[STEPHANI GUIDOLIN],[DE BRITO],,,,[2001-11-06],[HORTOLANDIA],[],[SP],[37233195803],[F],[],[],[37233195803],0.0,0.45,0.0,0.0,0.0,0.25,0.0,0.25,0.03,-1.0,
1efe205c-9293-4300-bc34-67611a482fae,Erick,Gonçalves,+5551998941579,Erick Gonçalves,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""GRASIELE DE PAULO OLIVEIRA"", ""dob"": ""1996-04-17"", ""gender"": ""F"", ""city"": ""TAQUARA"", ""state"": ""RS"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555551998941579"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""39553d7c-179d-43d2-ae69-48f3038e920e"", ""request_id"": ""1efe205c-9293-4300-bc34-67611a482fae""}",2549.32,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""GRASIELE DE PAULO OLIVEIRA"", ""dob"": ""1996-04-17"", ""gender"": ""F"", ""city"": ""TAQUARA"", ""state"": ""RS"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555551998941579"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",39553d7c-179d-43d2-ae69-48f3038e920e,1efe205c-9293-4300-bc34-67611a482fae,true,200,"[{""id_num"": ""**********"", ""name"": ""GRASIELE DE PAULO OLIVEIRA"", ""dob"": ""1996-04-17"", ""gender"": ""F"", ""city"": ""TAQUARA"", ""state"": ""RS"", ""type"": ""person""}]",,555551998941579,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,TAQUARA,1996-04-17,F,**********,GRASIELE DE PAULO OLIVEIRA,,RS,person,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[GRASIELE DE PAULO OLIVEIRA],[GRASIELE DE PAULO OLIVEIRA],[],[GRASIELE],[],[DE PAULO OLIVEIRA],,,,[1996-04-17],[TAQUARA],[],[RS],[**********],[F],[],[],[**********],0.0,0.35,0.0,0.31,0.0,0.32,0.31,0.32,0.03,,
6b5867db-0b5a-4ab8-8313-b7fc6bf62b93,Marcos,Vinicius,+5511983987140,Marcos Vinicius,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""10098692801"", ""name"": ""NIVALDO PECEGUEIRO"", ""dob"": ""1967-12-26"", ""gender"": ""M"", ""city"": ""SAO BERNARDO DO CAMPO"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511983987140"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""39c3d8b7-9182-4df6-a69c-8a2b41f39921"", ""request_id"": ""6b5867db-0b5a-4ab8-8313-b7fc6bf62b93""}",3002.84,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""10098692801"", ""name"": ""NIVALDO PECEGUEIRO"", ""dob"": ""1967-12-26"", ""gender"": ""M"", ""city"": ""SAO BERNARDO DO CAMPO"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511983987140"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",39c3d8b7-9182-4df6-a69c-8a2b41f39921,6b5867db-0b5a-4ab8-8313-b7fc6bf62b93,true,200,"[{""id_num"": ""10098692801"", ""name"": ""NIVALDO PECEGUEIRO"", ""dob"": ""1967-12-26"", ""gender"": ""M"", ""city"": ""SAO BERNARDO DO CAMPO"", ""state"": ""SP"", ""type"": ""person""}]",,555511983987140,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,SAO BERNARDO DO CAMPO,1967-12-26,M,10098692801,NIVALDO PECEGUEIRO,,SP,person,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[NIVALDO PECEGUEIRO],[NIVALDO PECEGUEIRO],[],[NIVALDO],[],[PECEGUEIRO],,,,[1967-12-26],[SAO BERNARDO DO CAMPO],[],[SP],[10098692801],[M],[],[],[10098692801],0.0,0.3,0.0,0.31,0.0,0.22,0.31,0.22,0.03,,
74b1fc3d-eb39-4af3-b511-060ac361d794,Katia,miranda,+5581997453256,Katia miranda,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555581997453256"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""316c67e0-51b1-439a-b553-52dd0748eeea"", ""request_id"": ""74b1fc3d-eb39-4af3-b511-060ac361d794""}",2565.7,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555581997453256"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",316c67e0-51b1-439a-b553-52dd0748eeea,74b1fc3d-eb39-4af3-b511-060ac361d794,true,200,[],,555581997453256,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0
8d399f45-3f14-421b-899d-12529af7a0cf,Leonardo,Rodrigues Do Carmo,+5521988818940,Leonardo Rodrigues Do Carmo,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""GISELE DE SOUZA SOARES SALLES"", ""dob"": ""1982-04-28"", ""gender"": ""F"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555521988818940"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""4475382f-bee9-4621-a18c-a106a47c056e"", ""request_id"": ""8d399f45-3f14-421b-899d-12529af7a0cf""}",2730.12,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""GISELE DE SOUZA SOARES SALLES"", ""dob"": ""1982-04-28"", ""gender"": ""F"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555521988818940"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",4475382f-bee9-4621-a18c-a106a47c056e,8d399f45-3f14-421b-899d-12529af7a0cf,true,200,"[{""id_num"": ""**********"", ""name"": ""GISELE DE SOUZA SOARES SALLES"", ""dob"": ""1982-04-28"", ""gender"": ""F"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}]",,555521988818940,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,RIO DE JANEIRO,1982-04-28,F,**********,GISELE DE SOUZA SOARES SALLES,,RJ,person,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[GISELE DE SOUZA SOARES SALLES],[GISELE DE SOUZA SOARES SALLES],[],[GISELE],[],[DE SOUZA SOARES SALLES],,,,[1982-04-28],[RIO DE JANEIRO],[],[RJ],[**********],[F],[],[],[**********],0.0,0.39,0.0,0.29,0.0,0.35,0.29,0.35,0.03,,
57e4681a-fde2-438d-970e-b41e668ff378,Milena,Claudino,+558393712412,Milena Claudino,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""55558393712412"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""0af02bd6-64b6-490a-a043-ea1004279d7c"", ""request_id"": ""57e4681a-fde2-438d-970e-b41e668ff378""}",2593.85,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""55558393712412"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",0af02bd6-64b6-490a-a043-ea1004279d7c,57e4681a-fde2-438d-970e-b41e668ff378,true,200,[],,55558393712412,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0
26c1e6fa-4509-41af-8884-995082887b29,josevaldo,ferreira de Jesus,+5511976336072,josevaldo ferreira de Jesus,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""JOAO JOSE FILHO"", ""dob"": ""1963-06-24"", ""gender"": ""M"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""10305010840"", ""name"": ""MARIA TERESA DA CUNHA"", ""dob"": ""1952-12-06"", ""gender"": ""F"", ""city"": ""SAO BERNARDO DO CAMPO"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""15096038831"", ""name"": ""JOSEVALDO FERREIRA DE JESUS"", ""dob"": ""1970-07-15"", ""gender"": ""M"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511976336072"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""ea788fd0-b7ef-4fc7-95fc-8f7d2c6158ef"", ""request_id"": ""26c1e6fa-4509-41af-8884-995082887b29""}",2533.95,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""JOAO JOSE FILHO"", ""dob"": ""1963-06-24"", ""gender"": ""M"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""10305010840"", ""name"": ""MARIA TERESA DA CUNHA"", ""dob"": ""1952-12-06"", ""gender"": ""F"", ""city"": ""SAO BERNARDO DO CAMPO"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""15096038831"", ""name"": ""JOSEVALDO FERREIRA DE JESUS"", ""dob"": ""1970-07-15"", ""gender"": ""M"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511976336072"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",ea788fd0-b7ef-4fc7-95fc-8f7d2c6158ef,26c1e6fa-4509-41af-8884-995082887b29,true,200,"[{""id_num"": ""**********"", ""name"": ""JOAO JOSE FILHO"", ""dob"": ""1963-06-24"", ""gender"": ""M"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""10305010840"", ""name"": ""MARIA TERESA DA CUNHA"", ""dob"": ""1952-12-06"", ""gender"": ""F"", ""city"": ""SAO BERNARDO DO CAMPO"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""15096038831"", ""name"": ""JOSEVALDO FERREIRA DE JESUS"", ""dob"": ""1970-07-15"", ""gender"": ""M"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}]",,555511976336072,97,"IDM-RP-006,IDM-RP-013",landline,UNKNOWN,BR,syntax,false,,false,false,false,,,,,,,,,,,,[],[],5,,,,SAO PAULO,1963-06-24,M,**********,JOAO JOSE FILHO,,SP,person,,,,,SAO BERNARDO DO CAMPO,1952-12-06,F,10305010840,MARIA TERESA DA CUNHA,,SP,person,,SAO PAULO,1970-07-15,M,15096038831,JOSEVALDO FERREIRA DE JESUS,SP,person,2025-04-20,false,landline,false,,false,BR,syntax,97,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[JOAO JOSE FILHO"", ""MARIA TERESA DA CUNHA"", ""JOSEVALDO FERREIRA DE JESUS]","[JOAO JOSE FILHO"", ""MARIA TERESA DA CUNHA"", ""JOSEVALDO FERREIRA DE JESUS]",[],"[JOAO"", ""MARIA"", ""JOSEVALDO]","["", ""TERESA"", ""FERREIRA]","[JOSE FILHO"", ""DA CUNHA"", ""DE JESUS]",,,,"[1963-06-24"", ""1952-12-06"", ""1970-07-15]","[SAO PAULO"", ""SAO BERNARDO DO CAMPO"", ""SAO PAULO]",[],"[SP"", ""SP"", ""SP]","[**********"", ""10305010840"", ""15096038831]","[M"", ""F"", ""M]",[],[],"[**********"", ""10305010840"", ""15096038831]",1.0,1.0,1.0,1.0,1.0,0.64,1.0,0.64,0.98,2.0,2.0