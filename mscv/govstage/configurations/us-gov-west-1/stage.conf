withDBMigration=false

scheduler {
  time.interval = 120
  s3.bucket = "rulecode-config-stage-147787095025-us-gov-west-1"
  s3.object = "rulecode-config.txt"
  s3.fetch.wait.time = 30
}
threadpool {
  poolSize=100
}

server {
  port=5000
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 80%
    non_heap.used_max_percentage = "<80.0" //should be less than 80%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

rcHelper {
  timeout = 100 # in ms
  allowedVendors = []
}

hmac {
  ttl=5
  time.interval=5
  strength=512
  aws.secrets.manager.id="rulecode-service/stage/hmac-514a56"
  secret.refresh.interval=5000
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]

jmx {
  port = 1098
}


transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region=us-gov-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-gov-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-gov-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-stage"
      }
      third-party {
        region=us-gov-west-1
        bucket="thirdparty-stats-stage-147787095025-us-gov-west-1"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

s3 {
  bucket = "rulecode-config-stage-147787095025-us-gov-west-1"
  paths {
    table = "table_definitions"
    rule = "rulecode_definitions"
    rulev2 = "rulecode_definitions_v2"
  }
  audit {
    bucket = "rulecode-audit-stage-147787095025-us-gov-west-1"
  }
}

refresh {
  rulecode {
    time {
      initial = 600
      interval = 600
    }
  }
  emailIntelligence {
    cron = "0 0 14 ? * *"
  }
}

toggle {
  rulecodeStudio {
    s3config: false
    configApi: false
    configReload: false
  }
  dynamodb {
    configReload: true
  }
}

memcache.servers = "memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com:11211"

http.plugin {
  threadPool {
    poolSize = 50
  }
}

external.vendors.config {
  twilio {
      endpoint = "https://lookups.twilio.com/v2/PhoneNumbers/{{PHONENUMBER}}"
  }
}

context {
  cache {
    size = 10000
    threads = 250
  }
}

#===================Dynamic Control Center==========================#

dynamic.control.center {
  s3 {
    bucketName = "globalconfig-147787095025-us-gov-west-1"
  }
  memcached {
    host=memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

###====== Dyanomo Table Configuration Begins========###

dynamodb {
  maxconcurrency=150
  batchget{
    retry.count=3
    timeoutInMills=150
    batch.size=95
  }
  dataFetchEnabled = true
  env = "stage"
  region = "us-gov-west-1"
  configTable = "rc_config_stage"
  currentConfigKey = "rc_table_config"
  ingestionKey = "rc_table_config_ingestion_success"
  rollbackKey = "rc_table_config_rollback"

  allowDynamoToggle=false

  unreleasedTables = [
  ]

  compressed_data_tables = [
    "fullcontact_email_lookup",
    "equifax_email_lookup",
    "neustar_ip_reputation_lookup",
    "efx_chm_ssn",
    "efx_lfm_ssn",
    "efx_chm_fullname_dob",
    "towerdata_email_lookup",
    "phone_correlation",
    "ssn_correlation",
    "address_correlation",
    "asl_vendor_lookup",
    "attom_vendor_lookup",
    "neustar_ipv4_geolocation_lookup",
    "neustar_ipv6_geolocation_lookup",
    "scpvl_lookup"
  ]

  localConf {
    isLocal = false
    region = "us-gov-west-1"
    access.key="x" #ignored if isLocal is false
    secret.key="x" #ignored if isLocal is false
  }

  simpleKeyTbl {
    partitionFieldName = "pk"
    dataFieldName = "data"
    numberOfKeysForBatch = "50"
  }

  compositeKeyTbl {
    partitionFieldName = "pk"
    sortFieldName = "sk"
    dataFieldName = "data"
  }
}
###====== Dyanomo Table Configuration Ends========###

server.metrics.enabled = false