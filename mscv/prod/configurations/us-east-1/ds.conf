withDBMigration=false

scheduler {
  time.interval = 120
  s3.bucket = "rulecode-config-ds"
  s3.object = "rulecode-config.txt"
  s3.fetch.wait.time = 30
}
threadpool {
  poolSize=500
}

server {
  port=5000
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 80%
    non_heap.used_max_percentage = "<80.0" //should be less than 80%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

hmac {
  ttl=5
  time.interval=5
  strength=512
  aws.secrets.manager.id="rulecode-service/ds/hmac"
  secret.refresh.interval=5000
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]

jmx {
  port = 1098
}

rcHelper {
  timeout = 100 # in ms
  allowedVendors = []
}

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region=us-east-1
        transaction {
          queueName=transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-ds
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-ds"
      }
      third-party {
        region=us-east-1
        bucket="mlpipe/thirdparty/datascience"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

s3 {
  bucket = "rulecode-config-ds"
  paths {
    table = "table_definitions"
    rule = "rulecode_definitions"
    rulev2 = "rulecode_definitions_v2"
  }
  audit {
    bucket = "rulecode-audit-ds"
  }
  kmsId = "arn:aws:kms:us-east-1:112942558241:key/7c148caf-8dba-4277-81e2-51c7ae149a1b"
}

refresh {
  rulecode {
    time {
      initial = 600
      interval = 600
    }
  }
  emailIntelligence {
      cron = "0 0 14 ? * *"
    }
}

toggle {
  rulecodeStudio {
    s3config: false
    configApi: false
    configReload: false
  }
  dynamodb {
      configReload: false
  }
}

memcache.servers = "vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com:11211"

http.plugin {
    threadPool {
        poolSize = 50
    }
}

external.vendors.config {
  payfone {
    username = """ENC(qmQUvzLzjQxfmXVPBX1LQp465ApKb78867aAzFCwnKaAaWN+Bhy58SeVWEsBp8CoG3sQKfJEFi32T5Y/O3PkMiy2P4k=)"""
    password = """ENC(hm4BfNq/YN3oNk69Z9MydkZMBJuWWz5YniwzmcCTEa//DpSBViKVjor4JaXmxPHm4YujI/B+pEO/i86VVJPA59GzwGQ=)"""
    clientId = """ENC(FbCmCCYbERqXdlZQMHfYpUx9P038XwELiEpjaq3TWlFfhn4qfRk=)"""
  }
  #In-house env creds
  neustar {
    endpoint = "http://gwy-ds-use1-app-393600028.us-east-1.elb.amazonaws.com/api/identityRisk_fraudPrevention-v1",
    auth.key = """ENC(7VMVBLw4bmXNpXm7GX4RcsVetIXX4F/jTiCFogbF6VG/vOV+8EkE1xXQ8a+Y12ZbqicVJi5w5Y2Mq+syE9nXyTxMo668NpDQ6yrWWQEoai/9Aa3qRWw=)"""
    endpoint2 = "http://gwy-stage-use1-app-103768082.us-east-1.elb.amazonaws.com/api/identityRisk_fraudPrevention-v1"
    auth.key2 = """ENC(7VMVBLw4bmXNpXm7GX4RcsVetIXX4F/jTiCFogbF6VG/vOV+8EkE1xXQ8a+Y12ZbqicVJi5w5Y2Mq+syE9nXyTxMo668NpDQ6yrWWQEoai/9Aa3qRWw=)"""
  }

    #whitepages
    whitepages {
      endpoint = "https://api.ekata.com/3.0/identity_check.json",
      auth.key = """ENC(taMabhc27o84vzdMfghWl/dVWmDPqiORJyeiQGYYaCA/e1FOWwc7ZGKQNxycHT3OmF2BufaTpOCYggtphQ/BA/4BoDwSQ04=)"""
    }
    whitepages_alternate {
       endpoint = "https://api.ekata.com/3.0/identity_check.json",
       auth.key = """ENC(taMabhc27o84vzdMfghWl/dVWmDPqiORJyeiQGYYaCA/e1FOWwc7ZGKQNxycHT3OmF2BufaTpOCYggtphQ/BA/4BoDwSQ04=)"""
    }
    enstream {
        endpoint = "https://token-prod.enstreamidentity.com/v1/service"
        auth.key = """ENC(CT0CoWjOBi5Fk7GStN4p4C/p3pKBk7BXl9EFb2GdFf/exnZIdUZ7MwGsoNWtasWwXG79cJkSOIxRNBs6ttDXHWlH/3OtNzPkMpg=)"""
        token.valid.time.in.minutes = 5
        kid = """ENC(LzFfVorzdSAH9xr3d1Lv42FLoWCL5IT8JpoxMu8V+XRgk88DE/cJYi61oizCkA==)"""
        operation = "CS-V1"
        sign.algorithm = "RSASSA_PKCS1_V1_5_SHA_256"
        encrypt.decrypt.public.key.secret = "kyc-vendor-service/ds/enstream_enc_key_secret"
        sign.verify.public.key.secret = "kyc-vendor-service/ds/enstream_sign_key_secret"
        service.provider.id = "**********"
    }
  twilio {
    endpoint = "https://lookups.twilio.com/v2/PhoneNumbers/{{PHONENUMBER}}",
    auth.key = """ENC(VFApn9dwe/i2NqnfYVVFzTessO8AKPMrdoDjRwEadXs3khyEkxIPm39PBZy6XTTl+87Je/K4qFrQm67Un0VnZ+Y4uE8/f+xLzU8TNVAF8SS1zOjsSCz/Sfutvt51dg2XAAZqTnUlT8C5ATyJL3zUrhr5Y5eehyR9T79QOxjlNhjdKg==)""" #Fips encrypted value
  }
    kyc-vendor-service {
        endpoint = "http://kyc-vendor-service/vendor/data"
    }
}
kms{
  region = "us-east-1"
  sign.key.id = "a1fbb610-d0a7-4fe1-9e7d-dd69f321ab38"
  decrypt.key.id = "5c1f2d26-42e5-4797-9ea6-07a3af3a6ca8"
}

context {
  cache {
    size = 10000
    threads = 250
  }
}

#===================Dynamic Control Center==========================#

dynamic.control.center {
  s3 {
    bucketName = "globalconfig-ds"
  }
  memcached {
    host=vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

###====== Dyanomo Table Configuration Begins========###

dynamodb {
        maxconcurrency=150
        batchget{
          retry.count=3
          timeoutInMills=500
          batch.size=95
        }
        dataFetchEnabled = true
        env = "ds"
        region="us-east-1"
        configTable = "rc_config_unified"
        currentConfigKey = "rc_table_config_ds"
        ingestionKey = "rc_table_config_ds_ingestion_success"
        rollbackKey = "rc_table_config_ds_rollback"

        allowDynamoToggle=false

        unreleasedTables = [
        ]

        compressed_data_tables = [
                "fullcontact_email_lookup",
                "equifax_email_lookup",
                "neustar_ip_reputation_lookup",
                "efx_chm_ssn",
                "efx_lfm_ssn",
                "efx_chm_fullname_dob",
                "towerdata_email_lookup",
                "phone_correlation",
                "ssn_correlation",
                "address_correlation",
                "asl_vendor_lookup",
                "attom_vendor_lookup",
                "neustar_ipv4_geolocation_lookup",
                "neustar_ipv6_geolocation_lookup",
                "scpvl_lookup"
                ]

        localConf {
          isLocal = false
          region = "us-east-1"
          access.key="x" #ignored if isLocal is false
          secret.key="x" #ignored if isLocal is false
        }

        simpleKeyTbl{
        partitionFieldName = "pk"
        dataFieldName = "data"
        numberOfKeysForBatch = "50"
        }

        compositeKeyTbl{
        partitionFieldName = "pk"
        sortFieldName = "sk"
        dataFieldName = "data"
        }
}
###====== Dyanomo Table Configuration Ends========###

address-service {
   endpoint = "http://address-service",
   alternateEndpoint = "http://address-service"
}

thirdparty.auditing.v2 {
  enabled = true
  flagWaitTimeoutMillis = 100
  aws {
    msk.cluster {
      bootstrap.servers {
        primary = "boot-ng87nddo.c1.kafka-serverless.us-east-1.amazonaws.com:9098"
      }
      topic.name = "thirdparty-auditing-ds"
    }
    s3 {
      region = "us-east-1"
      bucket = "thirdparty-stats-errors-ds"
      prefix = "send_errors"
      kmsArn = "arn:aws:kms:us-east-1:112942558241:key/6a364622-5d67-443c-9b40-74ca900db95d"
    }
  }
}
