withDBMigration=false

scheduler {
  time.interval = 120
  s3.bucket = "rulecode-config-prod"
  s3.object = "rulecode-config.txt"
  s3.fetch.wait.time = 30
}
threadpool {
  poolSize=100
}

server {
  port=5000
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 80%
    non_heap.used_max_percentage = "<80.0" //should be less than 80%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

rcHelper {
  timeout = 100 # in ms
  allowedVendors = []
}

hmac {
  ttl=5
  time.interval=5
  strength=512
  aws.secrets.manager.id="rulecode-service/prod/hmac"
  secret.refresh.interval=5000
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]

jmx {
  port = 1098
}

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region=us-east-1
        transaction {
          queueName=transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-prod"
      }
      third-party {
        region=us-east-1
        bucket="thirdparty-stats-prod"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

s3 {
  bucket = "rulecode-config-prod"
  paths {
    table = "table_definitions"
    rule = "rulecode_definitions"
    rulev2 = "rulecode_definitions_v2"
  }
  audit {
    bucket = "rulecode-audit-prod"
  }
}

refresh {
  rulecode {
    time {
      initial = 600
      interval = 600
    }
  }
  emailIntelligence {
    cron = "0 0 14 ? * *"
  }
}

toggle {
  rulecodeStudio {
    s3config: false
    configApi: false
    configReload: false
  }
  dynamodb {
    configReload: true
  }
}

memcache.servers = "replay-env-prod.ps2mlp.cfg.use1.cache.amazonaws.com:11211"

http.plugin {
  threadPool {
    poolSize = 50
  }
}

external.vendors.config {
  payfone {
    username = """ENC(qmQUvzLzjQxfmXVPBX1LQp465ApKb78867aAzFCwnKaAaWN+Bhy58SeVWEsBp8CoG3sQKfJEFi32T5Y/O3PkMiy2P4k=)""" #Fips encrypted value
    password = """ENC(hm4BfNq/YN3oNk69Z9MydkZMBJuWWz5YniwzmcCTEa//DpSBViKVjor4JaXmxPHm4YujI/B+pEO/i86VVJPA59GzwGQ=)""" #Fips encrypted value
    clientId = """ENC(FbCmCCYbERqXdlZQMHfYpUx9P038XwELiEpjaq3TWlFfhn4qfRk=)""" #Fips encrypted value
  }
  #In-house env creds
  neustar {
    endpoint = "http://gwy-prod-use1-app-564086062.us-east-1.elb.amazonaws.com/api/identityRisk_fraudPrevention-v1",
    endpoint2 = "http://gwy-prod-use1-app-564086062.us-east-1.elb.amazonaws.com/api/identityRisk_fraudPrevention-v1"
    auth.key = """ENC(7VMVBLw4bmXNpXm7GX4RcsVetIXX4F/jTiCFogbF6VG/vOV+8EkE1xXQ8a+Y12ZbqicVJi5w5Y2Mq+syE9nXyTxMo668NpDQ6yrWWQEoai/9Aa3qRWw=)""" #Fips encrypted value
    auth.key2 = """ENC(7VMVBLw4bmXNpXm7GX4RcsVetIXX4F/jTiCFogbF6VG/vOV+8EkE1xXQ8a+Y12ZbqicVJi5w5Y2Mq+syE9nXyTxMo668NpDQ6yrWWQEoai/9Aa3qRWw=)""" #Fips encrypted value
  }

  #whitepages
  whitepages {
    endpoint = "https://api.ekata.com/3.0/identity_check.json",
    auth.key = """ENC(taMabhc27o84vzdMfghWl/dVWmDPqiORJyeiQGYYaCA/e1FOWwc7ZGKQNxycHT3OmF2BufaTpOCYggtphQ/BA/4BoDwSQ04=)""" #Fips encrypted value
  }
  whitepages_alternate {
    endpoint = "https://api.ekata.com/3.0/identity_check.json",
    auth.key = """ENC(taMabhc27o84vzdMfghWl/dVWmDPqiORJyeiQGYYaCA/e1FOWwc7ZGKQNxycHT3OmF2BufaTpOCYggtphQ/BA/4BoDwSQ04=)""" #Fips encrypted value
  }
  enstream {
      endpoint = "https://token-prod.enstreamidentity.com/v1/service"
      auth.key = """ENC(ntQ5FjFI5DKT3JCmKuzEEoWppdbZ8Fj13EHymdALP30b3dQ82a1caASiu8bG1ihirLGCP4hdVSf5DbKpcB2Ff1W8o10L+ASQgzM=)"""
      token.valid.time.in.minutes = 5
      kid = """ENC(+kSIWbG73pbycJkl/D6YkaCsmPjKcNg8YV96BQ9RTUZGjdo8FjjDMPxx3SZeAA==)"""
      operation = "CS-V1"
      sign.algorithm = "RSASSA_PKCS1_V1_5_SHA_256"
      encrypt.decrypt.public.key.secret = "kyc-vendor-service/prod/enstream_enc_key_secret"
      sign.verify.public.key.secret = "kyc-vendor-service/prod/enstream_sign_key_secret"
      service.provider.id = "**********"
  }
  twilio {
    endpoint = "https://lookups.twilio.com/v2/PhoneNumbers/{{PHONENUMBER}}",
    auth.key = """ENC(VFApn9dwe/i2NqnfYVVFzTessO8AKPMrdoDjRwEadXs3khyEkxIPm39PBZy6XTTl+87Je/K4qFrQm67Un0VnZ+Y4uE8/f+xLzU8TNVAF8SS1zOjsSCz/Sfutvt51dg2XAAZqTnUlT8C5ATyJL3zUrhr5Y5eehyR9T79QOxjlNhjdKg==)""" #Fips encrypted value
  }
    kyc-vendor-service {
        endpoint = "http://kyc-vendor-service-replay/vendor/data"
    }
}
kms{
  region = "us-east-1"
  sign.key.id = "00bf5b74-bed1-4fc6-9c83-893a75fe1fe7"
  decrypt.key.id = "5aaa6bb2-6418-403f-87b5-1846aa03e443"
}

context {
  cache {
    size = 10000
    threads = 250
  }
}

#===================Dynamic Control Center==========================#

dynamic.control.center {
  s3 {
    bucketName = "globalconfig-prod"
  }
  memcached {
    host=replay-env-prod.ps2mlp.cfg.use1.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

###====== Dyanomo Table Configuration Begins========###

dynamodb {
  maxconcurrency=150
  batchget{
    retry.count=3
    timeoutInMills=200
    batch.size=95
  }
  dataFetchEnabled = true
  env = "prod"
  region="us-east-1"
  configTable = "rc_config_unified"
  currentConfigKey = "rc_table_config_prod"
  ingestionKey = "rc_table_config_prod_ingestion_success"
  rollbackKey = "rc_table_config_prod_rollback"

  allowDynamoToggle=false

  unreleasedTables = [
  ]

  compressed_data_tables = [
    "fullcontact_email_lookup",
    "equifax_email_lookup",
    "neustar_ip_reputation_lookup",
    "efx_chm_ssn",
    "efx_lfm_ssn",
    "efx_chm_fullname_dob",
    "towerdata_email_lookup",
    "phone_correlation",
    "ssn_correlation",
    "address_correlation",
    "asl_vendor_lookup",
    "attom_vendor_lookup",
    "neustar_ipv4_geolocation_lookup",
    "neustar_ipv6_geolocation_lookup",
    "scpvl_lookup"
  ]

  localConf {
    isLocal = false
    region = "us-east-1"
    access.key="x" #ignored if isLocal is false
    secret.key="x" #ignored if isLocal is false
  }

  simpleKeyTbl{
    partitionFieldName = "pk"
    dataFieldName = "data"
    numberOfKeysForBatch = "50"
  }

  compositeKeyTbl{
    partitionFieldName = "pk"
    sortFieldName = "sk"
    dataFieldName = "data"
  }
}
###====== Dyanomo Table Configuration Ends========###

server.metrics.enabled = false

address-service {
   endpoint = "http://address-service-replay",
   alternateEndpoint = "http://address-service-replay"
}