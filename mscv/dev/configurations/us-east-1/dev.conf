withDBMigration=false

scheduler {
  time.interval = 120
  s3.bucket = "rulecode-config-764009278656-us-east-1"
  s3.object = "rulecode-config.txt"
  s3.fetch.wait.time = 30
}
threadpool {
  poolSize=100
}

server {
  port=5000
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 80%
    non_heap.used_max_percentage = "<80.0" //should be less than 80%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

rcHelper {
  endpoint = "http://rc-helper-service"
  timeout = 100 # in ms
  allowedVendors = []
}

hmac {
  ttl=5
  time.interval=5
  strength=512
  aws.secrets.manager.id="rulecode-service/dev/hmac-49397d"
  secret.refresh.interval=5000
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]

jmx {
  port = 1098
}

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region=us-east-1
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-dev
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-dev-764009278656-us-east-1"
      }
      third-party {
        region=us-east-1
        bucket="thirdparty-stats-dev-764009278656-us-east-1"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

s3 {
  bucket = "rulecode-config-764009278656-us-east-1"
  paths {
    table = "table_definitions"
    rule = "rulecode_definitions"
    rulev2 = "rulecode_definitions_v2"
  }
  audit {
    bucket = "rulecode-audit-764009278656-us-east-1"
  }
}

refresh {
  rulecode {
    time {
      initial = 600
      interval = 600
    }
  }
  emailIntelligence {
      cron = "0 0 14 ? * *"
    }
}

toggle {
  rulecodeStudio {
    s3config: false
    configApi: false
    configReload: false
  }
  dynamodb {
    configReload: true
  }
}


memcache.servers = "product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link:11211"

http.plugin {
  threadPool {
    poolSize = 50
  }
}

external.vendors.config {
  payfone {
    username = """ENC(wicj4Av31TMKh1xq4NlH4WkuxvNBrqRpRveYeIhWGvPt6qlD5M63MqfZ800B3ayrCnGlVQ6SkOD4qoOkYDrR6maRy68=)"""
    password = """ENC(4C7BKtg8DGCCib/06XhPsXAo9D8vRYaqSEafGjiFzRnXiu3zNoZ4jQ6heJHKa4o16fEChIwmnKVlaCr3+Wa4vYAQrYM=)"""
    clientId = """ENC(NrHJNmkZU9QQAOihr1rxRx9GMZuE8c0xJMTDjUaY4sb/3qF4sg8=)"""
  }
  neustar {
    endpoint = "http://mock-service/api/identityRisk_fraudPrevention-v1?api_key=rafDo2faK5xhV0TmARtOjTTks0flzLXu"
    endpoint2 = "http://mock-service/api/identityRisk_fraudPrevention-v1?api_key=rafDo2faK5xhV0TmARtOjTTks0flzLXu"
    auth.key = """ENC(d3alKmYPs172KyXaKPtheo1PvNsjkoUDZdepaOMMLbF6wIkFDOcXp7ViH4CwdSjXeKlMk7EbwVReAreQ309Uut7Hyf1sJC4Of3gZQnwt7nhi2g==)"""
    auth.key2 = """ENC(d3alKmYPs172KyXaKPtheo1PvNsjkoUDZdepaOMMLbF6wIkFDOcXp7ViH4CwdSjXeKlMk7EbwVReAreQ309Uut7Hyf1sJC4Of3gZQnwt7nhi2g==)"""
  }
  #whitepages
  whitepages {
    endpoint = "https://api.ekata.com/3.0/identity_check.json",
    auth.key = """ENC(taMabhc27o84vzdMfghWl/dVWmDPqiORJyeiQGYYaCA/e1FOWwc7ZGKQNxycHT3OmF2BufaTpOCYggtphQ/BA/4BoDwSQ04=)""" #Fips encrypted value
  }
  whitepages_alternate {
    endpoint = "https://api.ekata.com/3.0/identity_check.json",
    auth.key = """ENC(taMabhc27o84vzdMfghWl/dVWmDPqiORJyeiQGYYaCA/e1FOWwc7ZGKQNxycHT3OmF2BufaTpOCYggtphQ/BA/4BoDwSQ04=)""" #Fips encrypted value
  }
  enstream {
    endpoint = "https://token-staging.enstreamidentity.com/v1/service"
    auth.key = """ENC(FGSNdnN+d6dmIf1r1RM3DltOPhDkcJWWLQ/wkpj+cvj4BBHlTed8kk7tKp0MVioHs2YCrAnqRDBnCZnwf0pNyTsL)"""
    token.valid.time.in.minutes = 5
    kid = """ENC(rsQDJbeVr81C6KlQ17b9isRtwZzxFyFKdyyyh/8av1q0eU9cffzPpN8=)"""
    operation = "CS-V1"
    sign.algorithm = "RSASSA_PKCS1_V1_5_SHA_256"
    encrypt.decrypt.public.key.secret = "kyc-vendor-service/dev/enstream_enc_key_secret"
    sign.verify.public.key.secret = "kyc-vendor-service/dev/enstream_sign_key_secret"
    service.provider.id = "SO001"
  }
  twilio {
    endpoint = "https://lookups.twilio.com/v2/PhoneNumbers/{{PHONENUMBER}}"
  }
  kyc-vendor-service {
      endpoint = "http://mock-service/vendor/data"
  }
}
kms{
  region = "us-east-1"
  sign.key.id = "c846eea0-f412-4a57-bacb-2c26b309cba5"
  decrypt.key.id = "00ccd785-90f4-4932-8bad-5852570dab65"
}

context {
  cache {
    size = 10000
    threads = 250
  }
}

#===================Dynamic Control Center==========================#

dynamic.control.center{
  s3 {
    bucketName="globalconfig-764009278656-us-east-1"
  }
  memcached {
    host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
    port=11211
    ttl=86400
  }
  local.cache {
    timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

###====== Dyanomo Table Configuration Begins========###

dynamodb {
  maxconcurrency=150
  batchget{
    retry.count=3
    timeoutInMills=150
    batch.size=95
  }
  dataFetchEnabled = true
  env = "dev"
  region="us-east-1"
  configTable = "rc_config_dev"
  currentConfigKey = "rc_table_config_stage_ta"
  ingestionKey = "rc_table_config_ingestion_success"
  rollbackKey = "rc_table_config_rollback"

  allowDynamoToggle=false

  unreleasedTables = [
  ]

  compressed_data_tables = [
    "fullcontact_email_lookup",
    "equifax_email_lookup",
    "neustar_ip_reputation_lookup",
    "efx_chm_ssn",
    "efx_lfm_ssn",
    "efx_chm_fullname_dob",
    "towerdata_email_lookup",
    "phone_correlation",
    "ssn_correlation",
    "address_correlation",
    "asl_vendor_lookup",
    "attom_vendor_lookup",
    "neustar_ipv4_geolocation_lookup",
    "neustar_ipv6_geolocation_lookup",
    "scpvl_lookup"
  ]

  localConf {
    isLocal = false
    region = "us-east-1"
    access.key="x" #ignored if isLocal is false
    secret.key="x" #ignored if isLocal is false
  }

  simpleKeyTbl{
    partitionFieldName = "pk"
    dataFieldName = "data"
    numberOfKeysForBatch = "50"
  }

  compositeKeyTbl{
    partitionFieldName = "pk"
    sortFieldName = "sk"
    dataFieldName = "data"
  }
}
###====== Dyanomo Table Configuration Ends========###

server.metrics.enabled = false
#add full name with file type like "ssn_correlation.json"
read_table_definitions_from_mscv = [
]

thirdparty.auditing.v2 {
  enabled = true
  flagWaitTimeoutMillis = 100
  aws {
    msk.cluster {
      bootstrap.servers {
        primary = "boot-wgls83kf.c3.kafka-serverless.us-east-1.amazonaws.com:9098"
      }
      topic.name = "thirdparty-auditing-dev"
    }
    s3 {
      region = "us-east-1"
      bucket = "thirdparty-stats-errors-dev-764009278656-us-east-1"
      prefix = "send_errors"
      kmsArn = "arn:aws:kms:us-east-1:764009278656:key/7be1b782-93b8-437d-81fb-da11d61e9af7"
    }
  }
}

address-service {
   endpoint = "http://address-service",
   alternateEndpoint = "http://address-service"
}