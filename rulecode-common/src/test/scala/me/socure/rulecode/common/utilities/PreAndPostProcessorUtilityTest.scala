package me.socure.rulecode.common.utilities

import org.scalatest.{FreeSpec, Matchers}
import me.socure.rulecode.common.utilities.PreAndPostProcessorUtility


/**
 * <AUTHOR> Kumar
 */
class PreAndPostProcessorUtilityTest extends FreeSpec with Matchers {
  "should fetch processors" - {
    val knownProcessors = Set("filter_integers","lowercase","lookup_based_result_filter","clean_string","concat","getTimeAdjustedDate","nonEmpty","clean_us_zip","negate","split","value_existence_check","time_adjusted_date_as_date_str","suppress_negative_value","substring","trim","extract_year_from_date","flatten")
    val processors = PreAndPostProcessorUtility.getProcessors
    knownProcessors.forall(processors.contains) shouldBe true
  }
}