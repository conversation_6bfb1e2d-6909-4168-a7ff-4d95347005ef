package me.socure.rulecode.common.processors

import org.json4s.{JArra<PERSON>, J<PERSON>oth<PERSON>, JString, JsonAST}
import org.scalatest.{FreeSpec, Matchers}

class ParseBrazilNamesProcessorTest extends FreeSpec with Matchers{
  val dataMap: Map[JsonAST.JValue, JsonAST.JValue] = Map(
    JArray(List(JString("Maria <PERSON> Souza"), JString("firstName"))) -> JArray(List(JString("Maria"))),
    JArray(List(JString("<PERSON>"), JString("lastName"))) -> JArray(List(JString("Souza"))),
    JArray(List(JString("<PERSON>"), JString("middleName"))) -> JArray(List(JString("Castro"))),
    JArray(List(JString("<PERSON> Souza"), JString("firstName"))) -> JArray(List(JString("<PERSON>"))),
    J<PERSON><PERSON>y(List(JString("<PERSON>"), JString("lastName"))) -> JArray(List(JString("Souza"))),
    JArray(List(JString("<PERSON>"), JString("Neide Cabral da Costa"), JString("firstName"))) -> JArray(List(JString("Maria"),JString("Neide"))),
    JArray(List(JString("Maria Castro Souza"), JString("Neide Cabral da Costa"), JString("lastName"))) -> JArray(List(JString("Souza"),JString("da Costa"))),
    JArray(List(JString("Maria Castro Souza"), JString("Neide Cabral da Costa"), JString("middleName"))) -> JArray(List(JString("Castro"),JString("Cabral"))),
    JArray(List(JString("Maria Castro Souza"), JString("Neide Cabral da Costa"),JString("Ana maria de almeida"), JString("firstName"))) -> JArray(List(JString("Maria"),JString("Neide"),JString("Ana"))),
    JArray(List(JString("Maria Castro Souza"), JString("Neide Cabral da Costa"),JString("Ana maria de almeida"), JString("lastName"))) -> JArray(List(JString("Souza"),JString("da Costa"),JString("de almeida"))),
    JNothing -> JNothing
  )

  "Parse Brazil names processor test" - {
    dataMap.foreach(entry => {
      val name = entry._1
      val expected = entry._2
      s"$name shouldBe $expected" in {
        ParseBrazilNamesProcessor.process(name) shouldBe expected
      }
    })
  }
}
