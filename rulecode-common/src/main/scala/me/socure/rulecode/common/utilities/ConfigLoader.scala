package me.socure.rulecode.common.utilities

import com.typesafe.config.{Config, ConfigException, ConfigFactory}
import me.socure.common.config.{ConfigurationLoader, EnvironmentConfigurationProvider}
import me.socure.common.environment.{AppNameResolver, EnvironmentResolver}
import me.socure.common.resource.ResourceLoader
import me.socure.rulecode.common.FileDefinitionUtility
import me.socure.rulecode.common.models._
import org.json4s.JsonAST.{JNothing, JValue}
import org.json4s.jackson.JsonMethods
import org.json4s.jackson.JsonMethods.parse
import org.slf4j.{Logger, LoggerFactory}

import java.nio.file.Path
import java.util
import javax.inject.Singleton
import scala.collection.JavaConverters._
import scala.io.Source
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

object ConfigLoader {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  private final val JsonFileExtension = ".json"
  private final val DependencyFileName = "dependency.json"

  private final val DependenciesKey = "dependencies"
  private final val DependenciesNameKey = "name"
  private final val DependenciesTypeKey = "type"
  private final val DependenciesOptionalKey = "optional"
  private final val DependenciesPrimaryLookupKey = "primary_lookup"

  private final val RuleCodeVersionKey = "version"
  private final val RuleCodeRulesKey = "rules"
  private final val RuleCodeOperatorsKey = "operators"
  private final val RuleCodeNameKey = "rule_code_name"
  private final val RuleCodeTypeKey = "type"
  private final val RuleCodeDefaultKey = "default"
  private final val RuleCodeComputeOnEmptyLookup = "compute_on_empty_lookup"
  private final val RuleCodeIsDerived = "is_derived"
  private final val RuleCodeEntryConditionOperatorsKey = "entry_condition_operators"
  private final val RuleCodeEntryConditionDefaultKey = "entry_condition_default"

  private final val VendorVariablesKey = "vendor_variables"
  private final val VendorVariablesNameKey = "variable_name"
  private final val VendorVariablesProcessorKey = "processors"

  private final val OperatorNameKey = "name"
  private final val OperatorInputsKey = "inputs"
  private final val OperatorOutputKey = "output"
  private final val OperatorOptionsKey = "options"
  private final val OperatorPreProcessorKey = "preprocessors"
  private final val OperatorPostProcessorKey = "postprocessors"

  private final val OptionsNameKey = "name"
  private final val OptionsValueKey = "value"

  private final val OperatorProcessorMethodsKey = "methods"
  private final val OperatorProcessorInputsKey = "inputs"
  private final val OperatorProcessorOutputKey = "output"
  private final val OperatorProcessorFlattenInputsKey = "flattenInputs"

  private def getConfigFromFilePath(resourceLoader: ResourceLoader, filePath: Path) = {
    ConfigFactory.parseString(Source.fromInputStream(resourceLoader.load(filePath)).getLines().mkString)
  }
  @Singleton
  def getConfigs: Config = {
    val configurationLoaderFactory: EnvironmentConfigurationProvider = new EnvironmentConfigurationProvider
    val configurationLoader: ConfigurationLoader = configurationLoaderFactory.get(EnvironmentResolver.resolve(), AppNameResolver.resolve)
    configurationLoader.load(EnvironmentResolver.resolve())
  }

  def getTableDefinitions(resourceLoader: ResourceLoader, config: Config): Seq[TableDefinition] = {
    try {
      val mscvTables = if(config.hasPath("read_table_definitions_from_mscv")) config.getStringList("read_table_definitions_from_mscv") else new util.ArrayList[String]()
      val resources = resourceLoader.listResources(_.endsWith(".json")).toList.filterNot(table => mscvTables.contains(table.toString))
      logger.info(s"Table Definitions Resource List Size : ${resources.size}")
      if (resources.isEmpty) {
        throw new IllegalArgumentException("No table definitions found")
      }

      val tableList = resources.foldLeft(List.empty[Config]) { (current, next) =>
        current :+ ConfigFactory.parseString(Source.fromInputStream(resourceLoader.load(next)).getLines().mkString)
      }

      val tableDefinitionList = tableList.map(TableDefinitionUtility.getTableDefinitionSeqFromConfig)

      tableDefinitionList
    } catch {
      case ce: ConfigException =>
        logger.error(s"ConfigException occured while fetching config file for 'table_definitions'", ce)
        throw new IllegalArgumentException("Error while fetching table_definitions")
      case NonFatal(ex) =>
        logger.error(s"Exception [${ex.getClass}] occured while fetching config file for 'table_definitions'", ex)
        throw new IllegalArgumentException("Error while fetching table_definitions")
    }
  }

  def getMSCVTableDefinitions(resourceLoader: ResourceLoader,config: Config): Seq[TableDefinition] = {
    try {
      val mscvTables = if(config.hasPath("read_table_definitions_from_mscv")) config.getStringList("read_table_definitions_from_mscv") else new util.ArrayList[String]()
      val resources = resourceLoader.listResources(_.endsWith(".json")).toList.filter(table => mscvTables.contains(table.toString))
      logger.info(s"Table Definitions Resource List Size in MSCV : ${resources.size}")
      if (resources.nonEmpty) {
        val tableList = resources.foldLeft(List.empty[Config]) { (current, next) =>
          current :+ ConfigFactory.parseString(Source.fromInputStream(resourceLoader.load(next)).getLines().mkString)
        }
        val tableDefinitionList = tableList.map(TableDefinitionUtility.getTableDefinitionSeqFromConfig)
        tableDefinitionList
      } else {
        Seq.empty[TableDefinition]
      }
    } catch {
      case ce: ConfigException =>
        logger.error(s"ConfigException occured while fetching config file from MSCV for 'table_definitions'", ce)
        throw new IllegalArgumentException("Error while fetching table_definitions from MSCV")
      case NonFatal(ex) =>
        logger.error(s"Exception [${ex.getClass}] occured while fetching config file from MSCV for 'table_definitions'", ex)
        throw new IllegalArgumentException("Error while fetching table_definitions from MSCV")
    }
  }

  def getHttpDefinitions(resourceLoader: ResourceLoader): Seq[JValue] = {
    try {
      val resources = resourceLoader.listResources(_.endsWith(".json")).toList
      logger.info(s"Http Definitions Resource List Size : ${resources.size}")
      if (resources.isEmpty) {
        logger.warn("No http definitions found")
      }

      resources.foldLeft(Seq.empty[JValue]) { (current, next) =>
        Try(JsonMethods.parse(Source.fromInputStream(resourceLoader.load(next)).getLines().mkString)) match {
          case Success(value) => current :+ value
          case Failure(error) =>
            logger.warn(s"Error in parsing http definition config file for ${next} ", error)
            current
        }
      }
    } catch {
      case ce: ConfigException =>
        logger.error(s"ConfigException occured while fetching config file for 'http_definitions'", ce)
        throw new IllegalArgumentException("Error while fetching http_definitions")
      case NonFatal(ex) =>
        logger.error(s"Exception [${ex.getClass}] occured while fetching config file for 'http_definitions'", ex)
        throw new IllegalArgumentException("Error while fetching http_definitions")
    }
  }

  def getFileDefinitions(resourceLoader: ResourceLoader): Seq[FileDefinition] = {
    try {
      val resources = resourceLoader.listResources(_.endsWith(".json")).toList
      logger.info(s"File Definitions Resource List Size : ${resources.size}")
      if (resources.isEmpty) {
        logger.warn("No file definitions found")
      }
      resources.foldLeft(Seq.empty[FileDefinition]) { (current, next) =>
        val config = ConfigFactory.parseString(Source.fromInputStream(resourceLoader.load(next)).getLines().mkString)
        current :+ FileDefinitionUtility.getFromConfig(config)
      }
    } catch {
      case ce: ConfigException =>
        logger.error(s"ConfigException occured while fetching config file for 'file_definitions'", ce)
        throw new IllegalArgumentException("Error while fetching file_definitions")
      case NonFatal(ex) =>
        logger.error(s"Exception [${ex.getClass}] occured while fetching config file for 'file_definitions'", ex)
        throw new IllegalArgumentException("Error while fetching file_definitions")
    }
  }

  private def mayBeExtractAsBoolean(config: Config, key: String, default: Boolean): Boolean = if (config.hasPath(key)) {
    config.getBoolean(key)
  } else default

  def getRuleCodeDefinitions(resourceLoader: ResourceLoader): Seq[RuleCodeDefinition] = {
    try {
      val resources = resourceLoader.listResources(_.endsWith(".json")).toList
      if (resources.isEmpty) {
        throw new IllegalArgumentException("No rulecode definition files found")
      }
      val resourcesGroupMap = resources.groupBy(_.getParent)
      resourcesGroupMap.foldLeft(Seq.empty[RuleCodeDefinition]) { case (seq, (key, value)) =>
        //expect atleast one dependency and one rulecode definition file per vendor
        if (value.size < 2) {
          throw new IllegalArgumentException(s"RuleCode Definition resource files size was less than two for ${key}. Expected greater than or equal to two")
        }
        val dependencyFile = value.sorted.head
        val pathGroupMap = value.sorted.tail.groupBy(_.toAbsolutePath.toString.endsWith("/vendor_config.json"))
        val ruleFiles = pathGroupMap.getOrElse(false, List.empty[Path])
        val vendorConfigFile = pathGroupMap.getOrElse(true, List.empty[Path])

        if (!dependencyFile.toAbsolutePath.toString.endsWith("/dependency.json")) {
          throw new IllegalArgumentException(s"Dependency file missing for ${key}")
        }
        val dependencyConfig = ConfigFactory.parseString(Source.fromInputStream(resourceLoader.load(dependencyFile)).getLines().mkString).getConfigList("dependencies")
        val dependencies = dependencyConfig.asScala.foldLeft(Seq.empty[RuleDependency]) { (depSoFar, dep) =>
          depSoFar ++ Seq(RuleDependency(dep.getString("name"), dep.getString("type"), mayBeExtractAsBoolean(dep, DependenciesOptionalKey, default = false), mayBeExtractAsBoolean(dep, DependenciesPrimaryLookupKey, default = true)))
        }
        logger.debug(s"Dependencies : ${dependencies}")
        val rules = ruleFiles.foldLeft(Seq.empty[RuleOperator]) { (ruleSeq, ruleFile) =>
          val ruleConfig = ConfigFactory.parseString(Source.fromInputStream(resourceLoader.load(ruleFile)).getLines().mkString).getConfigList("rules")
          val currentRules = ruleConfig.asScala.foldLeft(Seq.empty[RuleOperator]) { (rulesSoFar, rule) =>
            val operator = rule.getConfig("operator")
            val computeOnEmptyLookup = if (rule.hasPath(RuleCodeComputeOnEmptyLookup)) rule.getBoolean(RuleCodeComputeOnEmptyLookup) else false
            val operatorConfig: JValue = if (operator.hasPath("config")) {
              Json4sUtility.configToJValue(operator.getConfig("config"))
            } else JNothing
            rulesSoFar ++ Seq(RuleOperator(
              operator.getString("name"),
              operator.getStringList("inputs").asScala.toSeq,
              rule.getString("rule_code_name"),
              computeOnEmptyLookup = computeOnEmptyLookup,
              operatorConfig))
          }
          ruleSeq ++ currentRules
        }
        logger.debug(s"Rules : ${rules.toString()}")
        val vendorConfigJValue = if (vendorConfigFile.nonEmpty) {
          Try(parse(Source.fromInputStream(resourceLoader.load(vendorConfigFile.head)).getLines().mkString)) match {
            case Success(value) => value
            case Failure(error) =>
              logger.warn(s"Error in parsing vendor config file for ${key} ", error)
              JNothing
          }
        } else {
          JNothing
        }
        seq ++ Seq(RuleCodeDefinition(dependencies, rules, vendorConfigJValue))
      }
    } catch {
      case ce: ConfigException =>
        logger.error(s"ConfigException occurred while fetching config file for 'rulecode_definitions'", ce)
        throw new IllegalArgumentException("Error while fetching rulecode_definitions")
      case NonFatal(ex) =>
        logger.error(s"Exception [${ex.getClass}] occurred while fetching config file for 'rulecode_definitions'", ex)
        throw new IllegalArgumentException("Error while fetching rulecode_definitions")
    }
  }

  private def getProcessors(path: String, config: Config) = {
    if (config.hasPath(path)) {
      config.getConfigList(path).asScala.foldLeft(Seq.empty[RuleCodeOperatorProcessor])(
        (processors, processorConfig) =>
          processors :+ RuleCodeOperatorProcessor(
            methods = processorConfig.getStringList(OperatorProcessorMethodsKey).asScala.toSeq,
            inputs = processorConfig.getStringList(OperatorProcessorInputsKey).asScala.toSeq,
            output = processorConfig.getString(OperatorProcessorOutputKey),
            flattenInputs = if (processorConfig.hasPath(OperatorProcessorFlattenInputsKey)) {
              processorConfig.getBoolean(OperatorProcessorFlattenInputsKey)
            } else true
          )
      )
    } else {
      Seq.empty[RuleCodeOperatorProcessor]
    }
  }

  private def toRuleCodeCollection(resourceLoader: ResourceLoader, parent: Path, subPaths: List[Path]): Seq[RuleCodeCollection] = {
    if (subPaths.size < 2) {
      throw new IllegalArgumentException(s"Unable to get RuleCodeConfiguration V2 as some files are missing at ${parent} folder")
    } else {
      val dependencies = {
        val dependencyFile = subPaths.find(_.endsWith(DependencyFileName)).getOrElse(
          throw new IllegalArgumentException(s"Dependency file missing for ${parent}")
        )
        getConfigFromFilePath(resourceLoader, dependencyFile).getConfigList(DependenciesKey)
          .asScala.foldLeft(Seq.empty[RuleDependency]) { (dependencies, dependencyConfig) =>
          dependencies :+ RuleDependency(dependencyConfig.getString(DependenciesNameKey), dependencyConfig.getString(DependenciesTypeKey),
            mayBeExtractAsBoolean(dependencyConfig, DependenciesOptionalKey, default = false), mayBeExtractAsBoolean(dependencyConfig, DependenciesPrimaryLookupKey, default = true))
        }
      }

      val ruleCodeFiles = subPaths.groupBy(!_.toAbsolutePath.endsWith(DependencyFileName)).getOrElse(true, List.empty[Path])
      ruleCodeFiles.foldLeft(Seq.empty[RuleCodeCollection]) { (ruleCodeList, ruleCodeFile) =>
        val ruleCodeConfig = ConfigFactory.parseString(Source.fromInputStream(resourceLoader.load(ruleCodeFile)).getLines().mkString)
        val version = ruleCodeConfig.getString(RuleCodeVersionKey)
        val rules = ruleCodeConfig.getConfigList(RuleCodeRulesKey)
        val ruleCodeDefinitions = rules.asScala.foldLeft(Seq.empty[RuleCodeDefinitionV2])(populateRuleCodeDefinitions)
        val vendorVariables = if (ruleCodeConfig.hasPath(VendorVariablesKey)) {
          val variablesConfig = ruleCodeConfig.getConfigList(VendorVariablesKey)
          variablesConfig.asScala.foldLeft(Seq.empty[RuleCodeVendorVariable])(populateVendorVariables)
        } else Seq.empty[RuleCodeVendorVariable]
        ruleCodeList :+ RuleCodeCollection(dependencies, version, ruleCodeDefinitions, vendorVariables)
      }
    }
  }

  private def populateRuleCodeDefinitions(rules: Seq[RuleCodeDefinitionV2], ruleConfig: Config): Seq[RuleCodeDefinitionV2] = {
    val name = ruleConfig.getString(RuleCodeNameKey)
    val ruleCodeType = ruleConfig.getString(RuleCodeTypeKey)
    val default = ruleConfig.getString(RuleCodeDefaultKey)
    val computeOnEmptyLookup = if (ruleConfig.hasPath(RuleCodeComputeOnEmptyLookup)) ruleConfig.getBoolean(RuleCodeComputeOnEmptyLookup) else false
    val operators = populateOperatorsV2(ruleConfig, RuleCodeOperatorsKey)
    val isDerivedRule = if (ruleConfig.hasPath(RuleCodeIsDerived)) ruleConfig.getBoolean(RuleCodeIsDerived) else false
    val entryConditionOperators = if (ruleConfig.hasPath(RuleCodeEntryConditionOperatorsKey)) populateOperatorsV2(ruleConfig, RuleCodeEntryConditionOperatorsKey) else Seq.empty
    val entryConditionDefault = if(ruleConfig.hasPath(RuleCodeEntryConditionDefaultKey)) Option(ruleConfig.getString(RuleCodeEntryConditionDefaultKey)) else None
    rules :+ RuleCodeDefinitionV2(name, ruleCodeType, default, computeOnEmptyLookup, operators, isDerivedRule, entryConditionOperators,entryConditionDefault)
  }

  private def populateVendorVariables(variables: Seq[RuleCodeVendorVariable], ruleConfig: Config): Seq[RuleCodeVendorVariable] = {
    val name = ruleConfig.getString(VendorVariablesNameKey)
    val processors = if (ruleConfig.hasPath(VendorVariablesProcessorKey)) getProcessors(VendorVariablesProcessorKey, ruleConfig) else Seq.empty[RuleCodeOperatorProcessor]
    val operators = if (ruleConfig.hasPath(RuleCodeOperatorsKey)) populateOperatorsV2(ruleConfig,RuleCodeOperatorsKey ) else Seq.empty[RuleCodeOperatorV2]
    variables :+ RuleCodeVendorVariable(name, processors, operators)
  }

  private def populateOperatorsV2(ruleConfig: Config, key : String): Seq[RuleCodeOperatorV2] = {
    ruleConfig.getConfigList(key).asScala.foldLeft(Seq.empty[RuleCodeOperatorV2]) {
      (operators, operatorConfig) =>
        operators :+ RuleCodeOperatorV2(
          name = operatorConfig.getString(OperatorNameKey),
          inputs = operatorConfig.getStringList(OperatorInputsKey).asScala.toSeq,
          output = operatorConfig.getString(OperatorOutputKey),
          options = operatorConfig.getConfigList(OperatorOptionsKey).asScala.foldLeft(Seq.empty[RuleCodeOperatorOption]) {
            (options, optionConfig) =>
              options :+ RuleCodeOperatorOption(name = optionConfig.getString(OptionsNameKey), value = optionConfig.getString(OptionsValueKey))
          },
          preprocessors = getProcessors(OperatorPreProcessorKey, operatorConfig),
          postprocessors = getProcessors(OperatorPostProcessorKey, operatorConfig)
        )
    }
  }

  private def getRuleCodeCollections(vendor: Option[Vendor] = None, resourceLoader: ResourceLoader): Map[Vendor, RuleCodeCollection] = {
    try {
      val resources = resourceLoader.listResources(_.endsWith(JsonFileExtension)).toList
      val resourcesGroupedByVendor = resources.groupBy(_.getParent)

      vendor match {
        case Some(vendor) if vendor.name.nonEmpty =>
          Map(vendor -> resourcesGroupedByVendor
            .filter(vendorRulecodes => vendorRulecodes._1.toAbsolutePath.endsWith(vendor.name))
            .flatMap(vendorRulecodes =>
              if (vendorRulecodes._2.isEmpty) throw new IllegalArgumentException(s"No rulecode files found for vendor ${vendor}")
              else toRuleCodeCollection(resourceLoader, vendorRulecodes._1, vendorRulecodes._2)
            )
            .toSeq
            .fold(RuleCodeCollection(Seq.empty[RuleDependency], "v2", Seq.empty[RuleCodeDefinitionV2], Seq.empty[RuleCodeVendorVariable])) {
              (collection1, collection2) =>
                RuleCodeCollection(
                  dependencies = collection1.dependencies ++ collection2.dependencies,
                  version = "v2",
                  rules = collection1.rules ++ collection2.rules,
                  vendorVariables = collection1.vendorVariables ++ collection2.vendorVariables
                )
            }
          )
        case _ =>
          resourcesGroupedByVendor
            .map(
              vendorRuleCodes => {
                Vendor(vendorRuleCodes._1.toAbsolutePath.toString.split("/").last) ->
                  toRuleCodeCollection(resourceLoader, vendorRuleCodes._1, vendorRuleCodes._2)
                    .fold(RuleCodeCollection(Seq.empty[RuleDependency], "v2", Seq.empty[RuleCodeDefinitionV2], Seq.empty[RuleCodeVendorVariable])) {
                      (collection1, collection2) =>
                        RuleCodeCollection(
                          dependencies = collection1.dependencies ++ collection2.dependencies,
                          version = "v2",
                          rules = collection1.rules ++ collection2.rules,
                          vendorVariables = collection1.vendorVariables ++ collection2.vendorVariables
                        )
                    }
              }
            )
      }
    } catch {
      case NonFatal(ex) =>
        logger.error(s"Exception [${ex.getClass}] occurred while fetching config file for 'rulecode_definitions'", ex)
        throw new IllegalArgumentException("Error while fetching rulecode_definitions")
    }
  }

  def fetchRuleCodeConfiguration(tableResourceLoader: ResourceLoader,
                                 mscvTableResourceLoader: ResourceLoader,
                                 ruleCodeResourceLoader: ResourceLoader,
                                 httpResourceLoader: ResourceLoader
                                ): RuleCodeConfiguration = {
    val tableDefinitions = getTableDefinitions(tableResourceLoader,getConfigs)  ++ getMSCVTableDefinitions(mscvTableResourceLoader,getConfigs)
    val ruleCodeDefinitions = getRuleCodeDefinitions(ruleCodeResourceLoader)
    val httpDefinitions = getHttpDefinitions(httpResourceLoader)
    logger.info(s"Loaded ${tableDefinitions.size} tables, ${httpDefinitions.size} http and ${ruleCodeDefinitions.size} rulecode definitions")
    RuleCodeConfiguration(tableDefinitions, ruleCodeDefinitions, httpDefinitions)
  }

  def fetchRuleCodeConfigurationV2(
                                    vendor: Option[Vendor] = None,
                                    tableResourceLoader: ResourceLoader,
                                    mscvTableResourceLoader: ResourceLoader,
                                    httpResourceLoader: ResourceLoader,
                                    fileResourceLoader: ResourceLoader,
                                    ruleCodeResourceLoader: ResourceLoader
                                  ): RuleCodeConfigurationV2 = {
    print(s"find env name ${System.getenv("APP_ENVIRONMENT")}")
    print(s"find config name ${System.getenv("CONFIGURATION_NAME")}")
    val tableDefinitions = getTableDefinitions(tableResourceLoader,getConfigs) ++ getMSCVTableDefinitions(mscvTableResourceLoader,getConfigs)
    val httpDefinitions = getHttpDefinitions(httpResourceLoader)
    val fileDefinitions = getFileDefinitions(fileResourceLoader)
    val ruleCodeCollections = getRuleCodeCollections(vendor, ruleCodeResourceLoader)
    logger.info(s"V2: Loaded ${tableDefinitions.size} tables, ${httpDefinitions.size} http, ${fileDefinitions.size} files and ${ruleCodeCollections.values.flatMap(_.rules).toSeq.size} rulecode definitions")
    RuleCodeConfigurationV2(tableDefinitions, httpDefinitions, fileDefinitions, ruleCodeCollections)
  }
}
