package me.socure.rulecode.common.bestmatch.resolvers

import me.socure.rulecode.common.bestmatch.model.BestMatchResolver
import me.socure.rulecode.common.models.StreetAddress
import me.socure.rulecode.common.utilities.{DateUtility, Json4sUtility, MatcherUtility}
import org.json4s
import org.json4s.JsonAST.JArray
import org.json4s.native.JsonMethods
import org.json4s.{JsonAST, _}

object SCPVLPrefillBestMatchResolver extends BestMatchResolver {

  override def resolve(requestJsonAST: JsonAST.JValue, dbJson: JValue): JsonAST.JValue = {
    resolvePersonForPrefill(requestJsonAST, dbJson)
  }

  private def resolvePersonForPrefill(requestJson: json4s.JValue, value: json4s.JValue): JValue = {
    value match {
      case JObject(_) =>
        Json4sUtility.getOptionalJValue(value, "subscr_dtl") match {
          case Some(JArray(subscrDtl)) if subscrDtl.nonEmpty =>
            // Input PIIs
            val inputFirstNameOpt = Json4sUtility.getOptionalString(requestJson, "first_name").map(_.trim.toLowerCase)
            val inputLastNameOpt = Json4sUtility.getOptionalString(requestJson, "last_name").map(_.trim.toLowerCase)
            val inputDobOpt = Json4sUtility.getOptionalString(requestJson, "raw.dob")
            val inputEmailOpt = Json4sUtility.getOptionalString(requestJson, "raw.email").map(_.trim.toLowerCase)
            val inputSSNOpt = Json4sUtility.getOptionalString(requestJson, "ssn").map(_.trim.toLowerCase)
            val inputStreetOpt = Json4sUtility.getOptionalString(requestJson, "street").map(_.trim.toLowerCase)
            val inputCityOpt = Json4sUtility.getOptionalString(requestJson, "city").map(_.trim.toLowerCase)
            val inputStateOpt = Json4sUtility.getOptionalString(requestJson, "state").map(MatcherUtility.cleanN)
            val inputZipOpt = Some(List(
              Json4sUtility.getOptionalString(requestJson, "zip").map(MatcherUtility.cleanN).getOrElse(""),
              Json4sUtility.getOptionalString(requestJson, "zip4").map(MatcherUtility.cleanN).getOrElse("")
            ).mkString("")).map(_.trim.toLowerCase)
            val inputMobileNumberOpt = Json4sUtility.getOptionalString(requestJson, "raw.mobileNumber").map(_.trim.toLowerCase)

            val dataWithScore = subscrDtl.map { lookup =>
              val (firstNameMatchObj, lastNameMatchObj) = getNameMatchObj(lookup, inputFirstNameOpt, inputLastNameOpt)
              val dobMatchObj = getDobMatchObj(lookup, inputDobOpt)
              val emailMatchObj = getEmailMatchObj(lookup, inputEmailOpt)
              val ssnMatchObj = getSsnMatchObj(lookup, inputSSNOpt)
              val addrMatchObj = getAddressMatchObj(lookup, inputStreetOpt, inputCityOpt, inputStateOpt, inputZipOpt)

              val totalScore = firstNameMatchObj.map(_._2).getOrElse(0) +
                lastNameMatchObj.map(_._2).getOrElse(0) +
                dobMatchObj.map(_._2).getOrElse(0) +
                emailMatchObj._1.map(_._2).getOrElse(0) +
                ssnMatchObj.map(_._2).getOrElse(0) +
                addrMatchObj._1.map(_._2).getOrElse(0)

              val firstSeen = Json4sUtility.getOptionalString(lookup, "subscr_smmry.fs").getOrElse("")
              val (maxLastSeenDateStr, maxLastSeenDate) = getMaxPersonLastSeen(lookup)

              val phoneObj = createJObject(List(
                inputMobileNumberOpt.fold(Option.empty[(String, JValue)])(e => Some("value" -> JString(e))),
                if (firstSeen.nonEmpty) Some("first_seen" -> JString(firstSeen)) else None,
                maxLastSeenDateStr.fold(Option.empty[(String, JValue)])(e => Some("last_seen" -> JString(e)))
              ).flatten)

              val data = createJObject(List(
                firstNameMatchObj.fold(Option.empty[(String, JValue)])(e => Some("first_name" -> e._1)),
                lastNameMatchObj.fold(Option.empty[(String, JValue)])(e => Some("last_name" -> e._1)),
                dobMatchObj.fold(Option.empty[(String, JValue)])(e => Some("dob" -> e._1)),
                emailMatchObj._1.fold(Option.empty[(String, JValue)])(e => Some("matched_email" -> e._1)),
                ssnMatchObj.fold(Option.empty[(String, JValue)])(e => Some("ssn" -> e._1)),
                addrMatchObj._1.fold(Option.empty[(String, JValue)])(e => Some("matched_addr" -> e._1)),
                if (addrMatchObj._2.nonEmpty) Some("associated_addrs" -> JArray(addrMatchObj._2)) else None,
                if (emailMatchObj._2.nonEmpty) Some("associated_emails" -> JArray(emailMatchObj._2)) else None,
                Some("associated_phones" -> JArray(List(phoneObj)))
              ).flatten)

              (data, totalScore, maxLastSeenDate)
            }
            dataWithScore.filter(_._2 > 0).sortWith((x, y) => if (x._2 == y._2) x._3 > y._3 else x._2 > y._2).headOption.map(_._1).getOrElse(JNothing)
          case _ => JNothing
        }
      case _ => JNothing
    }
  }

  private def getNameMatchObj(lookup: JValue, inputFirstNameOpt: Option[String], inputLastNameOpt: Option[String]): (Option[(JValue, Int)], Option[(JValue, Int)]) = {
    val lookupFirstName = Json4sUtility.getOptionalString(lookup, "fn").getOrElse("").trim.toLowerCase
    val lookupLastName = Json4sUtility.getOptionalString(lookup, "ln").getOrElse("").trim.toLowerCase

    val firstNameMatch = inputFirstNameOpt.exists(MatcherUtility.matchN(_, lookupFirstName))
    val lastNameMatch = inputLastNameOpt.exists(MatcherUtility.matchN(_, lookupLastName))
    val firstNameSwapMatch = inputFirstNameOpt.exists(MatcherUtility.matchN(_, lookupLastName))
    val lastNameSwapMatch = inputLastNameOpt.exists(MatcherUtility.matchN(_, lookupFirstName))
    val nameSwapMatch = firstNameSwapMatch || lastNameSwapMatch

    val firstNameMatchObjInt = if (firstNameMatch) {
      Some(JString(lookupFirstName), 4)
    } else Some(JString(lookupFirstName), 0)

    val lastNameMatchObjInt = if (lastNameMatch) {
      Some(JString(lookupLastName), 6)
    } else Some(JString(lookupLastName), 0)

    if (firstNameMatchObjInt.isEmpty && lastNameMatchObjInt.isEmpty && nameSwapMatch) {
      val fnObj = if (firstNameSwapMatch) {
        Some(JString(lookupLastName), 6)
      } else Some(JString(lookupLastName), 0)

      val lnObj = if (lastNameSwapMatch) {
        Some(JString(lookupFirstName), 4)
      } else Some(JString(lookupFirstName), 0)
      (fnObj, lnObj)
    } else (firstNameMatchObjInt, lastNameMatchObjInt)
  }

  private def getDobMatchObj(lookup: JValue, inputDobOpt: Option[String]): Option[(JValue, Int)] = {
    val lookupDob = Json4sUtility.getOptionalString(lookup, "dob")
    val lookupDobEx = lookupDob.flatMap(DateUtility.getDateTime(_, runThruAllFormatsOnFailure = true))
    val inputDob = inputDobOpt.map(_.toLong).map(DateUtility.getDateTime)
    val dobMatch = (lookupDobEx, inputDob) match {
      case (Some(a), Some(b)) => DateUtility.getDaysDifference(a, b) == 0
      case _ => false
    }
    if (dobMatch) {
      Some(JString(lookupDob.getOrElse("")), 6)
    } else Some(JString(lookupDob.getOrElse("")), 0)
  }

  private def getEmailMatchObj(lookup: JValue, inputEmailOpt: Option[String]): (Option[(JValue, Int)], List[JValue]) = {
    val emailDtl = lookup \ "email_dtl"
    emailDtl.children match {
      case emailList: List[JValue] if emailList.nonEmpty =>
        val sortedData = sort(emailList.map(email => {
          val lookupEmail = Json4sUtility.getOptionalString(email, "email").getOrElse("").trim.toLowerCase
          val lookupFirstSeen = Json4sUtility.getOptionalString(email, "fs").getOrElse("").trim.toLowerCase

          val emailMatch = inputEmailOpt.exists(MatcherUtility.matchN(_, lookupEmail))

          val totalScore = if (emailMatch) 1 else 0

          val (maxLastSeenDateStr, maxLastSeenDate) = getMaxLastSeenDate(email)

          val data = createJObject(List(
            if (lookupEmail.nonEmpty) Some("value" -> JString(lookupEmail)) else None,
            if (lookupFirstSeen.nonEmpty) Some("first_seen" -> JString(lookupFirstSeen)) else None,
            if (maxLastSeenDateStr.nonEmpty) Some("last_seen" -> JString(maxLastSeenDateStr.get)) else None
          ).flatten)

          (data, totalScore, maxLastSeenDate)
        }))
        (sortedData.headOption, sortedData.map(_._1))
      case _ => (Option.empty[(JValue, Int)], List.empty[JValue])
    }
  }

  private def getSsnMatchObj(lookup: JValue, inputSSNOpt: Option[String]): Option[(JValue, Int)] = {
    val ssnDtl = lookup \ "ssn_dtl"
    ssnDtl.children match {
      case ssnList: List[JValue] if ssnList.nonEmpty =>
        sortAndRetrieve(ssnList.map(ssn => {
          val lookupSsn = Json4sUtility.getOptionalString(ssn, "ssn").getOrElse("").trim.toLowerCase

          val ssnMatch = inputSSNOpt.exists(MatcherUtility.matchN(_, lookupSsn))

          val totalScore = if (ssnMatch) 10 else 0

          val maxLastSeenDate = getMaxLastSeenDate(ssn)._2

          (JString(lookupSsn), totalScore, maxLastSeenDate)
        }))
      case _ => Option.empty[(JValue, Int)]
    }
  }

  private def getAddressMatchObj(lookup: JValue, inputStreetOpt: Option[String], inputCityOpt: Option[String], inputStateOpt: Option[String], inputZipOpt: Option[String]): (Option[(JValue, Int)], List[JValue]) = {
    val addrDtl = lookup \ "addr_dtl"
    addrDtl.children match {
      case addrList: List[JValue] if addrList.nonEmpty =>
        val sortedData = sort(addrList.map(addrs => {

          val lookupStreet = Json4sUtility.getOptionalString(addrs, "str").getOrElse("").trim.toLowerCase
          val lookupCity = Json4sUtility.getOptionalString(addrs, "cty").getOrElse("").trim.toLowerCase
          val lookupState = Json4sUtility.getOptionalString(addrs, "st").getOrElse("").trim.toLowerCase
          val lookupZip = Json4sUtility.getOptionalString(addrs, "zip").getOrElse("").trim.toLowerCase
          val lookupFirstSeen = Json4sUtility.getOptionalString(addrs, "fs").getOrElse("").trim.toLowerCase

          val isStreetMatch = inputStreetOpt.exists(street => MatcherUtility.matchStreetAddressOptimized(MatcherUtility.cleanStreetAddress(Some(StreetAddress(street))), Some(StreetAddress(lookupStreet))))
          val isCityMatch = inputCityOpt.exists(city => MatcherUtility.cleanCityState(city).equalsIgnoreCase(MatcherUtility.cleanCityState(lookupCity)))
          val isStateMatch = inputStateOpt.exists(MatcherUtility.cleanN(_).equalsIgnoreCase(lookupState))
          val isZipMatch = inputZipOpt.exists(zip => MatcherUtility.matchZip5(zip, lookupZip))

          val streetMatchScore = if (isStreetMatch) 4 else 0
          val cityMatchScore = if (isCityMatch) 3 else 0
          val stateMatchScore = if (isStateMatch) 1 else 0
          val zipMatchScore = if (isZipMatch) 2 else 0
          val totalScore = streetMatchScore + cityMatchScore + stateMatchScore + zipMatchScore

          val (maxLastSeenDateStr, maxLastSeenDate) = getMaxLastSeenDate(addrs)

          val data = createJObject(List(
            if (lookupStreet.nonEmpty) Some("street" -> JString(lookupStreet)) else None,
            if (lookupCity.nonEmpty) Some("city" -> JString(lookupCity)) else None,
            if (lookupState.nonEmpty) Some("state" -> JString(lookupState)) else None,
            if (lookupZip.nonEmpty) Some("zip" -> JString(lookupZip)) else None,
            if (lookupFirstSeen.nonEmpty) Some("first_seen" -> JString(lookupFirstSeen)) else None,
            if (maxLastSeenDateStr.nonEmpty) Some("last_seen" -> JString(maxLastSeenDateStr.get)) else None
          ).flatten)

          (data, totalScore, maxLastSeenDate)
        }))
        (sortedData.headOption, sortedData.map(_._1))
      case _ => (Option.empty[(JValue, Int)], List.empty[JValue])
    }
  }

  private def getMaxLastSeenDate(value: JValue): (Option[String], Long) = {
    value \ "frq_dtl" match {
      case org.json4s.JArray(frList) if frList.nonEmpty => frList.map(value => {
        val date = Json4sUtility.getOptionalString(value, "ao_dt").getOrElse("")
        (Some(date).filter(_.nonEmpty), DateUtility.getDateTime(date, runThruAllFormatsOnFailure = true).map(_.getMillis).getOrElse(0L))
      }).maxBy(_._2)
      case _ => (Option.empty[String], 0L)
    }
  }

  private def getMaxPersonLastSeen(value: JValue): (Option[String], Long) = {
    Json4sUtility.getOptionalStringArray(value, "subscr_smmry.seen_on_dates") match {
      case Some(dates) if dates.nonEmpty =>
        dates.map(x => (Some(x), DateUtility.getDateTime(x, runThruAllFormatsOnFailure = true).map(_.getMillis).getOrElse(0L))).maxBy(_._2)
      case _ => (Option.empty[String], 0L)
    }
  }

  private def sortAndRetrieve(values: List[(JValue, Int, Long)]): Option[(JValue, Int)] = {
    values.sortWith((x, y) => if (x._2 == y._2) x._3 > y._3 else x._2 > y._2).headOption.map(e => (e._1, e._2))
  }

  private def sort(values: List[(JValue, Int, Long)]): List[(JValue, Int)] = {
    values.sortWith((x, y) => if (x._2 == y._2) x._3 > y._3 else x._2 > y._2).map(e => (e._1, e._2))
  }

  private def createJObject(recentObjects: List[(String, JValue)]): JObject = {
    val nonEmptyEles = recentObjects.filterNot { case (_, value) => value == JNothing }
    val jFields = nonEmptyEles.map { case (key, value) => JField(key, value) }
    JObject(jFields)
  }

}