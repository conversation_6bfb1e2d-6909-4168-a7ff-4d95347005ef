package me.socure.rulecode.common.processors

import me.socure.rulecode.common.models.PreAndPostProcessor
import org.json4s.JsonAST.{JArray, JNothing, JString, JValue}

object ParseBrazilNamesProcessor extends PreAndPostProcessor{
  private val surnameParticles = Set("da", "das", "de", "do", "dos", "e", "di", "du", "del", "van", "von")
  private val suffixExclusions = Set("junior", "filho", "neto", "bisneto", "trineto", "segundo", "terceiro", "quarto", "quinto")
  val firstName = "firstName"
  val lastName = "lastName"
  val middleName = "middleName"
  override def process(dependencyJVal: JValue): JValue = {
    dependencyJVal match {
      case JArray(list) => {
        val fieldName = list.tail.last match {
          case JString(s) => s
          case _ => throw new Exception(" Type of the field name is not valid ")
        }
        val values = list.dropRight(1)
        values match {
          case arr if arr.forall(_ == JNothing) => JNothing
          case arr : List[JString] => JArray(arr.map(name => parseBrazilianName(name.s,fieldName)))
          case _ => JNothing
        }
      }
      case _ => JNothing
    }
  }

  def parseBrazilianName(fullName: String, fieldName:String): JValue = {
    // If null or blank, return empty
    if (fullName == null || fullName.trim.isEmpty) {
      return JString("")
    }

    // Split on whitespace
    val parts = fullName.trim.split("\\s+")
    // The very first part is the first name
    val firstNameValue = parts.head

    // Look for suffix in the last part
    var lastIndex = parts.length
    var hasSuffix = false
    if (suffixExclusions.contains(parts.last.toLowerCase)) {
      lastIndex -= 1
      hasSuffix = true
    }

    // Find the *last* occurrence of a surname particle before any suffix
    var particleStart: Option[Int] = None
    for (i <- 1 until lastIndex) {
      val partLower = parts(i).toLowerCase
      if (surnameParticles.contains(partLower)) {
        particleStart = Some(i)
      }
    }

    // Determine the lastName and the middle name parts
    val (lastNameRaw, middleParts) = particleStart match {
      // If found a particle, lastName starts at that particle
      case Some(idx) =>
        val ln = parts.slice(idx, lastIndex).mkString(" ")
        val mp = parts.slice(1, idx) // everything between firstName and lastName
        (ln, mp)
      // If no particle, use the last part as the lastName
      case None =>
        val ln = parts(lastIndex - 1)
        val mp = parts.slice(1, lastIndex - 1)
        (ln, mp)
    }

    // If there's a suffix, append it
    val finalLastName =
      if (hasSuffix) s"$lastNameRaw ${parts.last}"
      else lastNameRaw

    val middleNameValue = middleParts.mkString(" ")

    if(fieldName.equalsIgnoreCase(firstName)) JString(firstNameValue)
    else if(fieldName.equalsIgnoreCase(middleName)) JString(middleNameValue)
    else if(fieldName.equalsIgnoreCase(lastName)) JString(finalLastName)
    else JNothing
  }
}
