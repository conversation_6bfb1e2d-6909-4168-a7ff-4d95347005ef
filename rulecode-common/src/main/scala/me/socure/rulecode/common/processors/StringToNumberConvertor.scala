package me.socure.rulecode.common.processors

import me.socure.rulecode.common.models.PreAndPostProcessor
import org.json4s.JsonAST.{JInt, JValue}
import org.json4s.{JArray, JDouble, JNothing}

import scala.util.{Failure, Success, Try}

object StringToNumberConvertor extends PreAndPostProcessor {

  override def process(inputs: JValue): JValue = {
    inputs match {
      case JArray(arr) =>
        val dataType = arr.last.extract[String]
        val flatten = if (arr.length > 2) arr(1).extract[String] == "true" else true
        arr.head match {
          case JArray(inputArr) =>
            val result = if (flatten) inputArr.flatMap(convert(_, dataType)).filter(_ != JNothing) else inputArr.flatMap(convert(_, dataType))
            if (result.isEmpty) JNothing else JArray(result)
          case jValue: JValue => convert(jValue, dataType).getOrElse(JNothing)
        }
      case _ => JNothing
    }
  }

  private def convert(jValue: JValue, dataType: String): Option[JValue] = {
    jValue match {
      case JNothing => Some(JNothing)
      case _ =>
        val input = jValue.extractOpt[String].map(_.trim).filter(_.nonEmpty).getOrElse("")
        if (input.nonEmpty) {
          dataType match {
            case "int" =>
              Try(input.toInt) match {
                case Success(value) => Some(JInt(value))
                case Failure(_) => Option.empty[JInt]
              }
            case "double" =>
              Try(input.toDouble) match {
                case Success(value) => Some(JDouble(value))
                case Failure(_) => Option.empty[JDouble]
              }
            case _ => None
          }
        } else None
    }
  }

}
