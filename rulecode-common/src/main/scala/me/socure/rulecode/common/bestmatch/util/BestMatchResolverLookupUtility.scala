package me.socure.rulecode.common.bestmatch.util

import me.socure.rulecode.common.bestmatch.model.BestMatchResolver
import me.socure.rulecode.common.bestmatch.resolvers._

class BestMatchResolverLookupUtility(lookUpMap: Map[String, BestMatchResolver]) {

  def getResolver(methodName: String): BestMatchResolver = {
    lookUpMap.getOrElse(methodName.toLowerCase, EmptyBestMatchResolver)
  }

}

object BestMatchResolverLookupUtility extends BestMatchResolverLookupUtility(
  Map(
    "fcevl" -> FullContactBestMatchResolver,
    "tdevl" -> TowerdataBestMatchResolver,
    "tdevl_swap_match" -> TowerdataBestMatchSwapNameResolver,
    "exevl" -> EquifaxBestMatchResolver,
    "exevl_address" -> EquifaxBestMatchAddressLevelResolver,
    "asavl" -> AslBestMatchResolver,
    "asavl_except_zip" -> AslExceptZipMatchResolver,
    "asavl_street_and_zip" -> AslStreetAndZipMatchResolver,
    "asavl_except_state" -> AslExceptStateMatchResolver,
    "asavl_except_city" -> AslExceptCityMatchResolver,
    "attmvl" -> AttomBestMatchResolver,
    "attmvl_except_zip" -> AttomExceptZipMatchResolver,
    "attmvl_except_state" -> AttomExceptStateMatchResolver,
    "attmvl_except_city" -> AttomExceptCityMatchResolver,
    "attmvl_street_and_zip" -> AttomStreetAndZipMatchResolver,
    "swap_match" -> SwapNameResolver,
    "ssevl_swap_match" -> SocialStatsSwapNameMatchResolver,
    "expvl"-> EquifaxPhoneBestMatchResolver,
    "expvl_recent_phone" -> EquifaxRecentPhoneMatchResolver,
    "enpvl_name"-> EnformionNameBestMatchResolver,
    "enpvl_email"-> EnformionEmailBestMatchResolver,
    "enpvl_addr"-> EnformionAddressBestMatchResolver,
    "enpvl_name_email"-> EnformionNameEmailBestMatchResolver,
    "enpvl_dob"-> EnformionDobBestMatchResolver,
    "exsvl_chm_ssn" -> SyntheticSsnBestMatchResolver,
    "exnvl_chm_dob" -> SyntheticSsnBestMatchResolver,
    "exsvl_lfm_ssn" -> SyntheticSsnBestMatchResolver,
    "asl_name_dob" -> AslFullNameDobBestMatchAddressLevelResolver,
    "exnvl_addr" -> EXNVLAddressResolver,
    "scpvl" -> SCPVLBestMatchResolver,
    "scpvl_prefill" -> SCPVLPrefillBestMatchResolver
  )
)
