package me.socure.rulecode.common.models

import me.socure.common.json.bean.v3_0.Address
import me.socure.common.json.bean.v3_0.device.Device
import me.socure.common.json.bean.v3_0.payment.Payments
import me.socure.pii.standardization.common.model.response.PIIStandardizationResponse
case class RuleCodeRequestRawParams(
                                     email: Option[String],
                                     submissionDate: Option[Long],
                                     transactionDate: Option[Long],
                                     dob: Option[Long],
                                     physicalAddress: Option[String],
                                     physicalAddress2: Option[String],
                                     physicalAddressRaw: Option[String],
                                     city: Option[String],
                                     state: Option[String],
                                     zip: Option[String],
                                     mobileNumber: Option[String],
                                     ip: Option[String],
                                     emailDomain: Option[String] = None,
                                     dobString: Option[String] = None,
                                     ssn: Option[String] = None,
                                     last4: Option[String] = None,
                                     consentStatus: Option[String] = None,
                                     lastVerified: Option[String] = None,
                                     country: Option[String] = None,
                                     geoCode: Option[String] = None,
                                     addresses: Option[Set[Address]] = None ,
                                     driverLicense : Option[String] = None ,
                                     inputEmail: Option[String] = None
                                   )

case class NumericIP(
                      ipv4Int: Option[Long] = None,
                      ipv63Long1: Option[Long] = None,
                      ipv63Long2: Option[Long] = None,
                      ipv63Long3: Option[Long] = None,
                      ipBlock: Option[Long] = None,
                      ipv6Full: Option[String] = None
                    )

case class IdentityPreferences(
                                dobMatchLogic: Option[String] = None
                              )

case class ShippingDetails(
                            shippingLine1: Option[String] = None,
                            shippingLine2: Option[String] = None,
                            shippingCity: Option[String] = None,
                            shippingState: Option[String] = None,
                            shippingPostalCode: Option[String] = None,
                            shippingCountry: Option[String] = None,
                            shippingFirstName: Option[String] = None,
                            shippingSurName: Option[String] = None,
                            shippingEmail: Option[String] = None,
                            shippingEmailHandle: Option[String] = None,
                            shippingMobileNumber: Option[String] = None
                          )

case class OrderDetails(
                         billingCountry: Option[String] = None,
                         billingLine1: Option[String] = None,
                         billingLine2: Option[String] = None,
                         billingCity: Option[String] = None,
                         billingState: Option[String] = None,
                         billingPostalCode: Option[String] = None,
                         billingFirstName: Option[String] = None,
                         billingSurName: Option[String] = None,
                         billingEmail: Option[String] = None,
                         billingEmailHandle: Option[String] = None,
                         billingMobileNumber: Option[String] = None,
                         merchantId: Option[String] = None,
                         eStoreCategory: Option[String] = None,
                         orderChannel: Option[String] = None,
                         orderAmount: Option[Double] = None,
                         paymentMethod: Option[String] = None,
                         disbursementType: Option[String] = None,
                         lastOrderDate: Option[String] = None,
                         accountCreationDate: Option[String] = None,
                         prevUnpaidOrderCount: Option[Double] = None,
                         prevOrderCount: Option[Double] = None
                       )

case class RuleCodeRequest(
                            first_name: Option[String],
                            last_name: Option[String],
                            country_code: Option[Int],
                            phone_number: Option[String],
                            phoneAreaCode: Option[String] = None,
                            street: Option[String],
                            city: Option[String],
                            state: Option[String],
                            county: Option[String] = None,
                            zip: Option[String],
                            zip4: Option[String],
                            country: Option[String],
                            latitude: Option[Double] = None,
                            longitude: Option[Double] = None,
                            vendor: Option[String],
                            maskPii: Boolean,
                            std_address: Option[String] = None,
                            transactionId: Option[String],
                            accountId: Option[Long],
                            ssn: Option[String],
                            raw: Option[RuleCodeRequestRawParams],
                            ip: Option[String],
                            emailMD5: Option[String] = None,
                            gender: Option[String] = None,
                            environmentName: Option[String] = None,
                            emailDomain: Option[String] = None,
                            fullName: Option[String] = None,
                            numericIp: Option[NumericIP] = None,
                            shippingDetails: Option[ShippingDetails] = None,
                            orderDetails: Option[OrderDetails] = None,
                            identityPreferences: Option[IdentityPreferences] = None,
                            piiStdResponse: Option[PIIStandardizationResponse] = None,
                            idPlusRulecodes: Option[IdPlusRulecodes] = None,
                            isFeaturePlatformCall: Option[Boolean] = None, // TODO(PRANSHU): Remove this after fmval traffic routed via FP
                            prefillSsn: Option[String] = None,
                            payments: Option[Payments] = None,
                            device: Option[Device] = None,
                            simSwapKey: Option[String] = None,
                            modules: Option[Set[String]] = None,
                            phoneCountryCode: Option[String] = None,
                            mnoRequest: Option[String] = None,
                            stdAddress2: Option[String] = None
                          )

case class IdPlusRulecodes(
                            categoricalRulecodes: Map[String, Map[String, String]],
                            numericalRulecodes: Map[String, Map[String, Double]]
                          )