package me.socure.rulecode.common.processors

import me.socure.rulecode.common.models.PreAndPostProcessor
import org.json4s.JsonAST.{JArray, JB<PERSON>, JNothing, JString}
import org.json4s.JValue

object LowerCaseProcessor extends PreAndPostProcessor {

  override def process(dependencyJVal: JValue): JValue = {
    dependencyJVal match {
      case JArray(jArr) =>
        val result = jArr.map(
          str => str match {
            case JString(s) => JString(s.toLowerCase)
            case JBool(s) => JString(s.toString.toLowerCase)
            case _ => JNothing
          }
        )
        if (result.size == 1) result.head else JArray(result)
      case JString(str) => JString(str.toLowerCase)
      case JBool(bool) => JString(bool.toString.toLowerCase)
      case _ => JNothing
    }
  }

}