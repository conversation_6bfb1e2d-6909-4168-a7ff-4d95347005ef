package me.socure.rulecode.common.utilities

import com.typesafe.config.{Config, ConfigRenderOptions}
import me.socure.rulecode.common.models._
import org.json4s.JsonAST._
import org.json4s.jackson.JsonMethods
import org.json4s.{DefaultFormats, Formats, JNothing}
import org.slf4j.LoggerFactory

import scala.util.{Failure, Random, Success, Try}

object Json4sUtility {

  private implicit def jsonFormats: Formats = DefaultFormats
  private val logger = LoggerFactory.getLogger(getClass)

  private val FULL_NAME_MATCH_SCORE : Int = 2
  private val SINGLE_NAME_MATCH_SCORE : Int = 1
  private val NO_MATCH_SCORE : Int = 0
  private val PHONE_RISK = "phonerisk"
  private val PREFILL = "prefill"
  private val PHONE_BASIC = "PHONE_BASIC"
  private val IDENTITY_ENRICHMENT = "IDENTITY_ENRICHMENT"
  private val PHONE_SIMSWAP = "PHONE_SIMSWAP"


  private final val AsArraySuffix = "__as_array__"

  def parseJValue(path: String, requestJsonAST: JValue, dbJsonMap: Map[String, JValue], dependencies: Map[String, JValue] = Map.empty[String, JValue]) : JValue = {
    if (path.startsWith("input.")) {
      val derivedPath = path.replace("input.", "")
      if (derivedPath.endsWith("_optional_")) {
        val derivedPathWithoutOptional = derivedPath.replace("_optional_", "")
        val derivedJValue = derivedPathWithoutOptional.split("\\.").foldLeft(requestJsonAST) { (jValue, currentPath) =>
          jValue \ currentPath
        }
        derivedJValue.toOption match {
          case None => JNothing
          case Some(jValue: JValue) => jValue
        }
      } else if (derivedPath.endsWith("_optional_with_null_")) {
        val derivedPathWithoutSuffix = derivedPath.replace("_optional_with_null_", "")
        val derivedJValue = derivedPathWithoutSuffix.split("\\.").foldLeft(requestJsonAST) { (jValue, currentPath) =>
          getDerivedValueOptionalWithNull(currentPath, jValue)
        }
        derivedJValue.toOption match {
          case None => JNothing
          case Some(jValue: JValue) => jValue
        }
      } else {
        val derivedJValue = derivedPath.split("\\.").foldLeft(requestJsonAST) { (jValue, currentPath) =>
          jValue \ currentPath
        }
        derivedJValue.toOption match {
          case None => throw InputNotFoundException(s"No such element '${path}' present in the input request")
          case Some(jValue: JValue) => jValue
        }
      }
    } else if (path.startsWith("table.") || path.startsWith("http.")) {
      val pathsArray = path.split("\\.")
      if (pathsArray.size < 2) {
        throw new IllegalArgumentException(s"Invalid format '${path}'. Expected in this format 'table.<table_name>.<field_name>. field_name is optional")
      }
      val key = pathsArray(0) + "." + pathsArray(1)
      val jsonKey = if (!path.equals(key)) path.replace(key + ".", "") else ""
      if (jsonKey.equalsIgnoreCase("best_index")) {
        if (dbJsonMap.contains(path)) {
          dbJsonMap.get(path).getOrElse(throw new NoSuchElementException(s"No such element '${path}' present in the table response map"))
        } else {
          throw BestIdentityNotFoundException(s"Unable to find best identity index")
        }
      } else if (jsonKey.endsWith("_optional_")) {
        val jsonKeyWithoutOptional = jsonKey.replace("_optional_", "")
        if (dbJsonMap.contains(key)) {
          dbJsonMap.get(key) match {
            case Some(jValue: JValue) =>
              val derivedJValue = jsonKeyWithoutOptional.split("\\.").foldLeft(jValue) { (json, currentPath) =>
                if (currentPath.isEmpty) json
                else if (currentPath.startsWith(RulecodeServiceConstants.BestMatchMethodPrefix)) {
                  dbJsonMap.getOrElse(s"$key.$currentPath", JNothing)
                } else json \ currentPath
              }
              derivedJValue.toOption match {
                case None => JNothing
                case Some(jValue: JValue) => jValue
              }
            case None => JNothing
          }
        } else {
          JNothing
        }
      } else if (jsonKey.endsWith("_optional_with_null_")) {
        val jsonKeyWithoutSuffix = jsonKey.replace("_optional_with_null_", "")
        if (dbJsonMap.contains(key)) {
          dbJsonMap.get(key) match {
            case Some(jValue: JValue) =>
              val derivedJValue = jsonKeyWithoutSuffix.split("\\.").foldLeft(jValue) { (json, currentPath) =>
                if(currentPath.isEmpty) json
                else if (currentPath.startsWith(RulecodeServiceConstants.BestMatchMethodPrefix)) {
                  dbJsonMap.getOrElse(s"$key.$currentPath", JNothing)
                } else {
                  getDerivedValueOptionalWithNull(currentPath, json)
                }
              }
              derivedJValue.toOption match  {
                case None => JNothing
                case Some(jValue: JValue) => jValue
              }
            case None => JNothing
          }
        } else {
          JNothing
        }
      } else {
        if (dbJsonMap.contains(key)) {
          dbJsonMap.get(key) match {
            case Some(jValue: JValue) =>
              val derivedJValue = jsonKey.split("\\.").foldLeft(jValue) { (json, currentPath) =>
                if (currentPath.isEmpty) json
                else if (currentPath.startsWith(RulecodeServiceConstants.BestMatchMethodPrefix)) {
                  dbJsonMap.getOrElse(s"$key.$currentPath", JNothing)
                } else json \ currentPath
              }
              derivedJValue.toOption match {
                case None => throw JsonFieldNotFoundException(s"Json field '${path}' not found in the table lookup data")
                case Some(jValue: JValue) => jValue
              }
            case None => throw new NoSuchElementException(s"No such element '${key}' present in the table response map")
          }
        } else {
          throw LookupNotFoundException(s"Empty DB lookup and parameter '${path}' not resolved")
        }
      }
    } else if (path.startsWith("file.")) {
      val pathsArray = path.split("\\.")
      if (pathsArray.size < 2) {
        throw new IllegalArgumentException(s"Invalid format '${path}'. Expected in this format 'file.<file_name>")
      }
      val key = path.replaceAll("_optional_", "")
      if (path.endsWith("_optional_")) {
        if (dbJsonMap.contains(key)) {
          dbJsonMap.get(key) match {
            case Some(jValue: JValue) => jValue
            case None => JNothing
          }
        } else {
          JNothing
        }
      } else {
        if (dbJsonMap.contains(key)) {
          dbJsonMap.get(key) match {
            case Some(jValue: JValue) =>
              jValue.toOption match {
                case None => throw JsonFieldNotFoundException(s"Json field '${path}' not found in the file lookup data")
                case Some(jValue: JValue) => jValue
              }
            case None => throw new NoSuchElementException(s"No such element '${key}' present in the file response map")
          }
        } else {
          throw LookupNotFoundException(s"Empty file lookup and parameter '${path}' not resolved")
        }
      }
    } else if (path.startsWith("lookup.")) {
      val pathsArray = path.split("\\.")
      if (pathsArray.size < 3) {
        throw new IllegalArgumentException(s"Invalid field name '${path}'. Expected in this format 'lookup.table.<table_name>")
      }
      val key = pathsArray(1) + "." + pathsArray(2)
      if (dbJsonMap.contains(key)) {
        dbJsonMap.get(key) match {
          case Some(jValue: JValue) => jValue
          case None => throw new NoSuchElementException(s"No such element '${key}' present in the table response map")
        }
      } else {
        throw JsonFieldNotFoundException(s"Json field '${path}' not resolved")
      }
    } else if (path.startsWith("___")) {
      val derivedPath = path.replace("___", "")
      JValueDataTypeResolver(derivedPath, From.Defined).getJValue()
    } else if (path.startsWith("combination")) {
      val derivedPath = path.replace("combination.", "")
      val combinationFields = derivedPath.split(",").toList
      if (isCombinationValid(combinationFields, requestJsonAST, dependencies)) {
        createPrimaryKey(combinationFields, requestJsonAST, dependencies)
      } else {
        JNothing
      }
    } else if (dependencies.nonEmpty) {
      if (dependencies.contains(path)) {
        dependencies.get(path) match {
          case Some(jValue: JValue) => jValue
          case None => throw new NoSuchElementException(s"No such element '${path}' present in the dependencies map")
        }
      } else {
        throw JsonFieldNotFoundException(s"Json field '${path}' not resolved")
      }
    }
    else {
      throw new IllegalArgumentException(s"Unsupported path prefix '${path}'")
    }
  }

  def isCombinationValid(combinationElements: List[String], requestJsonAST: JValue,dependencies: Map[String, JValue]): Boolean = {
    var result = true
    for (element <- combinationElements) {
      val inputs = element.split("_____").toList
      if(element.contains("variable")){
        val derivedElement = element.replace("variable.","").split("_____")(0)
        if(dependencies.get(derivedElement) == JNothing || dependencies.get(derivedElement) == Some(JNothing)){
          result = false
        }
      }else {
        val derivedJValue = inputs(0).split("\\.").foldLeft(requestJsonAST) { (jValue, currentPath) =>
          jValue \ currentPath
        }
        val isExist = derivedJValue.toOption match {
          case Some(jValue: JValue) if jValue != None => true
          case _ => false
        }
        result &&= isExist
      }
    }
    result
  }

  def createPrimaryKey(combinations: List[String], requestJsonAST: JValue,dependencies: Map[String, JValue]): JString = {
    var keys = ""
    var values = ""
    for (key <- combinations) {
      val inputs = key.split("_____").toList
      keys += inputs(1) + "_"
      if (key.contains("variable")) {
        val derivedElement = key.replace("variable.", "").split("_____")(0)
        values += dependencies.get(derivedElement).get.extract[String] + "_"
      }else{
        val derivedJValue = inputs(0).split("\\.").foldLeft(requestJsonAST) { (jValue, currentPath) =>
          jValue \ currentPath
        }
        values += derivedJValue.extract[String] + "_"
      }
    }
    JString.apply(keys.dropRight(1)+ "|" + values.dropRight(1) + "___##___")
  }

  /**
   * This is json xpath function implementation, that will return JNothing if path is not found any individual entities.
   *
   * @param currentPath - json path
   * @param jValue - the jValue on which the xpath will be applied
   * @return JValue with values if found else JNothing
   *
   * @example
   *  <pre>
   *     Input:
   *          currentPath - "first_name"
   *          jValue - [
   *                      {"first_name":"stewart"},
   *                      {"last_name":"trust"},
   *                      {"first_name":"rob"}
   *                  ]
   *
   *     Output: [JString("stewart"), JNothing, JString("rob")]
   *
   *     Comments: Input's first and last entity has first_name attribute and the middle one doesn't.
   *               Hence after applying the path for missing first_name for middle entity will return JNothing and actual values for first and last entities.
   *  </pre>
   *
   */
  def getDerivedValueOptionalWithNull(currentPath: String, jValue: JValue) : JValue = {
    val derivedJVal = jValue match {
      case JArray(list) => {
        val jArrDerivedVal = list.foldLeft(List.empty[JValue]) {
          (listJVal, jVal) =>
            val currentJVal = (jVal \ currentPath).toOption match {
              case Some(value: JValue) => value
              case _ => JNothing
            }
            listJVal ++ List(currentJVal)
        }
        JArray(jArrDerivedVal)
      }
      case _ => jValue \ currentPath
    }
    derivedJVal.toOption match {
      case None => JNothing
      case Some(jValue: JValue) => jValue
    }
  }

  def getOptionalJValue(requestJsonAST: JValue, path: String): Option[JValue] = {
    path.split("\\.").foldLeft(requestJsonAST) { (json, currentPath) =>
      json \ currentPath
    }.toOption
  }

  def getOptionalInt(requestJsonAST: JValue, path: String): Option[Int] = {
    path.split("\\.").foldLeft(requestJsonAST) { (json, currentPath) =>
      json \ currentPath
    }.extractOpt[Int]
  }

  def getOptionalLong(requestJsonAST: JValue, path: String): Option[Long] = {
    path.split("\\.").foldLeft(requestJsonAST) { (json, currentPath) =>
      json \ currentPath
    }.extractOpt[Long]
  }

  def getOptionalDouble(requestJsonAST: JValue, path: String): Option[Double] = {
    path.split("\\.").foldLeft(requestJsonAST) { (json, currentPath) =>
      json \ currentPath
    }.extractOpt[Double]
  }

  def getOptionalString(requestJsonAST: JValue, path: String): Option[String] = {
    path.split("\\.").foldLeft(requestJsonAST) { (json, currentPath) =>
      json \ currentPath
    }.extractOpt[String]
  }

  def getOptionalBoolean(requestJsonAST: JValue, path: String): Option[Boolean] = {
    path.split("\\.").foldLeft(requestJsonAST) { (json, currentPath) =>
      json \ currentPath
    }.extractOpt[Boolean]
  }

  def getOptionalArray(requestJsonAST: JValue, path: String): Option[List[JValue]] = {
    val result = path.split("\\.").foldLeft(requestJsonAST) { (jValue, currentPath) =>
      jValue \ currentPath
    }
    result.extractOpt[List[JValue]]
  }

  def getOptionalStringArray(requestJsonAST: JValue, path: String): Option[List[String]] = {
    val result = path.split("\\.").foldLeft(requestJsonAST) { (jValue, currentPath) =>
      jValue \ currentPath
    }
    result.extractOpt[List[String]]
  }

  def getJStringAsString(inputOpt: Option[JValue]): String = inputOpt match {
    case Some(JString(name)) => name
    case Some(_) | None => ""
  }

  def getJStringAsStringOptional(inputOpt: Option[JValue]): Option[String] = inputOpt match {
    case Some(JString(name)) => Some(name)
    case Some(_) | None => None
  }

  def getJIntAsInt(inputOpt: Option[JValue]): Int = inputOpt match {
    case Some(JInt(name)) => name.toInt
    case Some(_) | None => 0
  }

  def getJLongAsLong(inputOpt: Option[JValue]): Long = inputOpt match {
    case Some(JLong(name)) => name
    case Some(_) | None => 0
  }

  def getJDoubleAsDouble(inputOpt: Option[JValue]): Double = inputOpt match {
    case Some(JDouble(name)) => name
    case Some(_) | None => 0
  }

  def getJBoolAsBoolean(inputOpt: Option[JValue]): Boolean = inputOpt match {
    case Some(JBool(name)) => name
    case Some(_) | None => false
  }

  def toOptInt(s: String): Option[Int] = {
    try {
      Some(s.toInt)
    } catch {
      case ex: Exception => None
    }
  }

  def lookup(jsonPath: String, data: JValue, config: JValue): Option[JValue] = {
    if (jsonPath.startsWith("input.")) {
      val key = jsonPath.replace("input.","")
      getOptionalJValue(data, key)
    } else if (jsonPath.startsWith("config.")) {
      val key = jsonPath.replace("config.","")
      getOptionalJValue(config, key)
    } else if (jsonPath.startsWith("random.string.")) {
      val length = toOptInt(jsonPath.replace("random.string.", "")).getOrElse(10)
      Some(JString(Random.alphanumeric.take(length).mkString))
    } else if (jsonPath.startsWith("split.")) {
       resolveSplit(jsonPath,data)
    } else if (jsonPath.startsWith("selectPackage.")) {
       selectPackage(jsonPath,data)
    }else {
      logger.error(s"Unable to resolve json path $jsonPath")
      None
    }
  }

  def resolveJsonTemplate(template: JValue,
                          data: JValue,
                          config: JValue,
                          mandateVariablePrefix: String = "$$",
                          optionalVariablePrefix: String = "??"
                         ): JValue = {
    template.transformField {
      case JField(k, JString(v)) if (v.startsWith(mandateVariablePrefix) || v.startsWith(optionalVariablePrefix)) =>
        val returnAsArray = v.endsWith(AsArraySuffix)
        val jsonPath = (if (v.startsWith(mandateVariablePrefix)) v.replace(mandateVariablePrefix, "")
        else v.replace(optionalVariablePrefix, "")).replaceAll(AsArraySuffix, "")
        val resolvedJValue = lookup(jsonPath.trim, data, config)
        resolvedJValue match {
          case Some(jValue) =>
            if (returnAsArray) JField(k, JArray(List(jValue))) else JField(k, jValue)
          case None =>
            if (v.startsWith(mandateVariablePrefix))
              throw new IllegalArgumentException(s"Unable to resolve json path $v found in the template")
            else JField(k, JNothing)
        }
    }
  }

  def configToJValue(config: Config): JValue = {
    val configStr = config.root().render(ConfigRenderOptions.concise())
    Try(JsonMethods.parse(configStr)) match {
      case Success(jValue) => jValue
      case Failure(error) =>
        logger.error("Error occured in parsing config", error)
        JNothing
    }
  }

  def mergeJValue(base: JValue, derived: JValue): JValue = {
    Try(base.merge(derived)) match {
      case Success(jValue) => jValue
      case Failure(error) =>
        logger.error("Error occured in merging json config", error)
        base
    }
  }

  def getNameScores(detailedPersons: List[InfutorDetailedPerson], inputFirstName: Option[String], inputLastName: Option[String]): List[Int] = {
    detailedPersons.map { person =>
      val firstNameMatch = MatcherUtility.matchN(inputFirstName.getOrElse(""), person.firstName.getOrElse(""))
      val lastNameMatch = MatcherUtility.matchN(inputLastName.getOrElse(""), person.lastName.getOrElse(""))
      if (firstNameMatch && lastNameMatch) {
        FULL_NAME_MATCH_SCORE
      } else if (firstNameMatch || lastNameMatch) {
        SINGLE_NAME_MATCH_SCORE
      } else {
        NO_MATCH_SCORE
      }
    }
  }

  def sortInfutorPersonsByDate(persons: List[InfutorDetailedPerson]) : List[InfutorDetailedPerson] = {
    persons.sortWith((p1, p2) => {
      (p1.lastSeenDate, p2.lastSeenDate) match {
        case (Some(d1), Some(d2)) if (d1.isEmpty && d2.isEmpty) => false
        case (Some(d1), Some(d2)) if (d1.isEmpty && d2.nonEmpty) => false
        case (Some(d1), Some(d2)) if (d1.nonEmpty && d2.isEmpty) => true
        case (Some(d1), Some(d2)) if (d1.nonEmpty && d2.nonEmpty) =>
          val dateOpt1 = DateUtility.getDateTime(d1)
          val dateOpt2 = DateUtility.getDateTime(d2)
          (dateOpt1, dateOpt2) match {
            case (Some(date1), Some(date2)) => date1.getMillis > date2.getMillis
            case _ => d1 > d2
          }
        case _ => false
      }
    })
  }

  def getBestMatchedIndex(requestJsonAST: JValue, bestIdentityOpt: Option[BestIdentity], dbJsonAST: JValue): Option[JValue] = {
    try {
      bestIdentityOpt match {
        case None => None
        case Some(bestIdentity: BestIdentity) =>
          val nodesList = bestIdentity.nodes.map(node => (dbJsonAST \ node).toOption).toList
          nodesList match {
            case n1 :: n2 :: n3 :: Nil =>
              (n1, n2, n3) match {
                case (Some(JArray(arr1)), Some(JArray(arr2)), Some(JArray(arr3))) =>
                  if (arr1.isEmpty || arr2.isEmpty || arr3.isEmpty) {
                    Some(JInt(-1))
                  } else if ((arr1.size == arr2.size) && (arr1.size == arr3.size)) {
                    if (arr1.size == 1) {
                      Some(JInt(0))
                    } else {
                      val inputFirstName = (requestJsonAST \ bestIdentity.inputNames.head.split("\\.").last).extractOpt[String]
                      val inputLastName = (requestJsonAST \ bestIdentity.inputNames.last.split("\\.").last).extractOpt[String]
                      val sortByField = bestIdentity.sortBy.split("\\.").last
                      val lastSeenDates = arr2.map(x => (x \ sortByField).extractOpt[String])
                      val detailedPersons = (arr1.zipWithIndex, lastSeenDates).zipped.map((person, date) =>
                        InfutorDetailedPerson(
                          person._2,
                          (person._1 \ bestIdentity.jsonNames.head.split("\\.").last).extractOpt[String],
                          (person._1 \ bestIdentity.jsonNames.last.split("\\.").last).extractOpt[String],
                          date
                        )
                      )
                      val nameScores = getNameScores(detailedPersons, inputFirstName, inputLastName)
                      val detailedPersonsWithScores = (detailedPersons, nameScores).zipped.map { (person, score) =>
                        InfutorDetailedPerson(person.idx, person.firstName, person.lastName, person.lastSeenDate, score)
                      }
                      val maxScore = nameScores.max
                      if (maxScore <= 0) {
                        //No name match found for all items.
                        //Simply sort the persons by date and pick the recent one.
                        val filteredPersonsWithValidDate = detailedPersonsWithScores.filter(p =>
                          p.lastSeenDate match {
                            case Some(date) => true
                            case _ => false
                          })
                        if (filteredPersonsWithValidDate.isEmpty) {
                          //Sorting is not possible in the case when there is no date available for all the persons.
                          //Fallback to 0th index
                          Some(JInt(detailedPersonsWithScores.head.idx))
                        } else if (filteredPersonsWithValidDate.size == 1) {
                          Some(JInt(filteredPersonsWithValidDate.head.idx))
                        } else {
                          val sortedPersons = sortInfutorPersonsByDate(filteredPersonsWithValidDate)
                          Some(JInt(sortedPersons.head.idx))
                        }
                      } else {
                        //Filter persons with max scores
                        val filteredPersonsWithMaxScores = detailedPersonsWithScores.filter(p => (p.nameScoreMatch == maxScore))
                        if (filteredPersonsWithMaxScores.size == 1) {
                          Some(JInt(filteredPersonsWithMaxScores.head.idx))
                        } else {
                          //More than one person have same max scores.
                          //Sort by date and pick the recent one
                          val filteredPersonsWithValidDate = filteredPersonsWithMaxScores.filter(p =>
                            p.lastSeenDate match {
                              case Some(date) => true
                              case _ => false
                            })
                          if (filteredPersonsWithValidDate.isEmpty) {
                            //Sorting is not possible in the case when there is no date available for all the persons.
                            //Fallback to 0th index
                            Some(JInt(filteredPersonsWithMaxScores.head.idx))
                          } else if (filteredPersonsWithValidDate.size == 1) {
                            Some(JInt(filteredPersonsWithValidDate.head.idx))
                          } else {
                            val sortedPersons = sortInfutorPersonsByDate(filteredPersonsWithValidDate)
                            Some(JInt(sortedPersons.head.idx))
                          }
                        }
                      }
                    }
                  } else {
                    Some(JInt(-1))
                  }
                case _ => Some(JInt(-1))
              }
            case _ => Some(JInt(-1))
          }
      }

    } catch {
      case ex: Exception =>
        logger.error("Exception while find best matched identity", ex)
        Some(JInt(-1))
    }
  }

  def getBestMatchedNode(requestJsonAST: JValue, bestIdentityOpt: Option[BestIdentity], dbJsonAST: JValue): BestMatchedNode = {
    try {
      bestIdentityOpt match {
        case Some(bestIdentity: BestIdentity) =>
          val bestIndex = getBestMatchedIndex(requestJsonAST, bestIdentityOpt, dbJsonAST)
          bestIndex match {
            case Some(JInt(idx)) if (idx >= 0) =>
              val bestNode = bestIdentity.nodes.foldLeft(List.empty[JField]) { (fieldsList, node) =>
                fieldsList.::(JField(node, (dbJsonAST \ node)(idx.toInt)))
              }
              BestMatchedNode(JInt(idx), JObject(bestNode))
            case _ => BestMatchedNode(JNothing, JNothing)
          }
        case None => BestMatchedNode(JNothing, JNothing)
      }
    } catch {
      case ex: Exception =>
        logger.error("Exception while getting best matched node", ex)
        BestMatchedNode(JNothing, JNothing)
    }
  }


  /*
  * This Method will add functionality to split the string
  * and use it's firstPart or secondPart.
  * pass head or tail
  * Example. http_definitions/enstream.json
  * */
  def resolveSplit(jsonPath: String, data: JValue): Option[JValue] = {
    val splitPath = jsonPath.replace("split.", "")
    var inputPath = ""
    var isHead = false;
    if (splitPath.startsWith("head.")) {
      inputPath = splitPath.replace("head.", "")
      isHead = true
    } else {
      inputPath = splitPath.replace("tail.", "")
    }
    val key = inputPath.replace("input.", "")
    val inputValue = getOptionalJValue(data, key)
    if(inputValue.isDefined) {
      val inputResult = inputValue.get match {
        case JString(str) => Some(str)
        case _ => None
      }
      val result = if (inputResult.isDefined) {
        val lastIndex = inputResult.get.lastIndexOf(" ")
        if (isHead) {
          if (lastIndex >= 0) JString(inputResult.get.substring(0, lastIndex)) else JString(inputResult.get)
        } else {
          if (lastIndex >= 0) JString(inputResult.get.substring(lastIndex + 1)) else JNothing
        }
      } else {
        JNothing
      }
      Option(result)
    }else{
      Option(JNothing)
    }
  }

  private def selectPackage(jsonPath: String, data: JValue): Option[JValue] = {
    val key = jsonPath.replace("selectPackage.input.", "")
    getOptionalJValue(data, key) match {
      case Some(JString(value)) =>
        val packages = if (value == "true") {
          List(PHONE_BASIC, IDENTITY_ENRICHMENT, PHONE_SIMSWAP)
        } else {
          List(PHONE_BASIC, IDENTITY_ENRICHMENT)
        }
        Option(JString(packages.mkString(",")))
      case _ => Option(JNothing)
    }
  }

}
