<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>rulecode</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>rulecode-common</artifactId>
    <dependencies>

        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
            <version>1.2.1</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-json4s</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-microservice-client</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>idplus-common-models</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-resource</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.5</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-email-validator</artifactId>
            <version>${socure.common.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.json4s</groupId>
            <artifactId>json4s-native_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rockymadden.stringmetric</groupId>
            <artifactId>stringmetric-core_${sc.ver}</artifactId>
            <version>0.27.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
        </dependency>

        <!-- Scala Test -->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${sc.ver}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalamock</groupId>
            <artifactId>scalamock-scalatest-support_${sc.ver}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <version>${revision}</version>
            <artifactId>pii-standardization-service-common</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
