<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>rulecode</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>rulecode-dynamo-db</artifactId>

    <organization>
        <name>Socure.com Inc.</name>
        <url>http://socure.com</url>
    </organization>
    <name>rulcode-dynamo-db</name>
    <description>DynamoDB based rulecode query service</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <scala.maven.plugin.version>3.2.2</scala.maven.plugin.version>
        <aws.secretsmanager.jdbc>1.0.6</aws.secretsmanager.jdbc>
        <sc.ver>2.11</sc.ver>
        <scala.version>${sc.ver}.12</scala.version>
        <io.netty.version>4.1.77.Final</io.netty.version>
        <ecr.account>************.dkr.ecr.us-east-1.amazonaws.com/socureid</ecr.account>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-scala_2.11</artifactId>
                <version>${jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-socks</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler-proxy</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-epoll</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-unix-common</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver-dns</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-dns</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>2.20.89</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>2.19.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>rulecode-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws.secretsmanager</groupId>
            <artifactId>aws-secretsmanager-jdbc</artifactId>
            <version>${aws.secretsmanager.jdbc}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-socure-constants</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra_2.11</artifactId>
            <version>2.4.0</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-transaction-aware-logger</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-hmac-http</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-config</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-executioncontext-factory</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-executorservice</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jmx-service</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-logs</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-metrics</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-s3</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>dynamodb-enhanced</artifactId>
            <version>2.20.89</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>netty-nio-client</artifactId>
            <version>2.20.89</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sso</artifactId>
            <version>2.20.89</version>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest-matchers-core_2.11</artifactId>
            <version>3.2.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest-shouldmatchers_2.11</artifactId>
            <version>3.2.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.paradoxical</groupId>
            <artifactId>atmos_2.11</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>com.github.pureconfig</groupId>
            <artifactId>pureconfig_2.11</artifactId>
            <version>0.9.2</version>
        </dependency>
        <dependency>
            <groupId>eu.timepit</groupId>
            <artifactId>refined-pureconfig_2.11</artifactId>
            <version>0.9.2</version>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest-funsuite_2.11</artifactId>
            <version>3.2.2</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.scalamock/scalamock-scalatest-support -->
        <dependency>
            <groupId>org.scalamock</groupId>
            <artifactId>scalamock-scalatest-support_2.11</artifactId>
            <version>3.6.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-openapi3-scalatra</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>${scala.maven.plugin.version}</version>
                <configuration>
                    <recompileMode>mixed</recompileMode>
                    <args>
                        <!--                        <maven.compiler.release>8</maven.compiler.release>
                                                <maven.compiler.source>8</maven.compiler.source>
                                                <maven.compiler.target>8</maven.compiler.target>-->
                        <arg>-target:jvm-1.8</arg>
                        <arg>-nobootcp</arg>
                    </args>
                </configuration>
                <executions>
                    <execution>
                        <id>scala-compile-first</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>add-source</goal>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>scala-test-compile</id>
                        <phase>process-test-resources</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
