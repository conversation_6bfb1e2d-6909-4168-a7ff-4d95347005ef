package me.socure.rulecode.dynamo.database

import com.typesafe.config.ConfigFactory
import me.socure.common.transaction.id.TrxId
import org.mockito.Matchers.any
import org.mockito.Mockito._
import org.scalatest.funsuite.AsyncFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatest.mockito.MockitoSugar
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model._

import java.util.concurrent.CompletableFuture
import scala.collection.JavaConverters._
import scala.concurrent.ExecutionContext

class BatchGetServiceTest extends AsyncFunSuite with Matchers with MockitoSugar {

  implicit val ec: ExecutionContext = scala.concurrent.ExecutionContext.global
  implicit val trxId: TrxId = TrxId("test")

  test("should batch requests and return merged response") {
    val mockClient = mock[DynamoDbAsyncClient]

    val tableName = "TestTable"
    val key = Map("id" -> AttributeValue.builder().s("123").build()).asJava
    val keysAndAttrs = KeysAndAttributes.builder().keys(List(key).asJava).build()

    val responseItem = Map(tableName -> List(key).asJava).asJava
    val response = BatchGetItemResponse.builder().responses(responseItem).build()

    val futureResp = CompletableFuture.completedFuture(response)
    when(mockClient.batchGetItem(any[BatchGetItemRequest]))
      .thenReturn(futureResp)

    val config = ConfigFactory.parseString(
      """dynamodb.batchget {
        |  retry.count = 3
        |  batch.size = 100
        |  timeoutInMills = 200
        |}""".stripMargin)

    val service = new BatchGetService(mockClient, config)

    val input = Map(tableName -> keysAndAttrs)

    service.batchGetAllAndMergeResponse(input, "testOp").map { merged =>
      val items = merged.responses().get(tableName)
      items should not be null
      items.size() shouldBe 1
    }
  }

  test("should retry if unprocessed keys are returned") {
    val mockClient = mock[DynamoDbAsyncClient]

    val tableName = "RetryTable"
    val key = Map("id" -> AttributeValue.builder().s("retry-key").build()).asJava
    val kaa = KeysAndAttributes.builder().keys(List(key).asJava).build()

    val firstResponse = BatchGetItemResponse.builder()
      .unprocessedKeys(Map(tableName -> kaa).asJava)
      .build()

    val secondResponse = BatchGetItemResponse.builder()
      .responses(Map(tableName -> List(key).asJava).asJava)
      .build()

    val future1 = CompletableFuture.completedFuture(firstResponse)
    val future2 = CompletableFuture.completedFuture(secondResponse)

    when(mockClient.batchGetItem(any[BatchGetItemRequest]))
      .thenReturn(future1, future2)

    val config = ConfigFactory.parseString(
      """dynamodb.batchget {
        |  retry.count = 3
        |  batch.size = 100
        |  timeoutInMills = 200
        |}""".stripMargin)

    val service = new BatchGetService(mockClient, config)

    val input = Map(tableName -> kaa)

    service.batchGetAllAndMergeResponse(input, "retryOp").map { merged =>
      merged.responses().get(tableName).size() shouldBe 1
    }
  }

  test("should split request into batches based on maxBatchSize") {
    val mockClient = mock[DynamoDbAsyncClient]

    val tableName = "SplitTable"

    // Create 5 fake keys
    val keys = (1 to 5).map { i =>
      Map("id" -> AttributeValue.builder().s(i.toString).build()).asJava
    }.toList

    val kaa = KeysAndAttributes.builder().keys(keys.asJava).build()
    val input = Map(tableName -> kaa)

    // Set up response for all batches — return the same key as response
    val responses = keys.grouped(2).map { batch =>
      val responseItems = Map(tableName -> batch.asJava).asJava
      CompletableFuture.completedFuture(
        BatchGetItemResponse.builder().responses(responseItems).build()
      )
    }.toSeq

    // Setup the mock to return each batch response
    when(mockClient.batchGetItem(any[BatchGetItemRequest]))
      .thenReturn(responses.head, responses(1), responses(2))

    val config = ConfigFactory.parseString(
      """dynamodb.batchget {
        |  retry.count = 1
        |  batch.size = 2
        |  timeoutInMills = 1000
        |}""".stripMargin)

    val service = new BatchGetService(mockClient, config)

    service.batchGetAllAndMergeResponse(input, "splitTestOp").map { merged =>
      // There should be 5 items merged back together
      val items = merged.responses().get(tableName)
      items.size() shouldBe 5

      // The returned keys should match the input
      val returnedKeys = items.asScala.map(_.get("id").s()).sorted
      returnedKeys shouldBe List("1", "2", "3", "4", "5")
    }
  }

  test("should split and retrieve items from multiple tables with same key ids") {
    val mockClient = mock[DynamoDbAsyncClient]

    val table1 = "TableA"
    val table2 = "TableB"

    // Common key structure: id
    val ids = List("1", "2", "3") // 3 keys → will trigger batching with batch size 2

    val table1Keys = ids.map(id => Map("id" -> AttributeValue.builder().s(id).build()).asJava)
    val table2Keys = ids.map(id => Map("id" -> AttributeValue.builder().s(id).build()).asJava)

    val kaa1 = KeysAndAttributes.builder().keys(table1Keys.asJava).build()
    val kaa2 = KeysAndAttributes.builder().keys(table2Keys.asJava).build()

    val input = Map(table1 -> kaa1, table2 -> kaa2)

    // Batch 1: TableA (id 1, 2)
    val tableAResponseBatch1 = List(
      Map("id" -> AttributeValue.builder().s("1").build(), "value" -> AttributeValue.builder().s("A1").build()).asJava,
      Map("id" -> AttributeValue.builder().s("2").build(), "value" -> AttributeValue.builder().s("A2").build()).asJava
    )

    // Batch 2: TableA (id 3)
    val tableAResponseBatch2 = List(
      Map("id" -> AttributeValue.builder().s("3").build(), "value" -> AttributeValue.builder().s("A3").build()).asJava
    )

    // Batch 3: TableB (id 1, 2)
    val tableBResponseBatch1 = List(
      Map("id" -> AttributeValue.builder().s("1").build(), "value" -> AttributeValue.builder().s("B1").build()).asJava,
      Map("id" -> AttributeValue.builder().s("2").build(), "value" -> AttributeValue.builder().s("B2").build()).asJava
    )

    // Batch 4: TableB (id 3)
    val tableBResponseBatch2 = List(
      Map("id" -> AttributeValue.builder().s("3").build(), "value" -> AttributeValue.builder().s("B3").build()).asJava
    )

    val responses = Seq(
      BatchGetItemResponse.builder().responses(Map(table1 -> tableAResponseBatch1.asJava).asJava).build(),
      BatchGetItemResponse.builder().responses(Map(table1 -> tableAResponseBatch2.asJava).asJava).build(),
      BatchGetItemResponse.builder().responses(Map(table2 -> tableBResponseBatch1.asJava).asJava).build(),
      BatchGetItemResponse.builder().responses(Map(table2 -> tableBResponseBatch2.asJava).asJava).build()
    )

    when(mockClient.batchGetItem(any[BatchGetItemRequest]))
      .thenReturn(
        CompletableFuture.completedFuture(responses(0)),
        CompletableFuture.completedFuture(responses(1)),
        CompletableFuture.completedFuture(responses(2)),
        CompletableFuture.completedFuture(responses(3))
      )

    val config = ConfigFactory.parseString(
      """dynamodb.batchget {
        |  retry.count = 1
        |  batch.size = 2
        |  timeoutInMills = 1000
        |}""".stripMargin)

    val service = new BatchGetService(mockClient, config)

    service.batchGetAllAndMergeResponse(input, "multiTableSplitSameKey").map { merged =>
      val mergedResponses = merged.responses()

      val tableAItems = mergedResponses.get(table1).asScala
      val tableBItems = mergedResponses.get(table2).asScala

      tableAItems.map(_.get("value").s()).sorted shouldBe List("A1", "A2", "A3")
      tableBItems.map(_.get("value").s()).sorted shouldBe List("B1", "B2", "B3")

      tableAItems.size shouldBe 3
      tableBItems.size shouldBe 3
    }
  }

}