package me.socure.rulecode.dynamo.database

import com.fasterxml.jackson.core.JsonParseException
import com.typesafe.config.Config
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.rulecode.common.models.RulecodeDynamoDbInfo
import org.json4s.jackson.JsonMethods
import org.json4s.{DefaultFormats, Formats}
import org.slf4j.LoggerFactory
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.{AttributeValue, AttributeValueUpdate, DescribeTableRequest, GetItemRequest, ResourceNotFoundException, ReturnConsumedCapacity, ScanRequest, UpdateItemRequest, UpdateItemResponse}
import scala.collection.JavaConversions._
import java.util.concurrent.atomic.AtomicReference
import scala.collection.JavaConverters
import scala.util.control.NonFatal
import me.socure.common.json4s.JsonEnrichments

/**
 * @param config
 * @param ec
 */
class DynamoDBHandler(config:Config, currentRef:AtomicReference[RulecodeDynamoDbInfo]) /*(implicit  ec:ExecutionContext)*/ {
  import DynamoDBHandler._
  implicit def jsonFormats: Formats = DefaultFormats
  val jsonEnrichment = new JsonEnrichments
  private val dynamoConfig = config.getConfig("dynamodb")
  private val clientRegion = dynamoConfig.getString("region")
  private val configTable = dynamoConfig.getString("configTable")
  private  val config_key = dynamoConfig.getString("currentConfigKey")
  private  val config_ingestion_key = dynamoConfig.getString("ingestionKey")
  private  val config_rollback_key = dynamoConfig.getString("rollbackKey")
  private val currentConfig = new AtomicReference[RulecodeDynamoDbInfo]()

  //TODO:pass it from scheduler?
  private val dynamoSyncClient = DynamoDbClient.builder
    .region(Region.of(clientRegion))
    .credentialsProvider(DefaultCredentialsProvider.create())
    .build

  def refresh(failOnError: Boolean) = {
    try {
      currentConfig.set(getDatabaseInfo)
      currentRef.set(getDatabaseInfo)
    } catch {
      case NonFatal(ex) =>
        logger.error("Error while refreshing dynamo data source", ex)
        if (failOnError) throw ex
    }
  }

  def getDatabaseInfo: RulecodeDynamoDbInfo = {
    try {
      //TODO: get AtomicReference instead of DB fetch

      val keyValueMap = JavaConverters.mapAsJavaMapConverter(Map("pk"->AttributeValue.builder().s(config_key).build())).asJava

      val getItemRequest = GetItemRequest
        .builder()
        .returnConsumedCapacity(ReturnConsumedCapacity.TOTAL)
        .tableName(configTable)
        .key(keyValueMap).build()
      val dynamoDbInfo = dynamoSyncClient.getItem(getItemRequest).item().getOrDefault("data",AttributeValue.fromS("{}")).s()
      if(dynamoDbInfo=="{}") {
        if(currentConfig.get()==null){
          logger.error("Error while fetching current database info and don't find any existing config")
          throw new Exception("Error while fetching current database info and don't find any existing config")
        }
        else currentConfig.get()
      } else JsonMethods.parse(dynamoDbInfo).extract[RulecodeDynamoDbInfo]
    } catch {
      case NonFatal(ex) =>
        logger.error("Error while fetching current database info", ex)
        throw ex
    }
  }

  def getConfigTableInfo: Map[String,RulecodeDynamoDbInfo] = {
    try {
      val getItemRequest = ScanRequest
        .builder()
        .returnConsumedCapacity(ReturnConsumedCapacity.TOTAL)
        .tableName(configTable)
        .build()
      val result = dynamoSyncClient.scan(getItemRequest).items()
      result.toSeq.map(t => {
        t.get("pk").s() -> JsonMethods.parse(t.getOrElse("data", AttributeValue.fromS("{}")).s()).extract[RulecodeDynamoDbInfo]
      }).toMap
    } catch {
      case NonFatal(ex) =>
        logger.error("Error while fetching config", ex)
        Map.empty[String,RulecodeDynamoDbInfo]
    }

  }

  def swapTableConfig(): UpdateItemResponse = {
    val configData = getConfigData(config_key)
    val configIngestionData = getConfigData(config_ingestion_key)
    validateDynamoTables(configData, configIngestionData)
    updateConfig(config_rollback_key, configData)
    updateConfig(config_key, configIngestionData)
  }

  def rollbackConfig(): UpdateItemResponse = {
    val rollBackData = getConfigData(config_rollback_key)
    validateDynamoTable(rollBackData)
    updateConfig(config_key, rollBackData)
  }

  def updateDynamoConfig(config: Map[String, String]) = {
    validateDynamoTables(config.values.toSet)
    try {
      val configData = getConfigData(config_key)
      val dynamoDbInfo = JsonMethods.parse(configData).extract[RulecodeDynamoDbInfo]
      val newDynamoDbInfo = dynamoDbInfo.copy(config = dynamoDbInfo.config.copy(valid_lookup_names = dynamoDbInfo.config.valid_lookup_names ++ config))
      if(newDynamoDbInfo.equals(dynamoDbInfo)) {
        throw new Exception(s"Config already present, No update done.")
      } else {
        updateConfig(config_rollback_key, configData)
        updateConfig(config_key, jsonEnrichment.anyToJsonEncoder(newDynamoDbInfo).encodeJson())
      }
    } catch {
      case NonFatal(ex) =>
        logger.error(s"Error while updating config", ex)
        metrics.increment("dynamo.config.update.error", s"class:${ex.getClass.getSimpleName}")
        throw ex
      case ex: JsonParseException =>
        logger.error(s"Error while updating config", ex)
        metrics.increment("dynamo.table.validate.error", s"class:${ex.getClass.getSimpleName}")
        throw ex
    }
  }

  private def updateConfig(key: String, dynamoDbInfoStr: String) = {
    val keyValueMap = JavaConverters.mapAsJavaMapConverter(Map("pk"->AttributeValue.builder().s(key).build())).asJava
    val valueMap = JavaConverters.mapAsJavaMapConverter(Map("data" -> AttributeValueUpdate.builder().value(AttributeValue.builder().s(dynamoDbInfoStr).build()).build())).asJava
    val updateItemRequest = UpdateItemRequest
      .builder()
      .tableName(configTable)
      .key(keyValueMap)
      .attributeUpdates(valueMap).build()
    dynamoSyncClient.updateItem(updateItemRequest)
  }

  private def getConfigData(key: String): String = {
    val keyValueMap = JavaConverters.mapAsJavaMapConverter(Map("pk" -> AttributeValue.builder().s(key).build())).asJava
    try {
      val getItemRequest = GetItemRequest
        .builder()
        .returnConsumedCapacity(ReturnConsumedCapacity.TOTAL)
        .tableName(configTable)
        .key(keyValueMap).build()
      dynamoSyncClient.getItem(getItemRequest).item().getOrDefault("data", AttributeValue.fromS("{}")).s()
    } catch {
      case NonFatal(ex) =>
        logger.error(s"Error while reading data $key", ex)
        throw ex
    }
  }

  private def validateDynamoTables(tableNames: Set[String]): Unit = {
    tableNames.foreach(tableName => {
      try {
        val tableResp = dynamoSyncClient.describeTable(DescribeTableRequest
          .builder()
          .tableName(tableName)
          .build()
        )
        if(!tableResp.table().tableStatus().toString.equals("ACTIVE")) {
          logger.error(s"${tableResp.table().tableName()} not active")
          throw new Exception(s"${tableResp.table().tableName()} not active")
        }
      } catch {
        case ex: ResourceNotFoundException =>
          logger.error(s"Error while describing table $tableName", ex)
          metrics.increment("dynamo.table.validate.error", s"class:${ex.getClass.getSimpleName}")
          throw ex
        case NonFatal(ex) =>
          logger.error(s"Error while describing table $tableName", ex)
          metrics.increment("dynamo.table.validate.error", s"class:${ex.getClass.getSimpleName}")
          throw ex
      }
    })
  }

  private def validateDynamoTables(dynamoDbInfoStr1: String, dynamoDbInfoStr2: String) = {
    try {
      val dynamoDbInfo1 = JsonMethods.parse(dynamoDbInfoStr1).extract[RulecodeDynamoDbInfo]
      val dynamoDbInfo2 = JsonMethods.parse(dynamoDbInfoStr2).extract[RulecodeDynamoDbInfo]
      if(dynamoDbInfo1.equals(dynamoDbInfo2)) {
        logger.error("Config with same value already exists")
        throw new Exception("Config with same value already exists")
      }
      validateDynamoTable(dynamoDbInfoStr1)
      validateDynamoTable(dynamoDbInfoStr2)
    } catch {
      case ex: JsonParseException =>
        logger.error(s"Error while updating config", ex)
        metrics.increment("dynamo.table.validate.error", s"class:${ex.getClass.getSimpleName}")
        throw ex
    }
  }

  private def validateDynamoTable(dynamoDbInfoStr: String): Unit = {
    val dynamoDbInfo = JsonMethods.parse(dynamoDbInfoStr).extract[RulecodeDynamoDbInfo]
    validateDynamoTables(dynamoDbInfo.config.valid_lookup_names.values.toSet)
  }

}

object DynamoDBHandler {
  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)
}

