package me.socure.rulecode.dynamo.database

import com.google.inject.Inject
import com.typesafe.config.Config
import io.opentracing.Span
import io.opentracing.util.GlobalTracer
import me.socure.common.logger.{TransactionAwareLogger, TransactionAwareLoggerFactory}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId
import software.amazon.awssdk.core.exception.SdkClientException
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model._

import java.net.{ConnectException, SocketTimeoutException}
import java.util.concurrent.{TimeUnit, _}
import scala.collection.JavaConverters._
import scala.compat.java8.FutureConverters
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future, Promise}

class BatchGetService @Inject()(client: DynamoDbAsyncClient,
                                config: Config)(implicit ec: ExecutionContext) {

  private val logger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(classOf[BatchGetService])
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[BatchGetService])
  private val scheduler: ScheduledExecutorService = Executors.newScheduledThreadPool(1)

  private val maxRetries = if (config.hasPath("dynamodb.batchget.retry.count")) config.getInt("dynamodb.batchget.retry.count") else 3
  private val maxBatchSize = if (config.hasPath("dynamodb.batchget.batch.size")) config.getInt("dynamodb.batchget.batch.size") else 90
  private val timeoutInMills = if (config.hasPath("dynamodb.batchget.timeoutInMills")) config.getInt("dynamodb.batchget.timeoutInMills") else 100
  private val timeoutDuration = FiniteDuration(timeoutInMills, TimeUnit.MILLISECONDS)

  private def withTimeout[T](fut: Future[T], duration: FiniteDuration): Future[T] = {
    val promise = Promise[T]()
    val timeoutTask = new Runnable {
      def run(): Unit = promise.tryFailure(new TimeoutException("Request timed out"))
    }
    scheduler.schedule(timeoutTask, duration.toMillis, TimeUnit.MILLISECONDS)
    Future.firstCompletedOf(Seq(fut, promise.future))
  }

  private def delayedFuture[T](delay: FiniteDuration)(value: => Future[T]): Future[T] = {
    val promise = Promise[T]()
    scheduler.schedule(new Runnable {
      def run(): Unit = promise.completeWith(value)
    }, delay.toMillis, TimeUnit.MILLISECONDS)
    promise.future
  }

  private def toScala[T](cf: CompletableFuture[T]): Future[T] = FutureConverters.toScala(cf)

  private def runBatchWithRetries(req: java.util.Map[String, KeysAndAttributes],
                                  operationName: String,
                                  retriesLeft: Int = maxRetries,
                                  accumulatedResponses: List[BatchGetItemResponse] = List.empty
                                 )(implicit trxId: TrxId): Future[List[BatchGetItemResponse]] = {
    val batchRequest = BatchGetItemRequest.builder()
      .requestItems(req)
      .returnConsumedCapacity(ReturnConsumedCapacity.TOTAL)
      .build()

    val responseFuture: Future[BatchGetItemResponse] = withTimeout(toScala(client.batchGetItem(batchRequest)), timeoutDuration)

    responseFuture.flatMap { response =>
      val updatedResponses = response :: accumulatedResponses
      val unprocessed = response.unprocessedKeys()
      if (!unprocessed.isEmpty && retriesLeft > 0) {
        runBatchWithRetries(unprocessed, operationName, retriesLeft - 1, updatedResponses)
      } else {
        if (!unprocessed.isEmpty) {
          metrics.increment("dynamo.batch.get.unprocessed.failed", s"operation:${operationName}")
        }
        Future.successful(updatedResponses)
      }
    }.recoverWith {
      case ex if retriesLeft > 0 && (ex.isInstanceOf[TimeoutException] || ex.isInstanceOf[SdkClientException] &&
        (ex.getCause.isInstanceOf[SocketTimeoutException] || ex.getCause.isInstanceOf[ConnectException])) =>
        val reason = ex match {
          case _: TimeoutException                          => "timeout"
          case e: SdkClientException if e.getCause.isInstanceOf[SocketTimeoutException] => "socket_timeout"
          case e: SdkClientException if e.getCause.isInstanceOf[ConnectException]       => "connection_timeout"
          case _                                            => "retryable"
        }
        logger.warn(s"Retryable error on rulecode dynamo call $operationName due to: $reason: ${ex.getMessage}")
        metrics.increment(s"dynamo.batch.get.$reason", s"operation:$operationName",s"retries_left:${retriesLeft}")
        runBatchWithRetries(req, operationName, retriesLeft - 1, accumulatedResponses)
      case ex =>
        val error = if(ex.getCause!=null) ex.getCause.getClass.getName else ex.getClass.getName
        logger.error(s"Final failure for batch - $operationName: ${error}",ex)
        metrics.increment("dynamo.batch.get.error", s"operation:$operationName", s"error:${error}")
        Future.successful(accumulatedResponses)
    }
  }

  def batchGetAll(input: Map[String, KeysAndAttributes], operationName: String, batchSize: Int = maxBatchSize)(implicit trxId: TrxId): Future[List[BatchGetItemResponse]] = {

    // Flatten keys per table
    val tableToKeyList: List[Map[String, KeysAndAttributes]] = input.toList.flatMap {
      case (tableName, kaa) =>
        val keys = kaa.keys().asScala.toList

        keys.grouped(batchSize).map { batch =>
          val keysAttr = KeysAndAttributes.builder().keys(batch.asJava).build()
          Map(tableName -> keysAttr)
        }
    }

    val combinedBatches: List[Map[String, KeysAndAttributes]] = {
      tableToKeyList
        .foldLeft(List.empty[Map[String, KeysAndAttributes]]) { (acc, curr) =>
          acc match {
            case Nil => List(curr)
            case head :: tail =>
              val totalKeysInHead = head.values.map(_.keys().size()).sum
              val currKeysCount = curr.values.map(_.keys().size()).sum

              if (totalKeysInHead + currKeysCount <= batchSize) {
                val merged = head.keySet.union(curr.keySet).map { table =>
                  val keys1 = head.get(table).map(_.keys().asScala).getOrElse(Seq.empty)
                  val keys2 = curr.get(table).map(_.keys().asScala).getOrElse(Seq.empty)
                  val combinedKeys = (keys1 ++ keys2).asJava
                  table -> KeysAndAttributes.builder().keys(combinedKeys).build()
                }.toMap
                merged :: tail
              } else {
                curr :: acc
              }
          }
        }
    }

    if(combinedBatches.size>1){
      logger.info(s"processing more than one batch ${operationName} ${combinedBatches.size}")
      metrics.increment("dynamo.batchget.multiple.batches",s"operation_name:${operationName}")
    }

    val batchResponseFuture: List[Future[List[BatchGetItemResponse]]] = combinedBatches.map(batch => runBatchWithRetries(batch.asJava, operationName))
    Future.sequence(batchResponseFuture).map(_.flatten)
  }

  def batchGetAllAndMergeResponse(input: Map[String, KeysAndAttributes], operationName: String, batchSize: Int = maxBatchSize)(implicit trxId: TrxId): Future[BatchGetItemResponse] = {

    val futureList: Future[List[BatchGetItemResponse]] = batchGetAll(input, operationName, batchSize)

    futureList.map { responses =>
      // Collect all items grouped by table name (preserve all duplicates)
      val mergedResponses: java.util.Map[String, java.util.List[java.util.Map[String, AttributeValue]]] =
        responses
          .flatMap(_.responses().asScala) // Seq[(String, java.util.List[Map[String, AttributeValue]])]
          .groupBy(_._1)                  // Map[String, Seq[(String, List[Map])]]
          .map {
            case (tableName, itemsLists) =>
              val allItems = itemsLists.flatMap(_._2.asScala)
              tableName -> allItems.asJava
          }.asJava

      // Combine all consumed capacities
      val totalConsumedCapacity = responses.flatMap(_.consumedCapacity().asScala).asJava

      // Use unprocessed keys from the last response (or empty map)
      val remainingUnprocessedKeys = responses.lastOption
        .map(_.unprocessedKeys())
        .getOrElse(new java.util.HashMap[String, KeysAndAttributes]())

      // Build final merged response
      BatchGetItemResponse.builder()
        .responses(mergedResponses)
        .consumedCapacity(totalConsumedCapacity)
        .unprocessedKeys(remainingUnprocessedKeys)
        .build()
    }
  }

  private def getTotalConsumedCapacity(responses: List[BatchGetItemResponse]): Double = {
    responses.flatMap(_.consumedCapacity().asScala)
      .map(_.capacityUnits().doubleValue())
      .sum
  }

  private def hasUnprocessedKeys(responses: List[BatchGetItemResponse]): Boolean = {
    responses.exists(!_.unprocessedKeys().isEmpty)
  }

  def getResponseStats(responses: List[BatchGetItemResponse]): BatchResponseStats = {
    val totalItems = responses.flatMap(_.responses().asScala.values.flatMap(_.asScala)).size
    val totalCapacity = getTotalConsumedCapacity(responses)
    val batchCount = responses.size
    val hasUnprocessed = hasUnprocessedKeys(responses)

    BatchResponseStats(totalItems, totalCapacity, batchCount, hasUnprocessed)
  }
}