package me.socure.rulecode.dynamo.database

import com.google.inject.Inject
import com.typesafe.config.Config
import me.socure.common.logger.{TransactionAwareLogger, TransactionAwareLoggerFactory}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.threads.RichCompletableFuture
import me.socure.common.transaction.id.TrxId
import me.socure.rulecode.common.models.TableDefinition
import me.socure.rulecode.dynamo.model.{CustomErrorMessage, SimpleKeyBatchResult, TableListToParamMap, TableToParamMap}
import me.socure.rulecode.dynamo.util.Gzip
import org.json4s.DefaultFormats
import org.json4s.jackson.{Json, JsonMethods}
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model._

import java.util.Base64
import scala.collection.JavaConversions.collectionAsScalaIterable
import scala.collection.JavaConverters._
import scala.collection.immutable.{Map => scalaMap}
import scala.compat.java8.FutureConverters
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class DynamoQueryClient @Inject()(config: Config,
                                  client: DynamoDbAsyncClient,
                                  batchGetService: BatchGetService)(implicit ec: ExecutionContext) {

  private val dynamodbConfig = config.getConfig("dynamodb")
  private val compressedDataTables = dynamodbConfig.getStringList("compressed_data_tables").toList

  private val kycMap = Map(
    "asl_vendor_lookup_all" -> "ck1",
    "asl_vendor_lookup_all_except_zip" -> "ck2",
    "asl_vendor_lookup_zip" -> "ck3",
    "asl_vendor_lookup_zip_city" -> "ck4",
    "asl_vendor_lookup_zip_state" -> "ck5",
    "attom_vendor_lookup_all" -> "ck1",
    "attom_vendor_lookup_all_except_zip" -> "ck2",
    "attom_vendor_lookup_zip" -> "ck3",
    "attom_vendor_lookup_zip_city" -> "ck4",
    "attom_vendor_lookup_zip_state" -> "ck5"
  )

  def getItem(getItemRequest: GetItemRequest)(implicit trxId: TrxId): Future[Option[String]] = {
    try {
      logger.debug("Initiating GetItem for Simple PK:" + getItemRequest.key().keySet().mkString(","))
      val queryStart = System.currentTimeMillis()
      val emptyPersons = "{\"persons\":[]}"
      val scalaResultFuture = FutureConverters.toScala(client.getItem(getItemRequest))
      scalaResultFuture.flatMap {
        result =>
          val queryTime = (System.currentTimeMillis() - queryStart) + " ms"
          logger.debug(s"Index query time taken for GetItem:$queryTime, cc: ${result.consumedCapacity().capacityUnits()}")
          val socialProfiles = if (result.sdkHttpResponse().isSuccessful) {
            val retrievedValue = result.item().getOrDefault("data", AttributeValue.fromS("")).s()
            val decodedString = Base64.getDecoder.decode(retrievedValue)
            val decompressed = Gzip.decompress(decodedString).getOrElse(emptyPersons)
            decompressed
          } else {
            emptyPersons
          }
          Future.successful(if (socialProfiles.nonEmpty) Some(socialProfiles) else Some(emptyPersons))
      }
    } catch {
      case exception: Throwable =>
        metrics.increment("rulecode.dynamodb.get.call.failure")
        logger.error("getItem for Index failed", exception)
        Future.successful(None)
    }
  }

  val logger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(classOf[DynamoQueryClient])
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[DynamoQueryClient])

  val envSuffix = s"_${config.getString("dynamodb.env")}"

  val simpleKeyConfig = config.getConfig("dynamodb.simpleKeyTbl")
  val simplePartitionKey = simpleKeyConfig.getString("partitionFieldName")
  val dataFieldName = simpleKeyConfig.getString("dataFieldName")
  val numberOfKeysForBatch = simpleKeyConfig.getString("numberOfKeysForBatch")


  def getKycGSIResponse(tableToParamMap: TableToParamMap)(implicit trxId: TrxId): Future[(TableToParamMap, Set[String])] = {
    val kycMapValue = kycMap(tableToParamMap.lookupName)
    val input = tableToParamMap.queryParams(simplePartitionKey)
    val attrValues = Map(
      ":ckval" -> AttributeValue.builder.s(s"$input").build
    ).asJava
    val queryRequest = QueryRequest.builder()
      .tableName(tableToParamMap.tableName)
      .indexName(s"$kycMapValue-index")
      .keyConditionExpression(s"$kycMapValue = :ckval")
      .expressionAttributeValues(attrValues)
      .returnConsumedCapacity(ReturnConsumedCapacity.TOTAL)
      .build()
    try {
      logger.debug(s"Initiating query for KYC table ${tableToParamMap.tableName}, GSI $kycMapValue-index")
      val scalaResultFuture = FutureConverters.toScala(client.query(queryRequest))
      scalaResultFuture.map { res => {
        logger.debug(s"Consumed Capacity for kyc table ${tableToParamMap.tableName}, GSI $kycMapValue-index: ${res.consumedCapacity().capacityUnits()}")
        tableToParamMap -> res.items().map(r => r.get(simplePartitionKey).s()).toSet
      }
      }
    } catch {
      case exception: Throwable =>
        logger.error(s"query for GSI Index $kycMapValue-index failed ", exception)
        Future(tableToParamMap, Set.empty)
    }

  }

  def getBatchGetWithGSIForKYC(tableListToParamMap: TableListToParamMap)(implicit trxId: TrxId): Future[Either[CustomErrorMessage, SimpleKeyBatchResult]] = {
    val gsiResponseFutureSeq = Future.sequence(tableListToParamMap.tableListToParamMap.map(t => getKycGSIResponse(t)))
    gsiResponseFutureSeq.flatMap(gsiResponseSet => {
      val gsiResponse = gsiResponseSet.toMap

      val uniqueIdsTableMap = gsiResponse.foldLeft(Map.empty[String, Set[String]]) { (a, b) =>
        a ++ Map(b._1.tableName -> (a.getOrElse(b._1.tableName, Set.empty[String]) ++ b._2))
      }

      try {
        val reqItemsMap = getReqItems(uniqueIdsTableMap)
        if (reqItemsMap.isEmpty) {
          Future.successful(Right(SimpleKeyBatchResult(Map.empty)))
        } else {
          logger.debug("Initiating batchGetItem for KYC Composite PK:" + reqItemsMap.keys.mkString(","))
          val queryStart = System.currentTimeMillis()

          val scalaResultFuture = batchGetService.batchGetAllAndMergeResponse(reqItemsMap, "getBatchGetWithGSIForKYC")


          scalaResultFuture.flatMap {
            result =>
              val queryTime = (System.currentTimeMillis() - queryStart)
              logger.debug(s"Index query time taken for CompositePK:$queryTime ms")
              if (logger.isDebugEnabled) {
                val stats = batchGetService.getResponseStats(List(result))
                logger.debug(s"Batch Get Stats : ${stats.batchCount} , ${stats.totalItems} , ${stats.hasUnprocessedKeys} , ${stats.totalConsumedCapacity}")
              }

              val responses = result.responses
              val isCompressedTable = responses.keySet().map(tableLookupName => tableLookupName -> compressedDataTables.exists(tableLookupName.contains(_))).toMap

              val tblDataMap = responses.entrySet().flatMap { e =>
                val tableLookupName = e.getKey
                val isCompressed = isCompressedTable.getOrElse(tableLookupName, false)
                if (e.getValue.nonEmpty) {
                  val t = e.getValue.map(r => {
                    val retrivedValueId = r.getOrDefault(simplePartitionKey, AttributeValue.fromS("")).s()
                    val retrievedValueData = r.getOrDefault(dataFieldName, AttributeValue.fromS("")).s()
                    if (isCompressed) {
                      val decodedString = Base64.getDecoder.decode(retrievedValueData)
                      val decompressed = Gzip.decompress(decodedString).getOrElse("{}")
                      (tableLookupName, retrivedValueId) -> List(decompressed)
                    } else {
                      (tableLookupName, retrivedValueId) -> List(retrievedValueData)
                    }
                  })
                  t
                } else {
                  Map.empty
                }
              }.toMap

              val simpleKeyBatchResult: Map[String, List[String]] = gsiResponse.map(g => {
                val data = g._2.foldLeft(List.empty[String]) { (dataList, id) =>
                  val d = tblDataMap.getOrElse((g._1.tableName, id), List.empty[String])
                  dataList ++ d
                }
                s"table.${g._1.lookupName}" -> data
              })
              Future.successful(Right(SimpleKeyBatchResult(simpleKeyBatchResult)))
          }
        }
      } catch {
        case exception: Throwable =>
          metrics.increment("rulecode.dynamodb.kyc.batch.call.failure")
          logger.error(s"getBatchItem for KYC Index failed due to ${exception.getMessage}")
          Future.successful(Left(CustomErrorMessage(s"getBatchItem for Index failed due to ${exception.getMessage}")))
      }

    })
  }


  def getNeustarIpv4Data(tableToParamMap: TableToParamMap, inverseTableToLookupMap: scalaMap[String, String], id: AttributeValue)(implicit trxId: TrxId) = {
    if (id.s().isEmpty || id.s() == null || id.s() == "") {
      val tblDataMap = Map(s"table.${inverseTableToLookupMap.getOrElse(tableToParamMap.tableName, "Unknown")}" -> List.empty[String])
      Future.successful(Right(SimpleKeyBatchResult(tblDataMap)))
    } else {
      val getItemRequest = GetItemRequest
        .builder()
        .returnConsumedCapacity(ReturnConsumedCapacity.TOTAL)
        .tableName(tableToParamMap.tableName.replace("_id_", "_data_"))
        .key(mapAsJavaMapConverter(Map("pk" -> id)).asJava).build()

      val scalaFutureForData = FutureConverters.toScala(client.getItem(getItemRequest))
      scalaFutureForData.flatMap { innerResult =>
        if (innerResult.sdkHttpResponse().isSuccessful) {
          val retrievedValue = innerResult.item().getOrDefault("data", AttributeValue.fromS("")).s()
          //logger.info("Retrieved Neustar Data:"+retrievedValue)
          val decodedString = Base64.getDecoder.decode(retrievedValue)
          val decompressed = Gzip.decompress(decodedString).getOrElse(tableToParamMap.lookupName)
          val tblDataMap = Map(s"table.${inverseTableToLookupMap.getOrElse(tableToParamMap.tableName, "Unknown")}" -> List(decompressed))
          Future.successful(Right(SimpleKeyBatchResult(tblDataMap)))
        } else {
          val tblDataMap = Map(s"table.${inverseTableToLookupMap.getOrElse(tableToParamMap.tableName, "Unknown")}" -> List.empty[String])
          Future.successful(Right(SimpleKeyBatchResult(tblDataMap)))
        }
      }
    }
  }

  def getNeustarIpv4Response(neustarPKLookup: TableListToParamMap, inverseTableToLookupMap: scalaMap[String, String])(implicit trxId: TrxId): Future[Either[CustomErrorMessage, SimpleKeyBatchResult]] = {
    try {
      val tableToParamMap = neustarPKLookup.tableListToParamMap.head
      val pkValueMap = tableToParamMap.queryParams.map { case (k: String, v: String) => (k, AttributeValue.fromS(v)) }
      val getItemRequest = GetItemRequest
        .builder()
        .returnConsumedCapacity(ReturnConsumedCapacity.TOTAL)
        .tableName(tableToParamMap.tableName)
        .key(mapAsJavaMapConverter(pkValueMap).asJava).build()
      val scalaResultFuture = FutureConverters.toScala(client.getItem(getItemRequest))
      val queryStart = System.currentTimeMillis()
      scalaResultFuture.flatMap {
        result =>
          val queryTime = (System.currentTimeMillis() - queryStart)
          logger.debug(s"Index query time taken for neustar SimplPK:$queryTime ms")
          if (result.sdkHttpResponse().isSuccessful) {
            val id = result.item().getOrDefault("data", AttributeValue.fromS(""))
            //logger.debug("Neustar Id retrieved:"+id)
            getNeustarIpv4Data(tableToParamMap, inverseTableToLookupMap, id)
          } else {
            val tblDataMap = Map(s"table.${inverseTableToLookupMap.getOrElse(tableToParamMap.lookupName, "Unknown")}" -> List.empty[String])
            Future.successful(Right(SimpleKeyBatchResult(tblDataMap)))
          }
      }
    } catch {
      //TODO: Check and catch NonFatal only
      case exception: Throwable =>
        logger.error("getBatchItem for Index failed", exception)
        Future.successful(Left(CustomErrorMessage(s"getBatchItem for Index failed due to ${exception.getMessage}")))
    }
  }

  def getNickNameAndFraudTableResponse(nickNameMultiPKLookup: TableListToParamMap, otherSinglePKLookup: TableListToParamMap, inverseTableToLookupMap: scalaMap[String, String])(implicit trxId: TrxId): Future[Either[CustomErrorMessage, SimpleKeyBatchResult]] = {
    try {
      val reqItemsMap = getReqItems(otherSinglePKLookup) ++ {
        nickNameMultiPKLookup.tableListToParamMap.headOption match {
          case Some(tableToParamMap: TableToParamMap) =>
            //Case where, minium 1 lookup is there and the tableName for all nick-name lookup (first/last) would be same.
            getReqItemsV2(nickNameMultiPKLookup, tableToParamMap.tableName)
          case None => Map.empty[String, KeysAndAttributes]
        }
      }

      logger.debug("Initiating batchGetItem for Simple PK:" + reqItemsMap.keys.mkString(","))
      val queryStart = System.currentTimeMillis()

      val scalaResultFuture = batchGetService.batchGetAllAndMergeResponse(reqItemsMap, "getNickNameAndFraudTableResponse")

      scalaResultFuture.flatMap {
        result =>
          val queryTime = (System.currentTimeMillis() - queryStart)
          logger.debug(s"Index query time taken for SimplPK:$queryTime ms")
          if (logger.isDebugEnabled) {
            val stats = batchGetService.getResponseStats(List(result))
            logger.debug(s"Batch Get Stats : ${stats.batchCount} , ${stats.totalItems} , ${stats.hasUnprocessedKeys} , ${stats.totalConsumedCapacity}")
          }

          val responses = result.responses

          val nickNameResult = if (nickNameMultiPKLookup.tableListToParamMap.nonEmpty) {
            val inverseMap = nickNameMultiPKLookup.tableListToParamMap.map(
              t => t.queryParams.head._2 -> t.lookupName
            ).toMap.groupBy(_._2)
            val nickNameTblDataMap = result.responses().entrySet()
              .filter(e => e.getKey.contains("nick_name") & (e.getValue.nonEmpty))
              .map { e => {
                val resValue = e.getValue.filterNot(f => f.isEmpty).map(v => v.getOrDefault(simplePartitionKey, AttributeValue.fromS("")).s() -> v.getOrDefault(dataFieldName, AttributeValue.fromS("{}")).s())
                s"table.${e.getKey}" -> resValue
              }
              }.toMap.headOption.filterNot(f => f._2.isEmpty).getOrElse(("", Set.empty[(String, String)]))._2.toMap //.map(f=> inverseMap.getOrElse(f._1,"")->f._2)

            inverseMap.map {
              i => s"table.${i._1}" -> i._2.map(v => nickNameTblDataMap.getOrElse(v._1, "{}")).toList
            }.filter(d => d._1.nonEmpty)
          } else Map.empty[String, List[String]]

          val isCompressedTable = responses.keySet().map(tableLookupName => tableLookupName -> compressedDataTables.exists(tableLookupName.contains(_))).toMap

          val tblDataMap = responses.entrySet().filter(e => !e.getKey.contains("nick_name"))
            .map { e => {
              if (e.getValue.nonEmpty) {
                val tableLookupName = e.getKey
                val isCompressed = isCompressedTable.get(tableLookupName).getOrElse(false)
                if (isCompressed) {
                  val retrievedValue = e.getValue.get(0).getOrDefault(dataFieldName, AttributeValue.fromS("")).s()
                  val decodedString = Base64.getDecoder.decode(retrievedValue)
                  val decompressed = Gzip.decompress(decodedString).getOrElse("{}")
                  //logger.info(s"Original: $retrievedValue , Equifax decompressed value:$decompressed")
                  s"table.${inverseTableToLookupMap.getOrElse(tableLookupName, "Unknown")}" -> List(decompressed)
                } else s"table.${inverseTableToLookupMap.getOrElse(tableLookupName, "Unknown")}" -> List(e.getValue.get(0).getOrDefault(dataFieldName, AttributeValue.fromS("{}")).s())
              } else {
                val tableLookupName = e.getKey
                s"table.${inverseTableToLookupMap.getOrElse(tableLookupName, "Unknown")}" -> List.empty[String]
              }
            }
            }.toMap ++ nickNameResult
          Future.successful(Right(SimpleKeyBatchResult(tblDataMap)))
      }
    } catch {
      //TODO: Check and catch NonFatal only
      case exception: Throwable =>
        logger.error("getBatchItem for Index failed", exception)
        Future.successful(Left(CustomErrorMessage(s"getBatchItem for Index failed due to ${exception.getMessage}")))
    }
  }

  def getSimpleKeyBatch(tableListToParamMap: TableListToParamMap, lookUpToDynamoTableMap: Map[String, String])(implicit trxId: TrxId): Future[Either[CustomErrorMessage, SimpleKeyBatchResult]] = {
    val inverseTableToLookupMap = lookUpToDynamoTableMap.map(_.swap)
    if (tableListToParamMap.tableListToParamMap.nonEmpty) {
      val neustarPKLookup = TableListToParamMap(tableListToParamMap.tableListToParamMap.filter(_.lookupName.contains("neustar_ipv4_geolocation")), None, None, None, None)
      val nickNameMultiPKLookup = TableListToParamMap(tableListToParamMap.tableListToParamMap.filter(_.lookupName.contains("nick_name")), None, None, None, None)
      val otherSinglePKLookup = TableListToParamMap(tableListToParamMap.tableListToParamMap.filterNot(_.lookupName.contains("nick_name")), None, None, None, None)
      val combinationsMultiPKLookup = TableListToParamMap(tableListToParamMap.tableListToParamMap.filter(e => e.lookupName.contains("sv4") && !e.lookupName.contains("prefill")), None, None, None, None)
      val combinationsMultiPKPrefillLookup = TableListToParamMap(tableListToParamMap.tableListToParamMap.filter(e => e.lookupName.contains("sv4") && e.lookupName.contains("prefill")), None, None, None, None)
      if (neustarPKLookup.tableListToParamMap.nonEmpty) { //Neustar is parallel future to Other fraud table queries, reconfirm this flow
        getNeustarIpv4Response(neustarPKLookup, inverseTableToLookupMap)
      } else if (combinationsMultiPKLookup.tableListToParamMap.nonEmpty || combinationsMultiPKPrefillLookup.tableListToParamMap.nonEmpty) {
        for {
          nonPrefillResult <- getBatchGetItemResponseForCombinations(combinationsMultiPKLookup, "SigmaV4_nonprefill")
          prefillResult <- getBatchGetItemResponseForCombinations(combinationsMultiPKPrefillLookup, "SigmaV4_prefill")
        } yield {
          (nonPrefillResult, prefillResult) match {
            case (Right(nonPrefillData), Right(prefillData)) =>
              val combinedData = nonPrefillData.dataPerTable ++ prefillData.dataPerTable
              Right(SimpleKeyBatchResult(combinedData))
            case (Left(_), Right(prefillData)) => Right(prefillData)
            case (Right(nonPrefillData), Left(_)) => Right(nonPrefillData)
            case (Left(a), Left(b)) => Left(a)
          }
        }
      } else {
        getNickNameAndFraudTableResponse(nickNameMultiPKLookup, otherSinglePKLookup, inverseTableToLookupMap)
      }
    } else {
      logger.debug("getBatchItem for Index Failed due to empty tableListToParamMap")
      Future.successful(Left(CustomErrorMessage(s"getBatchItem for Index Failed due to empty tableListToParamMap")))
    }
  }

  def populateKeysAndAttributes(tableParamMap: TableToParamMap): KeysAndAttributes = {
    //TODO: QueryParam Empty check
    val attrValMap = mapAsJavaMapConverter(tableParamMap.queryParams.map(
      queryParam => (queryParam._1, AttributeValue.builder().s(queryParam._2).build())
    )).asJava
    KeysAndAttributes.builder().keys(attrValMap).build()
  }

  def getReqItems(tblToKVPairs: TableListToParamMap): scalaMap[String, KeysAndAttributes] = {
    tblToKVPairs.tableListToParamMap.map(
      tableParamMap => scalaMap(tableParamMap.tableName ->
        populateKeysAndAttributes(tableParamMap))).reduceLeftOption(_ ++ _).getOrElse(scalaMap.empty[String, KeysAndAttributes])
  }

  def populateKeysAndAttributesV2(tableParamMapSet: Set[Map[String, String]]): KeysAndAttributes = {
    val listOfattrValMap = setAsJavaSetConverter(tableParamMapSet.filter(_.nonEmpty).map(
      es => mapAsJavaMapConverter(es
        .map(kvPair => scalaMap(kvPair._1 ->
          AttributeValue.builder().s(kvPair._2).build())).filter(_.nonEmpty).reduceLeft(_ ++ _)).asJava
    )).asJava

    KeysAndAttributes.builder().keys(listOfattrValMap).build()
  }

  def getReqItemsV2(tblToKVPairs: TableListToParamMap, indexTableName: String): scalaMap[String, KeysAndAttributes] = {
    val tableList = tblToKVPairs.tableListToParamMap
      .groupBy(_.queryParams)
      .keySet
    val keysAndAttributes = populateKeysAndAttributesV2(tableList)
    scalaMap(indexTableName -> keysAndAttributes)
  }

  def getReqItems(tableIdMap: Map[String, Set[String]]): Map[String, KeysAndAttributes] = {
    tableIdMap.map(t => t._1 -> populateKeysAndAttributes(t._2)).filter(_._2.keys().nonEmpty)
  }

  def populateKeysAndAttributes(ids: Set[String]): KeysAndAttributes = {
    val attrValMap = ids.map(id => Map(simplePartitionKey -> AttributeValue.builder().s(id).build()).asJava)
    KeysAndAttributes.builder().keys(attrValMap.asJava).build()
  }

  def querySortKeyTable(queryRequest: QueryRequest, tableDefinition: TableDefinition, inputAsOfDate: Option[Long])(implicit trxId: TrxId) = {
    try {
      val isCompressed = compressedDataTables.contains(tableDefinition.name)
      client.query(queryRequest).asScala.map { queryResponse =>
        val queryResult = {
          if (queryResponse.items().isEmpty || (tableDefinition.timeAdjustedFeature && queryResponse.items().head.get("removeDate") != null && Try(queryResponse.items().head.get("removeDate").s().trim.toLong).isSuccess
            && (inputAsOfDate.isEmpty || inputAsOfDate.get >= queryResponse.items().head.get("removeDate").s().trim.toLong)))
            (None, null)
          else {
            val asOfDate = if (tableDefinition.timeAdjustedFeature) queryResponse.items().head.get("asOfDate").n() else null
            if (isCompressed) {
              (Option(Gzip.decompress(Base64.getDecoder.decode(queryResponse.items().head.get(tableDefinition.jsonField).s())).getOrElse("{}")), asOfDate)
            } else {
              (Option(queryResponse.items().head.getOrDefault(tableDefinition.jsonField, AttributeValue.fromS("{}")).s()), asOfDate)
            }
          }
        }
        Right(queryResult)
      }
    } catch {
      case exception: Throwable =>
        metrics.increment(s"rulecode.dynamodb.${tableDefinition.name}.batch.call.failure")
        logger.error(s"getBatchItem for ${tableDefinition.name} failed due to ${exception.getMessage}")
        Future.successful(Left(CustomErrorMessage(s"getBatchItem for Index failed due to ${exception.getMessage}")))
    }
  }

  def getBatchGetItemResponseForCombinations(combinationsMultiPKLookup: TableListToParamMap, operationName: String)(implicit trxId: TrxId): Future[Either[CustomErrorMessage, SimpleKeyBatchResult]] = {
    try {
      // create a list of combinations
      val reqItemsMap = getReqItems(combinationsMultiPKLookup)
      var list: List[(String, String)] = List()
      for (key <- reqItemsMap.keySet) {
        val keysAndAttributes = reqItemsMap.get(key).get
        val keys = keysAndAttributes.keys()
        val a = createKeyForEachCombination(keys, key)
        list = list ++ a
      }

      val requestItems: Map[String, KeysAndAttributes] = list.groupBy(_._1).map {
        case (tableName, keys) =>
          tableName -> KeysAndAttributes.builder()
            .keys(keys.map { case (_, value) =>
              Map(simplePartitionKey -> AttributeValue.builder().s(value).build()).asJava
            }.asJava)
            .build()
      }

      val queryStart = System.currentTimeMillis()
      val allFutureResponses: Future[List[BatchGetItemResponse]] = batchGetService.batchGetAll(requestItems, operationName, numberOfKeysForBatch.toInt)

      // Extracting items from each BatchGetItemResponse and combining them by table name
      val combinedResponseFut: Future[Map[String, java.util.List[java.util.Map[String, AttributeValue]]]] = allFutureResponses.map { result =>
        if (logger.isDebugEnabled) {
          val stats = batchGetService.getResponseStats(result)
          logger.debug(s"Batch Get Stats : ${stats.batchCount} , ${stats.totalItems} , ${stats.hasUnprocessedKeys} , ${stats.totalConsumedCapacity}")
        }
        result.flatMap(_.responses().asScala).groupBy(_._1).map {
          case (tableName, tableItems) =>
            tableName -> {
              tableItems.flatMap(x => x._2.map(y => updateKeys(y.get(simplePartitionKey).s(), y.get(dataFieldName).s()).asJava)).asJava
            }
        }
      }

      // Create new Total Response BatchGetItemResponse with combined response
      val totalResponse: Future[BatchGetItemResponse] = combinedResponseFut.map { combinedResponse =>
        BatchGetItemResponse.builder()
          .responses(combinedResponse.asJava)
          .build()
      }

      totalResponse.flatMap {
        result =>
          val queryTime = (System.currentTimeMillis() - queryStart)
          logger.info(s"Index query time taken for CombineRuleCode:$queryTime ms")

          val responses = result.responses

          val tblDataMap = responses.entrySet()
            .map { e => {
              val lookupName = combinationsMultiPKLookup.tableListToParamMap.head.lookupName
              if (e.getValue.nonEmpty) {
                val tableLookupName = e.getKey
                val isCompressed = false // isCompressedTable.get(tableLookupName).getOrElse(false)
                if (isCompressed) {
                  // TODO: Implement the logic for compressed data
                  val retrievedValue = e.getValue.get(0).getOrDefault(dataFieldName, AttributeValue.fromS("")).s()
                  metrics.time("decompress.time", tags = s"tableLookup:$tableLookupName") {
                    val decodedString = Base64.getDecoder.decode(retrievedValue)
                    val decompressed = Gzip.decompress(decodedString).getOrElse("{}")
                    s"table.${lookupName}" -> List(decompressed)
                  }
                } else s"table.${lookupName}" -> List(e.getValue.map(data => data.getOrDefault(dataFieldName, AttributeValue.fromS("{}")).s()).toList.filter(_ != "{}").mkString.replace("}{", ","))
              } else {
                val tableLookupName = e.getKey
                s"table.${lookupName}" -> List("{}")
              }

            }
            }.toMap
          Future.successful(Right(SimpleKeyBatchResult(tblDataMap)))
      }
    } catch {
      //TODO: Check and catch NonFatal only
      case exception: Throwable =>
        logger.error("getBatchItem for Index failed", exception)
        Future.successful(Left(CustomErrorMessage(s"getBatchGetItemResponseForCombinations for Index failed due to ${exception.getMessage}")))
    }
  }

  def updateKeys(pk: String, data: String): Map[String, AttributeValue] = {
    implicit val formats = org.json4s.DefaultFormats
    val dataMap = JsonMethods.parse(data).extract[Map[String, Any]]
    val modifiedDataMap = dataMap.map { case (key, value) =>
      (pk.split("\\|")(0) + "-" + key, value)
    }
    val modifiedData = Json(DefaultFormats).write(modifiedDataMap)
    Map(simplePartitionKey -> AttributeValue.fromS(pk), dataFieldName -> AttributeValue.fromS(modifiedData))
  }

  def createKeyForEachCombination(listOfKeys: java.util.List[java.util.Map[String, AttributeValue]], tableName: String): List[(String, String)] = {
    var resultList: List[(String, String)] = List()
    for (keyMap <- listOfKeys) {
      for (key <- keyMap.keySet()) {
        val attributeValues = keyMap.get(key).s().split("___##___,")
        for (attribute <- attributeValues) {
          resultList = resultList :+ (tableName -> attribute.replace("___##___", ""))
        }

      }
    }
    resultList
  }
}