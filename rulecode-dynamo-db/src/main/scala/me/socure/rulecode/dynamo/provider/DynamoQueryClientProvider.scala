package me.socure.rulecode.dynamo.provider

import com.typesafe.config.Config
import me.socure.rulecode.dynamo.database.{BatchGetService, DynamoQueryClient}
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient

import javax.inject.{Inject, Provider}
import scala.concurrent.ExecutionContext


class DynamoQueryClientProvider @Inject()(config:Config,dynamoDbAsyncClient: DynamoDbAsyncClient,batchGetService: BatchGetService)(implicit ec:ExecutionContext) extends Provider[DynamoQueryClient] {
  override def get(): DynamoQueryClient = {
    new DynamoQueryClient(config,dynamoDbAsyncClient,batchGetService)
  }
}
