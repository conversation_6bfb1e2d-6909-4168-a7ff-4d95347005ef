package me.socure.rulecode.dynamo.provider

import com.typesafe.config.Config
import software.amazon.awssdk.auth.credentials.{AwsBasicCredentials, DefaultCredentialsProvider, StaticCredentialsProvider}
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient

import java.net.URI
import java.time.Duration
import javax.inject.{Inject, Provider}


/**
 * Provides dynamo async client with default configuration.
 * To tune any configuration like retries,timeouts etc ,
 * please make use of overrideConfiguration method of DynamoDbAsyncClient builder
 * @param config
 */
class DynamoDBAsyncClientProvider @Inject()(config:Config) extends Provider[DynamoDbAsyncClient] {
  override def get(): DynamoDbAsyncClient = {
    val dynamoConfig = config.getConfig("dynamodb")
    val asyncClientRegion = dynamoConfig.getString("region")
    val maxConcurrency = if(dynamoConfig.hasPath("maxconcurrency")) dynamoConfig.getInt("maxconcurrency") else 100

    val localDynamo = dynamoConfig.getBoolean("localConf.isLocal")

    if (localDynamo) {
      val localRegion = Region.of(dynamoConfig.getString("localConf.region"))
      val accessKey = dynamoConfig.getString("localConf.access.key")
      val secretKey = dynamoConfig.getString("localConf.secret.key")
      DynamoDbAsyncClient.builder
        .endpointOverride(URI.create("http://localhost:8000"))
        .region(localRegion) // The region is meaningless for local DynamoDb but required for client builder validation
        .credentialsProvider(StaticCredentialsProvider
          .create(AwsBasicCredentials.create(accessKey, secretKey))).build
    } else {
      DynamoDbAsyncClient.builder
        .region(Region.of(asyncClientRegion))
        .httpClientBuilder(NettyNioAsyncHttpClient.builder()
          .tcpKeepAlive(true)
          .useIdleConnectionReaper(false)
          .maxConcurrency(maxConcurrency))
         .credentialsProvider(DefaultCredentialsProvider.create())
        .build
    }
  }
}