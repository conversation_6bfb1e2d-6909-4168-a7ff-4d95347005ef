customerUserId,submissionDate,ACCOUNT_ID,firstName,surName,email,mobileNumber,physicalAddress,physicalAddress2,city,state,zip,country,ipAddress,nationalId,dob,MOBILE_NUMBER_ONLY_DIGITS,PHONE_COUNTRY_CODE,RAND_KEY,<PERSON><PERSON>IENT_NAME,RANK_ID,fullName,response,response_time_ms,result,status_code,message,profile,trans_id,request_id,profile.0.data.fullname,profile.0.data.persondetails,profile.0.data.phone_details.breach_details.count,profile.0.data.phone_details.breach_details.data,profile.0.data.phone_details.breach_details.data_lists,profile.0.data.phone_details.breach_details.first_breach,profile.0.data.phone_details.breach_details.last_breach,profile.0.data.phone_details.breach_details.list,profile.0.data.phone_details.country_code,profile.0.data.phone_details.current_carrier,profile.0.data.phone_details.disposable,profile.0.data.phone_details.disposable_provider,profile.0.data.phone_details.first_seen,profile.0.data.phone_details.original_carrier,profile.0.data.phone_details.portability_details.first,profile.0.data.phone_details.portability_details.last,profile.0.data.phone_details.portability_details.status,profile.0.data.phone_details.portability_details.times,profile.0.data.phone_details.status,profile.0.data.phone_details.suspicious_format,profile.0.data.phone_details.type1,profile.0.data.phone_details.type2,profile.0.data.phone_details.valid,profile.0.data.phone_details.valid_format,profile.0.data.phone_number,profile.0.data.risk_score,profile.0.data.signals,profile.0.result,profile.0.source,profile.0.status_code,profile.0.data.persondetails.0.city,profile.0.data.persondetails.0.dob,profile.0.data.persondetails.0.gender,profile.0.data.persondetails.0.id_num,profile.0.data.persondetails.0.name,profile.0.data.persondetails.0.state,profile.0.data.persondetails.0.type,profile.0.data.persondetails.1.address1,profile.0.data.persondetails.1.address2,profile.0.data.persondetails.1.address3,profile.0.data.persondetails.1.city,profile.0.data.persondetails.1.dob,profile.0.data.persondetails.1.gender,profile.0.data.persondetails.1.id_num,profile.0.data.persondetails.1.name,profile.0.data.persondetails.1.opening_at,profile.0.data.persondetails.1.state,profile.0.data.persondetails.1.type,profile.0.data.persondetails.1.zip,profile.0.data.persondetails.2.address1,profile.0.data.persondetails.2.address2,profile.0.data.persondetails.2.address3,profile.0.data.persondetails.2.city,profile.0.data.persondetails.2.dob,profile.0.data.persondetails.2.gender,profile.0.data.persondetails.2.id_num,profile.0.data.persondetails.2.name,profile.0.data.persondetails.2.opening_at,profile.0.data.persondetails.2.state,profile.0.data.persondetails.2.type,profile.0.data.persondetails.2.zip,fullAddress,transactionDate,IMIVL.100001,IMIVL.100002,IMIVL.100003,IMIVL.100004,IMIVL.100005,IMIVL.100006,IMIVL.100007,IMIVL.100008,IMIVL.100009,IMIVL.100010,IMIVL.100011,IMIVL.100042,IMIVL.100012,IMIVL.100013,IMIVL.100014,IMIVL.100015,IMIVL.100016,IMIVL.100017,IMIVL.100018,IMIVL.100019,IMIVL.100020,IMIVL.100021,IMIVL.100022,IMIVL.100023,IMIVL.100024,IMIVL.100025,IMIVL.100026,IMIVL.100027,IMIVL.100028,IMIVL.100029,IMIVL.100030,IMIVL.100031,IMIVL.100032,IMIVL.100033,IMIVL.100034,IMIVL.100035,IMIVL.100036,IMIVL.100037,IMIVL.100038,IMIVL.100039,IMIVL.100040,IMIVL.900001,IMIVL.900002,IMIVL.900003,IMIVL.900004,IMIVL.900005,IMIVL.900006,IMIVL.900007,IMIVL.900008,IMIVL.900009,IMIVL.900010,IMIVL.900011,IMIVL.900012,IMIVL.900013,IMIVL.900014,IMIVL.900015,IMIVL.900017,IMIVL.900018,IMIVL.900019,IMIVL.200016,IMIVL.200017,IMIVL.200018,IMIVL.200019,IMIVL.200020,IMIVL.200021,IMIVL.200022,IMIVL.200023,IMIVL.200024,IMIVL.200025,IMIVL.200026,IMIVL.200001,IMIVL.200002,IMIVL.200004,IMIVL.200005,IMIVL.200006,IMIVL.200007,IMIVL.200008,IMIVL.200009,IMIVL.200010,IMIVL.200015,IMIVL.100041
d2436b3c-4844-4326-82dc-e5a8a7d99d1a,2025-03-21 15:45:16,2499,Raiany,Silva,<EMAIL>,+5584999211323,Av. Luiz Lopes Varela 1142,,Ceara-Mirim,,,BR,213.78.167.92,,1992-06-06,5584999211323,+55,0.9979546828892182,TradeStation Group,1,Raiany Silva,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""RAIANY CRISTINA BARBOSA DA SILVA"", ""dob"": ""1992-06-07"", ""gender"": ""F"", ""city"": null, ""state"": null, ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555584999211323"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""824259bf-cee8-4d81-9cdf-f383378630a5"", ""request_id"": ""d2436b3c-4844-4326-82dc-e5a8a7d99d1a""}",3788.71,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""RAIANY CRISTINA BARBOSA DA SILVA"", ""dob"": ""1992-06-07"", ""gender"": ""F"", ""city"": null, ""state"": null, ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555584999211323"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",824259bf-cee8-4d81-9cdf-f383378630a5,d2436b3c-4844-4326-82dc-e5a8a7d99d1a,,"[{""id_num"": ""**********"", ""name"": ""RAIANY CRISTINA BARBOSA DA SILVA"", ""dob"": ""1992-06-07"", ""gender"": ""F"", ""city"": null, ""state"": null, ""type"": ""person""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555584999211323,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,,1992-06-07,F,**********,RAIANY CRISTINA BARBOSA DA SILVA,,person,,,,,,,,,,,,,,,,,,,,,,,,,Av. Luiz Lopes Varela 1142  Ceara-Mirim,2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[""RAIANY CRISTINA BARBOSA DA SILVA""],[""RAIANY CRISTINA BARBOSA DA SILVA""],[],[""RAIANY""],[""CRISTINA BARBOSA""],[""DA SILVA""],,,,[""1992-06-07""],[],[],[],[""**********""],[""F""],[],[],[""**********""],0.0,0.55,1.0,1.0,1.0,0.77,1.0,0.77,0.95,2.0,2.0,1,0,0,1,1,0,0,0,0,0,
bd7d7534-07ab-4d9a-8ab3-0ce093191663,2025-03-24 15:18:24,2499,Eduardo,Nascimento,<EMAIL>,+5567992798840,"TRAVESSA FELIPE DUQUE, 95",Bloco E apartamento 14,Campo Grande,,,BR,*************,,1995-07-17,5567992798840,+55,0.9970381809103811,TradeStation Group,2,Eduardo Nascimento,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""35616970115"", ""name"": ""BRIGETE MADALENA GUBERT NASCIMENTO"", ""dob"": ""1965-07-02"", ""gender"": ""F"", ""city"": ""CAMPO GRANDE"", ""state"": ""MS"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555567992798840"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""9ecbceba-a36a-41bf-95d6-74c5c035d70e"", ""request_id"": ""bd7d7534-07ab-4d9a-8ab3-0ce093191663""}",3045.16,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""35616970115"", ""name"": ""BRIGETE MADALENA GUBERT NASCIMENTO"", ""dob"": ""1965-07-02"", ""gender"": ""F"", ""city"": ""CAMPO GRANDE"", ""state"": ""MS"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555567992798840"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",9ecbceba-a36a-41bf-95d6-74c5c035d70e,bd7d7534-07ab-4d9a-8ab3-0ce093191663,,"[{""id_num"": ""35616970115"", ""name"": ""BRIGETE MADALENA GUBERT NASCIMENTO"", ""dob"": ""1965-07-02"", ""gender"": ""F"", ""city"": ""CAMPO GRANDE"", ""state"": ""MS"", ""type"": ""person""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555567992798840,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,CAMPO GRANDE,1965-07-02,F,35616970115,BRIGETE MADALENA GUBERT NASCIMENTO,MS,person,,,,,,,,,,,,,,,,,,,,,,,,,"TRAVESSA FELIPE DUQUE, 95 Bloco E apartamento 14 Campo Grande",2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[""BRIGETE MADALENA GUBERT NASCIMENTO""],[""BRIGETE MADALENA GUBERT NASCIMENTO""],[],[""BRIGETE""],[""MADALENA GUBERT""],[""NASCIMENTO""],,,,[""1965-07-02""],[""CAMPO GRANDE""],[],[""MS""],[""35616970115""],[""F""],[],[],[""35616970115""],0.0,0.58,0.0,0.14,1.0,1.0,0.14,1.0,0.84,,2.0,1,0,0,0,1,0,0,1,0,0,
95871317-3432-42a7-ac99-e796155e68dc,2025-04-07 02:53:17,2499,Renata,Gomes Gama Brito,<EMAIL>,+5565996790058,avenida dauri riva 55 centro,,colider,,,BR,**************,,1994-01-21,5565996790058,+55,0.9816315362263769,TradeStation Group,3,Renata Gomes Gama Brito,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""DENAPOLY DA SILVA BRITO"", ""dob"": ""1992-07-22"", ""gender"": null, ""city"": ""COLIDER"", ""state"": ""MT"", ""type"": ""person""}, {""id_num"": ""65137760153"", ""name"": ""ILMA LUCIA PINTO DUARTE"", ""dob"": ""1975-08-06"", ""gender"": ""F"", ""city"": ""VARZEA GRANDE"", ""state"": ""MT"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555565996790058"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""1cba464a-554d-4d59-b743-78d29ad9face"", ""request_id"": ""95871317-3432-42a7-ac99-e796155e68dc""}",2639.12,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""DENAPOLY DA SILVA BRITO"", ""dob"": ""1992-07-22"", ""gender"": null, ""city"": ""COLIDER"", ""state"": ""MT"", ""type"": ""person""}, {""id_num"": ""65137760153"", ""name"": ""ILMA LUCIA PINTO DUARTE"", ""dob"": ""1975-08-06"", ""gender"": ""F"", ""city"": ""VARZEA GRANDE"", ""state"": ""MT"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555565996790058"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",1cba464a-554d-4d59-b743-78d29ad9face,95871317-3432-42a7-ac99-e796155e68dc,,"[{""id_num"": ""**********"", ""name"": ""DENAPOLY DA SILVA BRITO"", ""dob"": ""1992-07-22"", ""gender"": null, ""city"": ""COLIDER"", ""state"": ""MT"", ""type"": ""person""}, {""id_num"": ""65137760153"", ""name"": ""ILMA LUCIA PINTO DUARTE"", ""dob"": ""1975-08-06"", ""gender"": ""F"", ""city"": ""VARZEA GRANDE"", ""state"": ""MT"", ""type"": ""person""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555565996790058,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,COLIDER,1992-07-22,,**********,DENAPOLY DA SILVA BRITO,MT,person,,,,VARZEA GRANDE,1975-08-06,F,65137760153,ILMA LUCIA PINTO DUARTE,,MT,person,,,,,,,,,,,,,,avenida dauri riva 55 centro  colider,2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[""DENAPOLY DA SILVA BRITO"", ""ILMA LUCIA PINTO DUARTE""]","[""DENAPOLY DA SILVA BRITO"", ""ILMA LUCIA PINTO DUARTE""]",[],"[""DENAPOLY"", ""ILMA""]","["""", ""LUCIA PINTO""]","[""DA SILVA BRITO"", ""DUARTE""]",,,,"[""1992-07-22"", ""1975-08-06""]","[""COLIDER"", ""VARZEA GRANDE""]",[],"[""MT"", ""MT""]","[""**********"", ""65137760153""]",[""F""],[],[],"[""**********"", ""65137760153""]",0.0,0.57,0.0,0.43,0.0,0.53,0.43,0.53,0.03,,,0,0,0,0,0,0,0,1,0,0,
453237b8-df15-415a-8f7b-480730db5114,2025-03-17 13:19:51,2499,JEFERSON,DEREVIANI,<EMAIL>,+5548996624723,Casas,,Imbituva,,,BR,170.244.63.212,,1989-05-06,5548996624723,+55,0.9808537678879169,TradeStation Group,4,JEFERSON DEREVIANI,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""JEFERSON LUIS DEREVIANI"", ""dob"": ""1989-05-06"", ""gender"": ""M"", ""city"": ""CRICIUMA"", ""state"": ""SC"", ""type"": ""person""}, {""id_num"": ""44440*********"", ""name"": ""JEFERSON LUIS DEREVIANI 0**********"", ""opening_at"": ""2021-12-01"", ""address1"": ""RUA IMIGRANTE THOME"", ""address2"": ""167"", ""address3"": ""PINHEIRINHO"", ""city"": ""CRICIUMA"", ""state"": ""SC"", ""zip"": ""88805050"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555548996624723"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""4b07cef5-f39d-49a1-ad13-19d0f2c6978a"", ""request_id"": ""453237b8-df15-415a-8f7b-480730db5114""}",2947.75,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""JEFERSON LUIS DEREVIANI"", ""dob"": ""1989-05-06"", ""gender"": ""M"", ""city"": ""CRICIUMA"", ""state"": ""SC"", ""type"": ""person""}, {""id_num"": ""44440*********"", ""name"": ""JEFERSON LUIS DEREVIANI 0**********"", ""opening_at"": ""2021-12-01"", ""address1"": ""RUA IMIGRANTE THOME"", ""address2"": ""167"", ""address3"": ""PINHEIRINHO"", ""city"": ""CRICIUMA"", ""state"": ""SC"", ""zip"": ""88805050"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555548996624723"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",4b07cef5-f39d-49a1-ad13-19d0f2c6978a,453237b8-df15-415a-8f7b-480730db5114,,"[{""id_num"": ""**********"", ""name"": ""JEFERSON LUIS DEREVIANI"", ""dob"": ""1989-05-06"", ""gender"": ""M"", ""city"": ""CRICIUMA"", ""state"": ""SC"", ""type"": ""person""}, {""id_num"": ""44440*********"", ""name"": ""JEFERSON LUIS DEREVIANI 0**********"", ""opening_at"": ""2021-12-01"", ""address1"": ""RUA IMIGRANTE THOME"", ""address2"": ""167"", ""address3"": ""PINHEIRINHO"", ""city"": ""CRICIUMA"", ""state"": ""SC"", ""zip"": ""88805050"", ""type"": ""business""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555548996624723,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,CRICIUMA,1989-05-06,M,**********,JEFERSON LUIS DEREVIANI,SC,person,RUA IMIGRANTE THOME,167,PINHEIRINHO,CRICIUMA,,,44440*********,JEFERSON LUIS DEREVIANI 0**********,2021-12-01,SC,business,88805050,,,,,,,,,,,,,Casas  Imbituva,2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[""JEFERSON LUIS DEREVIANI"", ""JEFERSON LUIS DEREVIANI 0**********""]",[""JEFERSON LUIS DEREVIANI""],[""JEFERSON LUIS DEREVIANI 0**********""],"[""JEFERSON"", ""JEFERSON""]","[""LUIS"", ""LUIS DEREVIANI""]","[""DEREVIANI"", ""0**********""]",,,,[""1989-05-06""],"[""CRICIUMA"", ""CRICIUMA""]",[""88805050""],"[""SC"", ""SC""]","[""**********"", ""44440*********""]",[""M""],[""RUA IMIGRANTE THOME""],[""167""],[""**********""],0.0,0.88,1.0,1.0,1.0,1.0,1.0,1.0,0.95,2.0,2.0,1,1,0,1,1,0,0,0,0,0,36
0edb729e-a39b-4067-a1b4-5659dac708aa,2025-03-12 21:43:24,2499,Sergio,Vitorio Ramos,<EMAIL>,+5527999799081,Rod. Barra De Sao Francisco - Ecoporanga,Vila Joao Gomes,Barra de Sao Francisco,,,BR,177.74.234.220,,2001-09-26,5527999799081,+55,0.9796561113305959,TradeStation Group,5,Sergio Vitorio Ramos,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""***********"", ""name"": ""SERGIO VITORIO RAMOS"", ""dob"": ""2001-09-26"", ""gender"": ""M"", ""city"": ""BARRA DE SAO FRANCISCO"", ""state"": ""ES"", ""type"": ""person""}, {""id_num"": ""46922903000120"", ""name"": ""SERGIO VITORIO RAMOS ***********"", ""opening_at"": ""2022-06-27"", ""address1"": ""1TVR BARRA DE SAO FRANCISCO ECOPORANGA KM14"", ""address2"": ""S N"", ""address3"": ""VILA PAULISTA"", ""city"": ""BARRA DE SAO FRANCISCO"", ""state"": ""ES"", ""zip"": ""29800000"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555527999799081"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""066532f5-3383-4543-a73f-37fc607aa49c"", ""request_id"": ""0edb729e-a39b-4067-a1b4-5659dac708aa""}",2586.1,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""***********"", ""name"": ""SERGIO VITORIO RAMOS"", ""dob"": ""2001-09-26"", ""gender"": ""M"", ""city"": ""BARRA DE SAO FRANCISCO"", ""state"": ""ES"", ""type"": ""person""}, {""id_num"": ""46922903000120"", ""name"": ""SERGIO VITORIO RAMOS ***********"", ""opening_at"": ""2022-06-27"", ""address1"": ""1TVR BARRA DE SAO FRANCISCO ECOPORANGA KM14"", ""address2"": ""S N"", ""address3"": ""VILA PAULISTA"", ""city"": ""BARRA DE SAO FRANCISCO"", ""state"": ""ES"", ""zip"": ""29800000"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555527999799081"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",066532f5-3383-4543-a73f-37fc607aa49c,0edb729e-a39b-4067-a1b4-5659dac708aa,,"[{""id_num"": ""***********"", ""name"": ""SERGIO VITORIO RAMOS"", ""dob"": ""2001-09-26"", ""gender"": ""M"", ""city"": ""BARRA DE SAO FRANCISCO"", ""state"": ""ES"", ""type"": ""person""}, {""id_num"": ""46922903000120"", ""name"": ""SERGIO VITORIO RAMOS ***********"", ""opening_at"": ""2022-06-27"", ""address1"": ""1TVR BARRA DE SAO FRANCISCO ECOPORANGA KM14"", ""address2"": ""S N"", ""address3"": ""VILA PAULISTA"", ""city"": ""BARRA DE SAO FRANCISCO"", ""state"": ""ES"", ""zip"": ""29800000"", ""type"": ""business""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555527999799081,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,BARRA DE SAO FRANCISCO,2001-09-26,M,***********,SERGIO VITORIO RAMOS,ES,person,1TVR BARRA DE SAO FRANCISCO ECOPORANGA KM14,S N,VILA PAULISTA,BARRA DE SAO FRANCISCO,,,46922903000120,SERGIO VITORIO RAMOS ***********,2022-06-27,ES,business,29800000,,,,,,,,,,,,,Rod. Barra De Sao Francisco - Ecoporanga Vila Joao Gomes Barra de Sao Francisco,2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[""SERGIO VITORIO RAMOS"", ""SERGIO VITORIO RAMOS ***********""]",[""SERGIO VITORIO RAMOS""],[""SERGIO VITORIO RAMOS ***********""],"[""SERGIO"", ""SERGIO""]","[""VITORIO"", ""VITORIO RAMOS""]","[""RAMOS"", ""***********""]",,,,[""2001-09-26""],"[""BARRA DE SAO FRANCISCO"", ""BARRA DE SAO FRANCISCO""]",[""29800000""],"[""ES"", ""ES""]","[""***********"", ""46922903000120""]",[""M""],[""1TVR BARRA DE SAO FRANCISCO ECOPORANGA KM14""],[""S N""],[""***********""],1.0,1.0,1.0,1.0,1.0,0.56,1.0,0.56,0.98,2.0,2.0,1,1,0,1,1,0,0,1,0,0,24
590beab1-4601-4291-bf7e-c1eae199b696,2025-04-19 23:33:40,2499,Charlles,Schepper,<EMAIL>,+5522992346547,Tulio de alencar,,Rio das ostras,,,BR,138.255.144.95,,2004-03-05,5522992346547,+55,0.9792803933980458,TradeStation Group,6,Charlles Schepper,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""MARCELLO AMARUZZA QUEIROZ"", ""dob"": ""1971-07-13"", ""gender"": ""M"", ""city"": ""CAMPOS DOS GOYTACAZES"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555522992346547"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""2d8641a7-67b0-497c-95eb-3595e8203b4f"", ""request_id"": ""590beab1-4601-4291-bf7e-c1eae199b696""}",2909.26,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""MARCELLO AMARUZZA QUEIROZ"", ""dob"": ""1971-07-13"", ""gender"": ""M"", ""city"": ""CAMPOS DOS GOYTACAZES"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555522992346547"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",2d8641a7-67b0-497c-95eb-3595e8203b4f,590beab1-4601-4291-bf7e-c1eae199b696,,"[{""id_num"": ""**********"", ""name"": ""MARCELLO AMARUZZA QUEIROZ"", ""dob"": ""1971-07-13"", ""gender"": ""M"", ""city"": ""CAMPOS DOS GOYTACAZES"", ""state"": ""RJ"", ""type"": ""person""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555522992346547,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,CAMPOS DOS GOYTACAZES,1971-07-13,M,**********,MARCELLO AMARUZZA QUEIROZ,RJ,person,,,,,,,,,,,,,,,,,,,,,,,,,Tulio de alencar  Rio das ostras,2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[""MARCELLO AMARUZZA QUEIROZ""],[""MARCELLO AMARUZZA QUEIROZ""],[],[""MARCELLO""],[""AMARUZZA""],[""QUEIROZ""],,,,[""1971-07-13""],[""CAMPOS DOS GOYTACAZES""],[],[""RJ""],[""**********""],[""M""],[],[],[""**********""],0.0,0.33,0.0,0.5,0.0,0.27,0.5,0.27,0.03,,,0,0,0,0,0,0,0,0,0,0,
7108739f-6414-4cdd-bbe6-241bb1ce56a0,2025-03-01 00:43:32,2499,Claudionor,Salles,<EMAIL>,+5511993436810,"Rua correia d3 lemos,821",,Sao paulo,,,BR,138.117.247.213,,1989-07-28,5511993436810,+55,0.9750413512349625,TradeStation Group,7,Claudionor Salles,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""*********"", ""name"": ""ROBISON APARECIDO DE OLIVEIRA"", ""dob"": ""1953-05-05"", ""gender"": ""M"", ""city"": ""MOGI DAS CRUZES"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""30120328860"", ""name"": ""GISLAINE DIAS MORAES"", ""dob"": ""1981-10-09"", ""gender"": ""F"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511993436810"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""f809319f-6445-40f1-9641-27e830151008"", ""request_id"": ""7108739f-6414-4cdd-bbe6-241bb1ce56a0""}",2586.43,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""*********"", ""name"": ""ROBISON APARECIDO DE OLIVEIRA"", ""dob"": ""1953-05-05"", ""gender"": ""M"", ""city"": ""MOGI DAS CRUZES"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""30120328860"", ""name"": ""GISLAINE DIAS MORAES"", ""dob"": ""1981-10-09"", ""gender"": ""F"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555511993436810"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",f809319f-6445-40f1-9641-27e830151008,7108739f-6414-4cdd-bbe6-241bb1ce56a0,,"[{""id_num"": ""*********"", ""name"": ""ROBISON APARECIDO DE OLIVEIRA"", ""dob"": ""1953-05-05"", ""gender"": ""M"", ""city"": ""MOGI DAS CRUZES"", ""state"": ""SP"", ""type"": ""person""}, {""id_num"": ""30120328860"", ""name"": ""GISLAINE DIAS MORAES"", ""dob"": ""1981-10-09"", ""gender"": ""F"", ""city"": ""SAO PAULO"", ""state"": ""SP"", ""type"": ""person""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555511993436810,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,MOGI DAS CRUZES,1953-05-05,M,*********,ROBISON APARECIDO DE OLIVEIRA,SP,person,,,,SAO PAULO,1981-10-09,F,30120328860,GISLAINE DIAS MORAES,,SP,person,,,,,,,,,,,,,,"Rua correia d3 lemos,821  Sao paulo",2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[""ROBISON APARECIDO DE OLIVEIRA"", ""GISLAINE DIAS MORAES""]","[""ROBISON APARECIDO DE OLIVEIRA"", ""GISLAINE DIAS MORAES""]",[],"[""ROBISON"", ""GISLAINE""]","[""APARECIDO"", ""DIAS""]","[""DE OLIVEIRA"", ""MORAES""]",,,,"[""1953-05-05"", ""1981-10-09""]","[""MOGI DAS CRUZES"", ""SAO PAULO""]",[],"[""SP"", ""SP""]","[""*********"", ""30120328860""]","[""M"", ""F""]",[],[],"[""*********"", ""30120328860""]",0.0,0.49,0.0,0.44,0.0,0.5,0.44,0.5,0.03,,,0,0,0,0,0,0,0,1,0,0,
1e1f21ee-6ba5-44a0-9b54-7031e329a517,2025-04-03 02:41:46,2499,Flavio,Pina,<EMAIL>,+5521974672033,"Rua Sao Salvador, 59 / 1303",,Rio de Janeiro,,,BR,179.218.8.78,,1969-06-16,5521974672033,+55,0.9746133622758512,TradeStation Group,8,Flavio Pina,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555521974672033"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""a710c2a5-1a6a-49ea-93da-60fad3121dba"", ""request_id"": ""1e1f21ee-6ba5-44a0-9b54-7031e329a517""}",2462.94,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [], ""fullname"": """", ""phone_number"": ""555521974672033"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",a710c2a5-1a6a-49ea-93da-60fad3121dba,1e1f21ee-6ba5-44a0-9b54-7031e329a517,,[],,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555521974672033,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"Rua Sao Salvador, 59 / 1303  Rio de Janeiro",2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
46f8ae1c-a536-4883-95df-ddd64e76b641,2025-04-14 02:16:28,2499,Brenno,Bonnet,<EMAIL>,+5521971095767,"Avenida Adolpho de Vasconcelos, 497",Apto. 1103,Rio de Janeiro,,,BR,177.205.209.88,,2002-04-25,5521971095767,+55,0.9745834553809749,TradeStation Group,9,Brenno Bonnet,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""ALESSANDRA DA SILVA MOTTA"", ""dob"": ""1974-05-21"", ""gender"": ""F"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555521971095767"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""0e53b777-f4cd-4da3-b07d-56e54de4729d"", ""request_id"": ""46f8ae1c-a536-4883-95df-ddd64e76b641""}",2561.61,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""ALESSANDRA DA SILVA MOTTA"", ""dob"": ""1974-05-21"", ""gender"": ""F"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555521971095767"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",0e53b777-f4cd-4da3-b07d-56e54de4729d,46f8ae1c-a536-4883-95df-ddd64e76b641,,"[{""id_num"": ""**********"", ""name"": ""ALESSANDRA DA SILVA MOTTA"", ""dob"": ""1974-05-21"", ""gender"": ""F"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555521971095767,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,RIO DE JANEIRO,1974-05-21,F,**********,ALESSANDRA DA SILVA MOTTA,RJ,person,,,,,,,,,,,,,,,,,,,,,,,,,"Avenida Adolpho de Vasconcelos, 497 Apto. 1103 Rio de Janeiro",2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[""ALESSANDRA DA SILVA MOTTA""],[""ALESSANDRA DA SILVA MOTTA""],[],[""ALESSANDRA""],[""""],[""DA SILVA MOTTA""],,,,[""1974-05-21""],[""RIO DE JANEIRO""],[],[""RJ""],[""**********""],[""F""],[],[],[""**********""],0.0,0.26,0.0,0.25,0.0,0.2,0.25,0.2,0.03,,,0,0,0,0,0,0,0,1,0,0,
c1373ff7-e728-44f7-91a1-5c1a5f1d35bf,2025-03-22 18:35:50,2499,ALUISIO,ALBERTTO DANTAS FILHO,<EMAIL>,+5584991268337,"Rua Ceara Mirim, 1140, Torre Frutos,",Apto 2301,Natal,,,BR,189.124.223.40,,1980-10-08,5584991268337,+55,0.9721557986491404,TradeStation Group,10,ALUISIO ALBERTTO DANTAS FILHO,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""*********"", ""name"": ""ALUISIO ALBERTO DANTAS FILHO"", ""dob"": ""1980-10-08"", ""gender"": ""M"", ""city"": ""NATAL"", ""state"": ""RN"", ""type"": ""person""}, {""id_num"": ""**********"", ""name"": ""ANA CLAUDIA MOREIRA DA SILVA"", ""dob"": ""1984-12-31"", ""gender"": ""F"", ""city"": ""NATAL"", ""state"": ""RN"", ""type"": ""person""}, {""id_num"": ""24298785000199"", ""name"": ""ALUISIO ALBERTO DANTAS FILHO ***********"", ""opening_at"": ""2016-03-02"", ""address1"": ""AV ODILON GOMES DE LIMA"", ""address2"": ""2021"", ""address3"": ""CAPIM MACIO"", ""city"": ""NATAL"", ""state"": ""RN"", ""zip"": ""59078400"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555584991268337"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""0bf92a1d-b674-49fa-b3bd-6e40ae27f58c"", ""request_id"": ""c1373ff7-e728-44f7-91a1-5c1a5f1d35bf""}",3009.02,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""*********"", ""name"": ""ALUISIO ALBERTO DANTAS FILHO"", ""dob"": ""1980-10-08"", ""gender"": ""M"", ""city"": ""NATAL"", ""state"": ""RN"", ""type"": ""person""}, {""id_num"": ""**********"", ""name"": ""ANA CLAUDIA MOREIRA DA SILVA"", ""dob"": ""1984-12-31"", ""gender"": ""F"", ""city"": ""NATAL"", ""state"": ""RN"", ""type"": ""person""}, {""id_num"": ""24298785000199"", ""name"": ""ALUISIO ALBERTO DANTAS FILHO ***********"", ""opening_at"": ""2016-03-02"", ""address1"": ""AV ODILON GOMES DE LIMA"", ""address2"": ""2021"", ""address3"": ""CAPIM MACIO"", ""city"": ""NATAL"", ""state"": ""RN"", ""zip"": ""59078400"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555584991268337"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",0bf92a1d-b674-49fa-b3bd-6e40ae27f58c,c1373ff7-e728-44f7-91a1-5c1a5f1d35bf,,"[{""id_num"": ""*********"", ""name"": ""ALUISIO ALBERTO DANTAS FILHO"", ""dob"": ""1980-10-08"", ""gender"": ""M"", ""city"": ""NATAL"", ""state"": ""RN"", ""type"": ""person""}, {""id_num"": ""**********"", ""name"": ""ANA CLAUDIA MOREIRA DA SILVA"", ""dob"": ""1984-12-31"", ""gender"": ""F"", ""city"": ""NATAL"", ""state"": ""RN"", ""type"": ""person""}, {""id_num"": ""24298785000199"", ""name"": ""ALUISIO ALBERTO DANTAS FILHO ***********"", ""opening_at"": ""2016-03-02"", ""address1"": ""AV ODILON GOMES DE LIMA"", ""address2"": ""2021"", ""address3"": ""CAPIM MACIO"", ""city"": ""NATAL"", ""state"": ""RN"", ""zip"": ""59078400"", ""type"": ""business""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555584991268337,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,NATAL,1980-10-08,M,*********,ALUISIO ALBERTO DANTAS FILHO,RN,person,,,,NATAL,1984-12-31,F,**********,ANA CLAUDIA MOREIRA DA SILVA,,RN,person,,AV ODILON GOMES DE LIMA,2021,CAPIM MACIO,NATAL,,,24298785000199,ALUISIO ALBERTO DANTAS FILHO ***********,2016-03-02,RN,business,59078400,"Rua Ceara Mirim, 1140, Torre Frutos, Apto 2301 Natal",2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[""ALUISIO ALBERTO DANTAS FILHO"", ""ANA CLAUDIA MOREIRA DA SILVA"", ""ALUISIO ALBERTO DANTAS FILHO ***********""]","[""ALUISIO ALBERTO DANTAS FILHO"", ""ANA CLAUDIA MOREIRA DA SILVA""]",[""ALUISIO ALBERTO DANTAS FILHO ***********""],"[""ALUISIO"", ""ANA"", ""ALUISIO""]","[""ALBERTO"", ""CLAUDIA MOREIRA"", ""ALBERTO DANTAS FILHO""]","[""DANTAS FILHO"", ""DA SILVA"", ""***********""]",,,,"[""1980-10-08"", ""1984-12-31""]","[""NATAL"", ""NATAL"", ""NATAL""]",[""59078400""],"[""RN"", ""RN"", ""RN""]","[""*********"", ""**********"", ""24298785000199""]","[""M"", ""F""]",[""AV ODILON GOMES DE LIMA""],[""2021""],"[""*********"", ""**********""]",1.0,0.98,1.0,1.0,1.0,0.73,1.0,0.73,0.98,2.0,2.0,1,1,0,1,1,0,0,1,0,0,45
f0bb80d3-1060-498a-8056-0d81d6dc5653,2025-03-23 20:47:29,2499,Luan,Santos,<EMAIL>,+5521981347178,Tua toquio 1943,,Sao Joao,,,BR,45.187.230.2,,1998-10-19,5521981347178,+55,0.9718565435001298,TradeStation Group,11,Luan Santos,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""FLAVIA PADRAO DE CAMPOS"", ""dob"": ""1979-09-20"", ""gender"": ""F"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}, {""id_num"": ""15616692700"", ""name"": ""IAN SAMPAIO TORRES"", ""dob"": ""1993-03-28"", ""gender"": ""M"", ""city"": ""DUQUE DE CAXIAS"", ""state"": ""RJ"", ""type"": ""person""}, {""id_num"": ""16527410034"", ""name"": ""PAULO CESAR TELLES MENDIZABAL"", ""dob"": ""1949-10-02"", ""gender"": ""M"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555521981347178"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""6016f91c-5f2e-4d8f-8a7b-271dc0ab64ce"", ""request_id"": ""f0bb80d3-1060-498a-8056-0d81d6dc5653""}",2583.93,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""FLAVIA PADRAO DE CAMPOS"", ""dob"": ""1979-09-20"", ""gender"": ""F"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}, {""id_num"": ""15616692700"", ""name"": ""IAN SAMPAIO TORRES"", ""dob"": ""1993-03-28"", ""gender"": ""M"", ""city"": ""DUQUE DE CAXIAS"", ""state"": ""RJ"", ""type"": ""person""}, {""id_num"": ""16527410034"", ""name"": ""PAULO CESAR TELLES MENDIZABAL"", ""dob"": ""1949-10-02"", ""gender"": ""M"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555521981347178"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",6016f91c-5f2e-4d8f-8a7b-271dc0ab64ce,f0bb80d3-1060-498a-8056-0d81d6dc5653,,"[{""id_num"": ""**********"", ""name"": ""FLAVIA PADRAO DE CAMPOS"", ""dob"": ""1979-09-20"", ""gender"": ""F"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}, {""id_num"": ""15616692700"", ""name"": ""IAN SAMPAIO TORRES"", ""dob"": ""1993-03-28"", ""gender"": ""M"", ""city"": ""DUQUE DE CAXIAS"", ""state"": ""RJ"", ""type"": ""person""}, {""id_num"": ""16527410034"", ""name"": ""PAULO CESAR TELLES MENDIZABAL"", ""dob"": ""1949-10-02"", ""gender"": ""M"", ""city"": ""RIO DE JANEIRO"", ""state"": ""RJ"", ""type"": ""person""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555521981347178,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,RIO DE JANEIRO,1979-09-20,F,**********,FLAVIA PADRAO DE CAMPOS,RJ,person,,,,DUQUE DE CAXIAS,1993-03-28,M,15616692700,IAN SAMPAIO TORRES,,RJ,person,,,,,RIO DE JANEIRO,1949-10-02,M,16527410034,PAULO CESAR TELLES MENDIZABAL,,RJ,person,,Tua toquio 1943  Sao Joao,2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[""FLAVIA PADRAO DE CAMPOS"", ""IAN SAMPAIO TORRES"", ""PAULO CESAR TELLES MENDIZABAL""]","[""FLAVIA PADRAO DE CAMPOS"", ""IAN SAMPAIO TORRES"", ""PAULO CESAR TELLES MENDIZABAL""]",[],"[""FLAVIA"", ""IAN"", ""PAULO""]","[""PADRAO"", ""SAMPAIO"", ""CESAR TELLES""]","[""DE CAMPOS"", ""TORRES"", ""MENDIZABAL""]",,,,"[""1979-09-20"", ""1993-03-28"", ""1949-10-02""]","[""RIO DE JANEIRO"", ""DUQUE DE CAXIAS"", ""RIO DE JANEIRO""]",[],"[""RJ"", ""RJ"", ""RJ""]","[""**********"", ""15616692700"", ""16527410034""]","[""F"", ""M"", ""M""]",[],[],"[""**********"", ""15616692700"", ""16527410034""]",0.0,0.55,0.0,0.57,0.0,0.5,0.57,0.5,0.03,,,0,0,0,0,0,0,0,0,0,0,
f1ceb07c-f70d-43a7-b972-8144f15885f8,2025-04-10 18:36:25,2499,PEDRO,DE ALMEIDA,<EMAIL>,+5541996527312,"Rua Parnaiba, 456",,Curitiba,,,BR,168.181.48.210,,1988-07-23,5541996527312,+55,0.9715093764006628,TradeStation Group,12,PEDRO DE ALMEIDA,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""36865956800"", ""name"": ""PEDRO HENRIQUE DE ALMEIDA"", ""dob"": ""1988-07-23"", ""gender"": ""M"", ""city"": ""CURITIBA"", ""state"": ""PR"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555541996527312"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""c92bb50e-6dee-49c4-8fea-2d7743d82672"", ""request_id"": ""f1ceb07c-f70d-43a7-b972-8144f15885f8""}",2603.49,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""36865956800"", ""name"": ""PEDRO HENRIQUE DE ALMEIDA"", ""dob"": ""1988-07-23"", ""gender"": ""M"", ""city"": ""CURITIBA"", ""state"": ""PR"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555541996527312"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",c92bb50e-6dee-49c4-8fea-2d7743d82672,f1ceb07c-f70d-43a7-b972-8144f15885f8,,"[{""id_num"": ""36865956800"", ""name"": ""PEDRO HENRIQUE DE ALMEIDA"", ""dob"": ""1988-07-23"", ""gender"": ""M"", ""city"": ""CURITIBA"", ""state"": ""PR"", ""type"": ""person""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555541996527312,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,CURITIBA,1988-07-23,M,36865956800,PEDRO HENRIQUE DE ALMEIDA,PR,person,,,,,,,,,,,,,,,,,,,,,,,,,"Rua Parnaiba, 456  Curitiba",2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,[""PEDRO HENRIQUE DE ALMEIDA""],[""PEDRO HENRIQUE DE ALMEIDA""],[],[""PEDRO""],[""HENRIQUE""],[""DE ALMEIDA""],,,,[""1988-07-23""],[""CURITIBA""],[],[""PR""],[""36865956800""],[""M""],[],[],[""36865956800""],0.0,0.78,1.0,1.0,1.0,1.0,1.0,1.0,0.95,2.0,2.0,1,1,0,1,1,0,0,1,0,0,37
a8e75e25-7de1-4805-8b30-f314125ec851,2025-03-09 17:53:48,2499,Veronica,Born,<EMAIL>,+5551999615082,R. Marechal Hermes 613,ap. 803,Porto Alegre,,,BR,189.38.104.25,,1980-11-07,5551999615082,+55,0.9637144339168316,TradeStation Group,13,Veronica Born,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""***********"", ""name"": ""VERONICA BORN"", ""dob"": ""1980-11-07"", ""gender"": ""F"", ""city"": ""PORTO ALEGRE"", ""state"": ""RS"", ""type"": ""person""}, {""id_num"": ""19883888000186"", ""name"": ""VERONICA BORN ***********"", ""opening_at"": ""2014-03-15"", ""address1"": ""R MARECHAL HERMES"", ""address2"": ""613"", ""address3"": ""CAMAQUA"", ""city"": ""PORTO ALEGRE"", ""state"": ""RS"", ""zip"": ""91910290"", ""type"": ""business""}, {""id_num"": ""89680185000136"", ""name"": ""DOTTO VEICULOS LTDA"", ""opening_at"": ""1984-02-07"", ""address1"": ""AV GETULIO VARGAS"", ""address2"": ""3357"", ""address3"": ""NOSSA SENHORA DAS GRACAS"", ""city"": ""CANOAS"", ""state"": ""RS"", ""zip"": ""92110002"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555551999615082"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""0492139a-3da0-4f6a-8575-4f0c6f76b848"", ""request_id"": ""a8e75e25-7de1-4805-8b30-f314125ec851""}",2598.18,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""***********"", ""name"": ""VERONICA BORN"", ""dob"": ""1980-11-07"", ""gender"": ""F"", ""city"": ""PORTO ALEGRE"", ""state"": ""RS"", ""type"": ""person""}, {""id_num"": ""19883888000186"", ""name"": ""VERONICA BORN ***********"", ""opening_at"": ""2014-03-15"", ""address1"": ""R MARECHAL HERMES"", ""address2"": ""613"", ""address3"": ""CAMAQUA"", ""city"": ""PORTO ALEGRE"", ""state"": ""RS"", ""zip"": ""91910290"", ""type"": ""business""}, {""id_num"": ""89680185000136"", ""name"": ""DOTTO VEICULOS LTDA"", ""opening_at"": ""1984-02-07"", ""address1"": ""AV GETULIO VARGAS"", ""address2"": ""3357"", ""address3"": ""NOSSA SENHORA DAS GRACAS"", ""city"": ""CANOAS"", ""state"": ""RS"", ""zip"": ""92110002"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555551999615082"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",0492139a-3da0-4f6a-8575-4f0c6f76b848,a8e75e25-7de1-4805-8b30-f314125ec851,,"[{""id_num"": ""***********"", ""name"": ""VERONICA BORN"", ""dob"": ""1980-11-07"", ""gender"": ""F"", ""city"": ""PORTO ALEGRE"", ""state"": ""RS"", ""type"": ""person""}, {""id_num"": ""19883888000186"", ""name"": ""VERONICA BORN ***********"", ""opening_at"": ""2014-03-15"", ""address1"": ""R MARECHAL HERMES"", ""address2"": ""613"", ""address3"": ""CAMAQUA"", ""city"": ""PORTO ALEGRE"", ""state"": ""RS"", ""zip"": ""91910290"", ""type"": ""business""}, {""id_num"": ""89680185000136"", ""name"": ""DOTTO VEICULOS LTDA"", ""opening_at"": ""1984-02-07"", ""address1"": ""AV GETULIO VARGAS"", ""address2"": ""3357"", ""address3"": ""NOSSA SENHORA DAS GRACAS"", ""city"": ""CANOAS"", ""state"": ""RS"", ""zip"": ""92110002"", ""type"": ""business""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555551999615082,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,PORTO ALEGRE,1980-11-07,F,***********,VERONICA BORN,RS,person,R MARECHAL HERMES,613,CAMAQUA,PORTO ALEGRE,,,19883888000186,VERONICA BORN ***********,2014-03-15,RS,business,91910290,AV GETULIO VARGAS,3357,NOSSA SENHORA DAS GRACAS,CANOAS,,,89680185000136,DOTTO VEICULOS LTDA,1984-02-07,RS,business,92110002,R. Marechal Hermes 613 ap. 803 Porto Alegre,2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[""VERONICA BORN"", ""VERONICA BORN ***********"", ""DOTTO VEICULOS LTDA""]",[""VERONICA BORN""],"[""VERONICA BORN ***********"", ""DOTTO VEICULOS LTDA""]","[""VERONICA"", ""VERONICA"", ""DOTTO""]","["""", ""BORN"", ""VEICULOS""]","[""BORN"", ""***********"", ""LTDA""]",,,,[""1980-11-07""],"[""PORTO ALEGRE"", ""PORTO ALEGRE"", ""CANOAS""]","[""91910290"", ""92110002""]","[""RS"", ""RS"", ""RS""]","[""***********"", ""19883888000186"", ""89680185000136""]",[""F""],"[""R MARECHAL HERMES"", ""AV GETULIO VARGAS""]","[""613"", ""3357""]",[""***********""],1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,0.98,2.0,2.0,1,1,0,1,1,0,0,1,0,0,45
31910f92-042d-47cb-8c26-194963b52b2d,2025-03-08 13:44:27,7921,Eliane Aparecida,Pereira Brandão,<EMAIL>,+5527998912449,RUA ALEXANDRE HERCULANO,,JACAREÍ,,12312-545,BR,,,1978-10-15,5527998912449,+55,0.9605855346671465,Wise,14,Eliane Aparecida Pereira Brandão,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""MANUELA SOUZA ABADE"", ""dob"": ""1981-04-04"", ""gender"": ""M"", ""city"": ""SERRA"", ""state"": ""ES"", ""type"": ""person""}, {""id_num"": ""***********"", ""name"": ""MARCELO LORRAN ABADE SANTOS"", ""dob"": ""2003-02-11"", ""gender"": ""M"", ""city"": ""SERRA"", ""state"": ""ES"", ""type"": ""person""}, {""id_num"": ""50104653000199"", ""name"": ""50 104 653 MANUELA SOUZA ABADE"", ""opening_at"": ""2023-03-28"", ""address1"": ""R PARAISO"", ""address2"": ""29A"", ""address3"": ""JARDIM TROPICAL"", ""city"": ""SERRA"", ""state"": ""ES"", ""zip"": ""29162070"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555527998912449"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""5a8b94b9-cc69-4095-b090-2e0ddb7002cc"", ""request_id"": ""31910f92-042d-47cb-8c26-194963b52b2d""}",3237.93,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""MANUELA SOUZA ABADE"", ""dob"": ""1981-04-04"", ""gender"": ""M"", ""city"": ""SERRA"", ""state"": ""ES"", ""type"": ""person""}, {""id_num"": ""***********"", ""name"": ""MARCELO LORRAN ABADE SANTOS"", ""dob"": ""2003-02-11"", ""gender"": ""M"", ""city"": ""SERRA"", ""state"": ""ES"", ""type"": ""person""}, {""id_num"": ""50104653000199"", ""name"": ""50 104 653 MANUELA SOUZA ABADE"", ""opening_at"": ""2023-03-28"", ""address1"": ""R PARAISO"", ""address2"": ""29A"", ""address3"": ""JARDIM TROPICAL"", ""city"": ""SERRA"", ""state"": ""ES"", ""zip"": ""29162070"", ""type"": ""business""}], ""fullname"": """", ""phone_number"": ""555527998912449"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",5a8b94b9-cc69-4095-b090-2e0ddb7002cc,31910f92-042d-47cb-8c26-194963b52b2d,,"[{""id_num"": ""**********"", ""name"": ""MANUELA SOUZA ABADE"", ""dob"": ""1981-04-04"", ""gender"": ""M"", ""city"": ""SERRA"", ""state"": ""ES"", ""type"": ""person""}, {""id_num"": ""***********"", ""name"": ""MARCELO LORRAN ABADE SANTOS"", ""dob"": ""2003-02-11"", ""gender"": ""M"", ""city"": ""SERRA"", ""state"": ""ES"", ""type"": ""person""}, {""id_num"": ""50104653000199"", ""name"": ""50 104 653 MANUELA SOUZA ABADE"", ""opening_at"": ""2023-03-28"", ""address1"": ""R PARAISO"", ""address2"": ""29A"", ""address3"": ""JARDIM TROPICAL"", ""city"": ""SERRA"", ""state"": ""ES"", ""zip"": ""29162070"", ""type"": ""business""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555527998912449,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,SERRA,1981-04-04,M,**********,MANUELA SOUZA ABADE,ES,person,,,,SERRA,2003-02-11,M,***********,MARCELO LORRAN ABADE SANTOS,,ES,person,,R PARAISO,29A,JARDIM TROPICAL,SERRA,,,50104653000199,50 104 653 MANUELA SOUZA ABADE,2023-03-28,ES,business,29162070,RUA ALEXANDRE HERCULANO  JACAREÍ  12312-545,2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[""MANUELA SOUZA ABADE"", ""MARCELO LORRAN ABADE SANTOS"", ""50 104 653 MANUELA SOUZA ABADE""]","[""MANUELA SOUZA ABADE"", ""MARCELO LORRAN ABADE SANTOS""]",[""50 104 653 MANUELA SOUZA ABADE""],"[""MANUELA"", ""MARCELO"", ""50""]","[""SOUZA"", ""LORRAN ABADE"", ""104 653 MANUELA SOUZA""]","[""ABADE"", ""SANTOS"", ""ABADE""]",,,,"[""1981-04-04"", ""2003-02-11""]","[""SERRA"", ""SERRA"", ""SERRA""]",[""29162070""],"[""ES"", ""ES"", ""ES""]","[""**********"", ""***********"", ""50104653000199""]","[""M"", ""M""]",[""R PARAISO""],[""29A""],"[""**********"", ""***********""]",0.0,0.45,0.0,0.35,0.0,0.42,0.35,0.42,0.03,,,0,0,0,0,0,0,0,0,0,0,
801025f1-d9f1-498a-81ea-e3399ae4236d,2025-03-15 23:02:48,2499,Nayara,Vale,<EMAIL>,+5563984381782,Av na 1,Mirante do parque,Palmas,,,BR,179.155.133.217,,1994-12-20,5563984381782,+55,0.956946660935503,TradeStation Group,15,Nayara Vale,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""LUDYMILLA LOURENCO SIQUEIRA LIMA"", ""dob"": ""1986-11-24"", ""gender"": ""F"", ""city"": ""PORTO NACIONAL"", ""state"": ""TO"", ""type"": ""person""}, {""id_num"": ""97943878191"", ""name"": ""JOAO PEDRO PEREIRA PASSOS"", ""dob"": ""1984-03-02"", ""gender"": ""M"", ""city"": ""PORTO NACIONAL"", ""state"": ""TO"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555563984381782"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}], ""trans_id"": ""33f7bec8-351f-4e1f-9f18-70b29805e08b"", ""request_id"": ""801025f1-d9f1-498a-81ea-e3399ae4236d""}",2610.14,true,200,Search successful.,"[{""result"": true, ""status_code"": 200, ""data"": {""persondetails"": [{""id_num"": ""**********"", ""name"": ""LUDYMILLA LOURENCO SIQUEIRA LIMA"", ""dob"": ""1986-11-24"", ""gender"": ""F"", ""city"": ""PORTO NACIONAL"", ""state"": ""TO"", ""type"": ""person""}, {""id_num"": ""97943878191"", ""name"": ""JOAO PEDRO PEREIRA PASSOS"", ""dob"": ""1984-03-02"", ""gender"": ""M"", ""city"": ""PORTO NACIONAL"", ""state"": ""TO"", ""type"": ""person""}], ""fullname"": """", ""phone_number"": ""555563984381782"", ""risk_score"": 97, ""signals"": ""IDM-RP-006,IDM-RP-013"", ""phone_details"": {""type1"": ""landline"", ""type2"": ""UNKNOWN"", ""country_code"": ""BR"", ""status"": ""syntax"", ""disposable"": false, ""disposable_provider"": null, ""valid"": false, ""suspicious_format"": false, ""valid_format"": false, ""portability_details"": {""status"": null, ""first"": null, ""last"": null, ""times"": null}, ""original_carrier"": null, ""current_carrier"": null, ""first_seen"": null, ""breach_details"": {""count"": null, ""first_breach"": null, ""last_breach"": null, ""list"": null, ""data"": [], ""data_lists"": []}}}, ""source"": ""5""}]",33f7bec8-351f-4e1f-9f18-70b29805e08b,801025f1-d9f1-498a-81ea-e3399ae4236d,,"[{""id_num"": ""**********"", ""name"": ""LUDYMILLA LOURENCO SIQUEIRA LIMA"", ""dob"": ""1986-11-24"", ""gender"": ""F"", ""city"": ""PORTO NACIONAL"", ""state"": ""TO"", ""type"": ""person""}, {""id_num"": ""97943878191"", ""name"": ""JOAO PEDRO PEREIRA PASSOS"", ""dob"": ""1984-03-02"", ""gender"": ""M"", ""city"": ""PORTO NACIONAL"", ""state"": ""TO"", ""type"": ""person""}]",,[],[],,,,BR,,false,,,,,,,,syntax,false,landline,UNKNOWN,false,false,555563984381782,97.0,"IDM-RP-006,IDM-RP-013",true,5,200.0,PORTO NACIONAL,1986-11-24,F,**********,LUDYMILLA LOURENCO SIQUEIRA LIMA,TO,person,,,,PORTO NACIONAL,1984-03-02,M,97943878191,JOAO PEDRO PEREIRA PASSOS,,TO,person,,,,,,,,,,,,,,Av na 1 Mirante do parque Palmas,2025-04-20,false,landline,false,,false,BR,syntax,97.0,,,,,,,,,UNKNOWN,,,,,,,,,,,,,,,,,,,,,,,,5,"[""LUDYMILLA LOURENCO SIQUEIRA LIMA"", ""JOAO PEDRO PEREIRA PASSOS""]","[""LUDYMILLA LOURENCO SIQUEIRA LIMA"", ""JOAO PEDRO PEREIRA PASSOS""]",[],"[""LUDYMILLA"", ""JOAO""]","[""LOURENCO SIQUEIRA"", ""PEDRO PEREIRA""]","[""LIMA"", ""PASSOS""]",,,,"[""1986-11-24"", ""1984-03-02""]","[""PORTO NACIONAL"", ""PORTO NACIONAL""]",[],"[""TO"", ""TO""]","[""**********"", ""97943878191""]","[""F"", ""M""]",[],[],"[""**********"", ""97943878191""]",0.0,0.28,0.0,0.27,0.0,0.25,0.27,0.25,0.03,,,0,0,0,0,0,0,0,0,0,0,
edfe0dd6-87a7-4e32-851a-343e6ba315ab,2025-03-23 07:15:19,2499,Raju,Raju,<EMAIL>,+918523805465,1-6-45/1/A,Hyderabad,Hyderabad,,,IN,152.59.198.56,,1974-08-05,918523805465,+91,0.9997797796943476,TradeStation Group,1,Raju Raju,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""c0965eb4-7875-4e6c-a57d-b87b21432d7f"", ""request_id"": ""edfe0dd6-87a7-4e32-851a-343e6ba315ab""}",805.79,true,200,Search successful.,[],c0965eb4-7875-4e6c-a57d-b87b21432d7f,edfe0dd6-87a7-4e32-851a-343e6ba315ab,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,1-6-45/1/A Hyderabad Hyderabad,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
39535327-4fdc-4882-98a0-80d72210be99,2025-03-10 17:06:44,2499,Athul,Acharya,<EMAIL>,+918310469875,Farangipet Post kumpanamajal house,"Alankar Nivas, Farangipet",Mangalore,,,IN,*************,,1998-05-07,918310469875,+91,0.9987175049266125,TradeStation Group,2,Athul Acharya,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""7079fda5-9380-4a5d-bb4d-10520087d28f"", ""request_id"": ""39535327-4fdc-4882-98a0-80d72210be99""}",823.28,true,200,Search successful.,[],7079fda5-9380-4a5d-bb4d-10520087d28f,39535327-4fdc-4882-98a0-80d72210be99,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"Farangipet Post kumpanamajal house Alankar Nivas, Farangipet Mangalore",2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
f6b96e2a-1b4e-4640-a4f1-21d33c3272a4,2025-03-26 01:53:26,2499,VENKATESAN,PARTHASARATHY,<EMAIL>,+919840822634,"Flat No.3, KAVERI","13 , Bharadiar II Street",Palavanthangal,,,IN,*************,,1965-07-15,919840822634,+91,0.998180387558,TradeStation Group,3,VENKATESAN PARTHASARATHY,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""378d5da5-a8fb-4df6-95d9-fe90cc566abb"", ""request_id"": ""f6b96e2a-1b4e-4640-a4f1-21d33c3272a4""}",805.63,true,200,Search successful.,[],378d5da5-a8fb-4df6-95d9-fe90cc566abb,f6b96e2a-1b4e-4640-a4f1-21d33c3272a4,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"Flat No.3, KAVERI 13 , Bharadiar II Street Palavanthangal",2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
5f5df567-d11b-4a6e-9f05-9fa1e5e1b629,2025-04-22 10:26:17,2499,Mukul,Verma,<EMAIL>,+917701969201,Narela Delhi 110040,,Narela,,,IN,152.58.86.251,,1994-02-04,917701969201,+91,0.9965798728579539,TradeStation Group,4,Mukul Verma,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""b55c675d-b45d-4cee-939c-c123bcaf948a"", ""request_id"": ""5f5df567-d11b-4a6e-9f05-9fa1e5e1b629""}",808.58,true,200,Search successful.,[],b55c675d-b45d-4cee-939c-c123bcaf948a,5f5df567-d11b-4a6e-9f05-9fa1e5e1b629,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Narela Delhi 110040  Narela,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
5a3e3d91-41c9-49ce-90fd-79c713abb110,2025-04-09 01:28:51,2499,uttam,kumbhar,<EMAIL>,+917991006289,belpada ganapada belpada,,Bolangir,,,IN,117.99.42.21,,2000-04-08,917991006289,+91,0.9962469115889553,TradeStation Group,5,uttam kumbhar,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""fa6c0242-d92b-44d6-bbca-6e4e24ecfc0f"", ""request_id"": ""5a3e3d91-41c9-49ce-90fd-79c713abb110""}",834.92,true,200,Search successful.,[],fa6c0242-d92b-44d6-bbca-6e4e24ecfc0f,5a3e3d91-41c9-49ce-90fd-79c713abb110,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,belpada ganapada belpada  Bolangir,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
6caff61a-76b1-4e58-976a-ff0199ae008b,2025-03-01 07:40:16,2499,Dineash,Khivasara,<EMAIL>,+919561018489,48 pune,,pune,,,IN,58.84.61.189,,1992-12-27,919561018489,+91,0.9955921730063353,TradeStation Group,6,Dineash Khivasara,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""dadf24f2-56e8-4fb1-806c-b97814dfd637"", ""request_id"": ""6caff61a-76b1-4e58-976a-ff0199ae008b""}",840.54,true,200,Search successful.,[],dadf24f2-56e8-4fb1-806c-b97814dfd637,6caff61a-76b1-4e58-976a-ff0199ae008b,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,48 pune  pune,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
f728e6de-d9fc-4b24-921f-93748e42444e,2025-04-22 10:31:30,2499,Mukul,Verma,<EMAIL>,+917701969201,Narela Delhi 110040,,Narela,,,IN,10.0.0.171,,1994-02-04,917701969201,+91,0.9951680928345316,TradeStation Group,7,Mukul Verma,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""9bc524c3-152c-4d9d-8961-ce2a2027a17c"", ""request_id"": ""f728e6de-d9fc-4b24-921f-93748e42444e""}",833.66,true,200,Search successful.,[],9bc524c3-152c-4d9d-8961-ce2a2027a17c,f728e6de-d9fc-4b24-921f-93748e42444e,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Narela Delhi 110040  Narela,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
8a42a4cf-79d1-4076-969d-b759b7d925f7,2025-03-04 07:16:26,2499,Shubham,Gujar,<EMAIL>,+918669128550,"484, Subhash Road",Agasti chitrmandir,Akole,,,IN,59.97.221.57,,1996-01-30,918669128550,+91,0.9944679922740162,TradeStation Group,8,Shubham Gujar,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""978c6581-f907-4d5f-b1e7-717e3cfd6a76"", ""request_id"": ""8a42a4cf-79d1-4076-969d-b759b7d925f7""}",836.74,true,200,Search successful.,[],978c6581-f907-4d5f-b1e7-717e3cfd6a76,8a42a4cf-79d1-4076-969d-b759b7d925f7,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"484, Subhash Road Agasti chitrmandir Akole",2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
c9c35e5b-f6eb-43c8-b8d3-3a4814611507,2025-03-18 18:45:50,9783,Gravit,Kohli,<EMAIL>,+919997400008,229 177 Street no 1 Rajendra Nagar,,Dehradun,UT,248001,IN,160.202.38.33,GOLPK9730J,1999-11-25,919997400008,+91,0.9938213050727269,Lili - International,9,Gravit Kohli,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""230b021f-f9d7-44df-b989-a60848f3c478"", ""request_id"": ""c9c35e5b-f6eb-43c8-b8d3-3a4814611507""}",892.23,true,200,Search successful.,[],230b021f-f9d7-44df-b989-a60848f3c478,c9c35e5b-f6eb-43c8-b8d3-3a4814611507,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,229 177 Street no 1 Rajendra Nagar  Dehradun UT 248001,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
03526760-b49d-4e84-af5b-2e8c945065b2,2025-03-02 14:59:42,2499,mustaque,der,<EMAIL>,+917829173724,nagori vas,,basu,,,IN,45.62.218.255,,1997-03-05,917829173724,+91,0.9902351765066942,TradeStation Group,10,mustaque der,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""a933083b-2457-47a3-8c1c-6f053eb36cff"", ""request_id"": ""03526760-b49d-4e84-af5b-2e8c945065b2""}",969.81,true,200,Search successful.,[],a933083b-2457-47a3-8c1c-6f053eb36cff,03526760-b49d-4e84-af5b-2e8c945065b2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,nagori vas  basu,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
43c6b66a-23f3-486f-bc6f-699a45ab5522,2025-04-15 12:27:35,5626,Raj K,Gopalakrishnan,<EMAIL>,+919811130000,A113 RESIDENCY RD. SHANTHALA NAGAR,4 WEWORK GALAXY,BENGALURU,,560025,IN,157.51.24.245,,1973-10-23,919811130000,+91,0.9896707757160662,Efficient Capital Labs Inc,11,Raj K Gopalakrishnan,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""fa3b0c28-f2cf-4fb1-a254-8514f0cec224"", ""request_id"": ""43c6b66a-23f3-486f-bc6f-699a45ab5522""}",824.48,true,200,Search successful.,[],fa3b0c28-f2cf-4fb1-a254-8514f0cec224,43c6b66a-23f3-486f-bc6f-699a45ab5522,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,A113 RESIDENCY RD. SHANTHALA NAGAR 4 WEWORK GALAXY BENGALURU  560025,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
6a798636-ece6-4407-8eed-e78bb3d32d03,2025-03-08 03:48:47,2499,Reuben,Bilon,<EMAIL>,+918078936890,At Joseph engineering College,,Mangalore,,,IN,223.186.15.173,,2004-02-15,918078936890,+91,0.9871464220440578,TradeStation Group,12,Reuben Bilon,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""412bb331-5036-4684-b9d2-f02bf901d693"", ""request_id"": ""6a798636-ece6-4407-8eed-e78bb3d32d03""}",852.96,true,200,Search successful.,[],412bb331-5036-4684-b9d2-f02bf901d693,6a798636-ece6-4407-8eed-e78bb3d32d03,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,At Joseph engineering College  Mangalore,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
d3d87b20-0493-4e34-b172-743e76005eb8,2025-03-18 10:20:07,2499,Abdulaziz,Aziz,<EMAIL>,+916200797418,Hatt gacchhi,Hatt gachi,Bihar,,,IN,152.59.146.55,,1999-04-03,916200797418,+91,0.9871074018655113,TradeStation Group,13,Abdulaziz Aziz,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""31de427d-b923-44ab-8ecd-31078d10e2d1"", ""request_id"": ""d3d87b20-0493-4e34-b172-743e76005eb8""}",821.56,true,200,Search successful.,[],31de427d-b923-44ab-8ecd-31078d10e2d1,d3d87b20-0493-4e34-b172-743e76005eb8,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Hatt gacchhi Hatt gachi Bihar,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
a0770037-682f-4ae4-a1c2-04b96e5669d8,2025-03-08 07:41:13,2499,Aniket,Ale,<EMAIL>,+918928744916,Virshi,Amravati,Amravati,,,IN,103.178.133.24,,2001-10-10,918928744916,+91,0.9870013708997545,TradeStation Group,14,Aniket Ale,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""329751b3-3615-49cf-bb96-a59e904b4d97"", ""request_id"": ""a0770037-682f-4ae4-a1c2-04b96e5669d8""}",836.58,true,200,Search successful.,[],329751b3-3615-49cf-bb96-a59e904b4d97,a0770037-682f-4ae4-a1c2-04b96e5669d8,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Virshi Amravati Amravati,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
29615052-4cc4-4f45-a88b-dbd47b90ed17,2025-04-19 14:42:26,2499,Aditya,Jagtap,<EMAIL>,+917083963923,Maharastra,Saswad,Pune,,,IN,106.213.83.57,,1976-07-16,917083963923,+91,0.9864348588162399,TradeStation Group,15,Aditya Jagtap,"{""result"": true, ""status_code"": 200, ""message"": ""Search successful."", ""profile"": [], ""trans_id"": ""2c27d9a3-0961-400b-bec9-d1f4de6adae3"", ""request_id"": ""29615052-4cc4-4f45-a88b-dbd47b90ed17""}",837.61,true,200,Search successful.,[],2c27d9a3-0961-400b-bec9-d1f4de6adae3,29615052-4cc4-4f45-a88b-dbd47b90ed17,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Maharastra Saswad Pune,2025-04-20,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,[],[],[],[],[],[],,,,[],[],[],[],[],[],[],[],[],,,,,,,,,,0.0,0.0,0,0,0,0,0,0,0,0,0,0,
