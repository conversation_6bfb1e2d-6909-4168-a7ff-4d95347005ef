package me.socure.rulecode.operators.derived

import me.socure.rulecode.common.models.{CategoricalRuleCode, NumericalRuleCode, RuleCodeResponse}
import me.socure.rulecode.operators.derived.MnivlDerivedRuleCodesResolver.{MNIVL_900001, MNIVL_900023, MNIVL_900024, MNIVL_900025}
import org.apache.commons.csv.{CSVFormat, CSVParser}
import org.json4s.jackson.JsonMethods
import org.scalatest.{FreeSpec, Matchers}

import java.io.FileInputStream
import java.nio.charset.StandardCharsets
import scala.collection.JavaConverters._

class MnivlDerivedRuleCodesResolverTest extends FreeSpec with Matchers {

  "MNIVL Derived Rulecodes" - {
    Seq("MNIVL.200024", "MNIVL.200025", "MNIVL.200026").foreach { ruleCode =>
      val testStream = new FileInputStream(getClass.getClassLoader.getResource(s"MNIVL/$ruleCode.csv").getFile)
      val csvReader = CSVParser.parse(testStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
      val headers = csvReader.getHeaderNames.asScala.toList
      csvReader
        .getRecords
        .asScala.map { row => {
          val numericalRuleCodes = (0 until headers.size - 1).map { i =>
            val header = headers(i)
            val entry = row.get(i)
            if (entry != "NA" && header.startsWith("N_")) Seq(NumericalRuleCode(header.split("_").last, row.get(i).toDouble)) else Seq.empty[NumericalRuleCode]
          }.reduce(_ ++ _)
          val categoricalRuleCodes = (0 until headers.size - 1).map { i =>
            val header = headers(i)
            val entry = row.get(i)
            if (entry != "NA" && header.startsWith("C_")) Seq(CategoricalRuleCode(header.split("_").last, row.get(i))) else Seq.empty[CategoricalRuleCode]
          }.reduce(_ ++ _)
          val expected = Option(row.get(headers.size - 1)).filter(_ != "NA")
          val response = RuleCodeResponse(
            numericalRuleCodes,
            categoricalRuleCodes,
            Seq.empty[String]
          )

          s"[ $ruleCode - ${row.getRecordNumber} ] - $expected" in {
            val derivedRuleCodeResponse = MnivlDerivedRuleCodesResolver.generate(response)
            val result = derivedRuleCodeResponse.numericalRuleCodes.find(
              n => n.name.equalsIgnoreCase(ruleCode)
            )
            result match {
              case Some(numericalRuleCode) => numericalRuleCode.value shouldBe expected.get.toDouble
              case None => expected shouldBe None
            }
          }
        }}
    }
  }

  "computeMnivl_900023_900024_900025" - {

    "when rule900001 present" - {
      "should correctly parse and split Brazilian names" in {
        val ruleCodeResponse = RuleCodeResponse(
          numericalRuleCodes = Seq.empty,
          categoricalRuleCodes = Seq(CategoricalRuleCode(MNIVL_900001, "JOAO DA SILVA,MARIA DOS SANTOS")),
          errors = Seq.empty
        )
        
        val requestJson = JsonMethods.parse("""{"fullName": "JOAO DA SILVA"}""")

        val result = MnivlDerivedRuleCodesResolver.generate(ruleCodeResponse, requestJson, Map.empty)
        
        val categoricalCodes = result.categoricalRuleCodes
        categoricalCodes should contain(CategoricalRuleCode(MNIVL_900023, "JOAO::MARIA"))
        categoricalCodes should not contain(CategoricalRuleCode(MNIVL_900024, ""))
        categoricalCodes should contain(CategoricalRuleCode(MNIVL_900025, "DA SILVA::DOS SANTOS"))
      }

      "should handle multiple names" in {
        val ruleCodeResponse = RuleCodeResponse(
          numericalRuleCodes = Seq.empty,
          categoricalRuleCodes = Seq(CategoricalRuleCode(MNIVL_900001, "JOAO MARIA DA SILVA,JOAO MARIA DOS SALVA")),
          errors = Seq.empty
        )
        
        val requestJson = JsonMethods.parse("""{"fullName": "JOAO MARIA DA SILVA"}""")
        
        val result = MnivlDerivedRuleCodesResolver.generate(ruleCodeResponse, requestJson, Map.empty)
        
        val categoricalCodes = result.categoricalRuleCodes
        categoricalCodes should contain(CategoricalRuleCode(MNIVL_900023, "JOAO::JOAO"))
        categoricalCodes should contain(CategoricalRuleCode(MNIVL_900024, "MARIA::MARIA"))
        categoricalCodes should contain(CategoricalRuleCode(MNIVL_900025, "DA SILVA::DOS SALVA"))
      }

      "should handle empty middle names" in {
        val ruleCodeResponse = RuleCodeResponse(
          numericalRuleCodes = Seq.empty,
          categoricalRuleCodes = Seq(CategoricalRuleCode(MNIVL_900001, "JOAO SILVA,MARIA SANTOS")),
          errors = Seq.empty
        )
        
        val requestJson = JsonMethods.parse("""{"fullName": "JOAO SILVA"}""")
        
        val result = MnivlDerivedRuleCodesResolver.generate(ruleCodeResponse, requestJson, Map.empty)
        
        val categoricalCodes = result.categoricalRuleCodes
        categoricalCodes should contain(CategoricalRuleCode(MNIVL_900023, "JOAO::MARIA"))
        categoricalCodes should not contain(CategoricalRuleCode(MNIVL_900024, ""))
        categoricalCodes should contain(CategoricalRuleCode(MNIVL_900025, "SILVA::SANTOS"))
      }
    }

    "when rule900001 is missing" - {
      "should return original response" in {
        val originalResponse = RuleCodeResponse(
          numericalRuleCodes = Seq.empty,
          categoricalRuleCodes = Seq.empty,
          errors = Seq.empty
        )
        
        val requestJson = JsonMethods.parse("""{"fullName": "JOAO DA SILVA"}""")
        
        val result = MnivlDerivedRuleCodesResolver.generate(originalResponse, requestJson, Map.empty)
        result.categoricalRuleCodes.filter(e => Seq(MNIVL_900023, MNIVL_900024, MNIVL_900025).contains(e.name)) shouldBe empty
      }
    }

  }
}
