package me.socure.rulecode.operators.derived

import me.socure.rulecode.common.models._
import me.socure.rulecode.common.utilities.DateUtility
import org.apache.commons.csv.{CSVFormat, CSVParser}
import org.json4s.native.JsonMethods.parse
import org.scalatest.{FreeSpec, Matchers}

import java.io.FileInputStream
import java.nio.charset.StandardCharsets
import scala.collection.JavaConverters._

class ImivlDerivedRuleCodesResolverTest extends FreeSpec with Matchers {

  "IMIVL Derived Rulecodes" - {
    Seq("IMIVL.200024", "IMIVL.200025", "IMIVL.200026").foreach { ruleCode =>
      val testStream = new FileInputStream(getClass.getClassLoader.getResource(s"IMIVL/$ruleCode.csv").getFile)
      val csvReader = CSVParser.parse(testStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
      val headers = csvReader.getHeaderNames.asScala.toList
      csvReader
        .getRecords
        .asScala.map { row => {
          val numericalRuleCodes = (0 until headers.size - 1).map { i =>
            val header = headers(i)
            val entry = row.get(i)
            if (entry != "NA" && header.startsWith("N_")) Seq(NumericalRuleCode(header.split("_").last, row.get(i).toDouble)) else Seq.empty[NumericalRuleCode]
          }.reduce(_ ++ _)
          val categoricalRuleCodes = (0 until headers.size - 1).map { i =>
            val header = headers(i)
            val entry = row.get(i)
            if (entry != "NA" && header.startsWith("C_")) Seq(CategoricalRuleCode(header.split("_").last, row.get(i))) else Seq.empty[CategoricalRuleCode]
          }.reduce(_ ++ _)
          val expected = Option(row.get(headers.size - 1)).filter(_ != "NA")
          val response = RuleCodeResponse(
            numericalRuleCodes,
            categoricalRuleCodes,
            Seq.empty[String]
          )

          s"[ $ruleCode - ${row.getRecordNumber} ] - $expected" in {
            val derivedRuleCodeResponse = ImivlDerivedRuleCodesResolver.generate(response)
            val result = derivedRuleCodeResponse.numericalRuleCodes.find(
              n => n.name.equalsIgnoreCase(ruleCode)
            )
            result match {
              case Some(numericalRuleCode) => numericalRuleCode.value shouldBe expected.get.toDouble
              case None => expected shouldBe None
            }
          }
        }}
    }
  }



}