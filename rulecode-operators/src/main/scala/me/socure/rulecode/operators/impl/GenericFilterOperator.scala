package me.socure.rulecode.operators.impl

import me.socure.rulecode.common.utilities.Json4sUtility
import me.socure.rulecode.operators.OperatorV2
import me.socure.rulecode.operators.options.{FilterType, GenericFilterOptions, OrderType}
import org.json4s.JsonAST.{JArray, JNothing, JValue}
import org.json4s.{DefaultFormats, Formats}

/**
 * <b>GenericFilterOperator</b> filters the input array present/not-present in filterCriteria provided
 *
 * @param inputs  - one JArray has to be provided as the input.
 * @param options - the <b>filterTyoe</b> json field attribute used to be filtered. The values can be <b>In|NotIn|InSubStr|NotInSubStr</b>
 *                - the <b>attrToBeFiltered</b> attribute used to be filter in given json field.
 *                - the <b>filterCriteria</b> sequence of values to be filtered based on which the <b>inputs</b> will be filtered.
 *                - the <b>sortByAttr</b> optional sort attribute for ordering the result.
 *                - the <b>orderType</b> ascending or descending order. The values can be <b>asc|desc</b>
 * @example
 * <p>
 * <pre>
 * GenericFilterOperator.process(List(parse("[{""url"":""htdws://img.com/static"",""label"":""twitter""},{""url"":""htdws://img.com/static"",""label"":""github""}]")),
 * GenericFilterOptions(FilterType.In,"label",Set("twitter","google")
 * // returns [{""url"":""htdws://img.com/static"",""label"":""twitter""}]
 * </pre>
 */
object GenericFilterOperator extends OperatorV2[JValue, GenericFilterOptions] {

  private implicit val formats: Formats = DefaultFormats

  def process(inputs: Seq[JValue], options: GenericFilterOptions): Option[JValue] = {
    if(inputs.nonEmpty) {
      val jArr2 = inputs.tail.foldLeft(Seq.empty[JValue]) { (out, v) =>
        v match {
          case JArray(l) => out ++ l
          case x: JValue => out ++ Seq(x)
        }
      }

      if(options.returnNAOnEmptyInputs && jArr2.filter(_ != JNothing).size == 0) {
        None
      } else {
        val filterCriteria = if (options.isCaseInsensitive) {
          options.filterCriteria.filter(!_.isEmpty).map(_.toLowerCase) ++ jArr2.flatMap(_.extractOpt[String]).map(_.toLowerCase()).toSet
        } else if (options.ignoreEmptyFilterCriteria) {
          options.filterCriteria.filter(!_.isEmpty) ++ jArr2.flatMap(_.extractOpt[String]).toSet
        } else options.filterCriteria ++ jArr2.flatMap(_.extractOpt[String]).toSet
        inputs.head match {
          case JArray(arr) => getFilteredResult(arr, filterCriteria, options)
          case jValue: JValue => getFilteredResult(List(jValue), filterCriteria, options)
          case _ => None
        }
      }

    } else None
  }

  private def getFilteredResult(arr: List[JValue], filterCriteria: Set[String], options: GenericFilterOptions) = {
    val filteredResult = arr.filter {
      obj =>
        val mappedValue = if (options.attrToBeFilteredSafeExtract) {
          if (options.attrToBeFiltered.isEmpty) obj else Json4sUtility.getOptionalJValue(obj, options.attrToBeFiltered).getOrElse(JNothing)
        } else if (options.attrToBeFiltered.isEmpty) obj else obj \ options.attrToBeFiltered

        mappedValue match {
          case JNothing => false
          case value =>
            val extractedValue = if (options.isCaseInsensitive) value.extract[String].toLowerCase else value.extract[String]
            val toCompareValue = if (options.trimInputs) extractedValue.trim else extractedValue
            options.filterType match {
              case FilterType.In => filterCriteria.contains(toCompareValue)
              case FilterType.NotIn => !filterCriteria.contains(toCompareValue)
              case FilterType.InSubStr => !filterCriteria.filter(filterVal => toCompareValue.matches(s"(.*)$filterVal(.*)")).isEmpty
              case FilterType.NotInSubStr => filterCriteria.filter(filterVal => toCompareValue.matches(s"(.*)$filterVal(.*)")).isEmpty
            }
        }
    }
    if (options.sortByAttr.isEmpty) {
      JArray(filteredResult).toSome
    } else {
      val orderedResult = filteredResult.sortWith {
        (a, b) =>
          options.orderType match {
            case OrderType.asc => (a \ options.sortByAttr.get).extract[String] < (b \ options.sortByAttr.get).extract[String]
            case OrderType.desc => (a \ options.sortByAttr.get).extract[String] > (b \ options.sortByAttr.get).extract[String]
          }
      }
      JArray(orderedResult).toSome
    }
  }
}


