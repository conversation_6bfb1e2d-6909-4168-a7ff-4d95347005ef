package me.socure.rulecode.operators.options

import me.socure.rulecode.operators.OperatorV2Options
import me.socure.rulecode.operators.options.ContainsConditions.ContainsCondition
import me.socure.rulecode.operators.options.ContainsValueType.ContainsValueType

/**
 * <AUTHOR> Kumar
 */
case class GenericContainsOptions(condition: ContainsCondition,
                                  valueType: ContainsValueType = ContainsValueType.Collection) extends OperatorV2Options

object GenericContainsOptions {
  def apply(inputMap: Map[String, String]): GenericContainsOptions = {
    val condition = inputMap.get("condition").map(ContainsConditions.withName).getOrElse(throw new IllegalArgumentException("condition attribute not found for ContainsOperator"))
    val valueType = inputMap.get("valueType").map(ContainsValueType.withName).getOrElse(ContainsValueType.Collection)
    GenericContainsOptions(condition, valueType)
  }
}

/**
 * anySubString -> matches if the username (string / list) contains full or part of any of the string in the nickname
 * list. this is different from 'any' which matches only the whole string (excluding the substring)
 */
object ContainsConditions extends Enumeration {
  type ContainsCondition = Value
  val any: ContainsConditions.Value = Value("any")
  val anySubString: ContainsConditions.Value = Value("anySubString")
  val anyNonEmptySubString: ContainsConditions.Value = Value("anyNonEmptySubString")
  val all: ContainsConditions.Value = Value("all")
  val none: ContainsConditions.Value = Value("none")
}

object ContainsValueType extends Enumeration {
  type ContainsValueType = Value
  val String: ContainsValueType.Value = Value("string")
  val Collection: ContainsValueType.Value = Value("collection")
}