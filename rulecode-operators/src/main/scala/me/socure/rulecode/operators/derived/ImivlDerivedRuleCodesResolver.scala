package me.socure.rulecode.operators.derived

import com.socure.common.concurrent.FutureWithStartTime
import me.socure.address.client.AddressClientSecured
import me.socure.address.client.factory.AddressClientFactory
import me.socure.address.common.models.{Address, AddressVerificationQuery, AddressesVerificationRequest, AddressesVerificationResponse}
import me.socure.common.transaction.id.TrxId
import me.socure.internal.worklog.InternalWorkLogger
import me.socure.internal.worklog.constants.InternalWorkLogSources
import me.socure.internal.worklog.factory.InternalWorkLoggerFactory
import me.socure.model.ErrorResponse
import me.socure.rulecode.common.models.{CategoricalRuleCode, NumericalRuleCode, RuleCodeResponse}
import me.socure.rulecode.common.utilities.{ConfigLoa<PERSON>, Json4sUtility}
import me.socure.rulecode.filedata.RulecodeDataFileLookup
import org.json4s.JsonAST
import org.json4s.JsonAST.{JArray, JObject}
import org.slf4j.LoggerFactory

import java.time.LocalDate
import java.util.concurrent.TimeUnit
import scala.collection.Seq
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future, TimeoutException}
import scala.util.{Failure, Success, Try}

object ImivlDerivedRuleCodesResolver  extends DerivedRuleCodesResolver {

  implicit val ec: ExecutionContext = scala.concurrent.ExecutionContext.Implicits.global
  implicit val localDateOrdering: Ordering[LocalDate] = Ordering.fromLessThan(_ isBefore _)

  private val logger = LoggerFactory.getLogger(getClass)
  private val internalWorkLogger: InternalWorkLogger = InternalWorkLoggerFactory.getLogger()
  val addressSecuredClient: AddressClientSecured = AddressClientFactory.create(ConfigLoader.getConfigs.getString("address-service.endpoint"), ConfigLoader.getConfigs.getString("address-service.alternateEndpoint"))

  type DerivedRuleCodeGenerator = (RuleCodeResponse, JsonAST.JValue, Map[String, JsonAST.JValue]) => RuleCodeResponse

  override def vendor: String = "IMIVL"

  val IDMERIT_PHONE_NAME_MATCH_LEVEL = s"$vendor.200024"
  val IDMERIT_PHONE_FIRSTNAME_MATCH_TIERS = s"$vendor.200025"
  val IDMERIT_PHONE_LASTNAME_MATCH_TIERS = s"$vendor.200026"
  val IDMERIT_ADDRESS_MISMATCH_CITY_STATE_ZIP = s"$vendor.200050"
  val IDMERIT_ADDRESS_PARTIAL = s"$vendor.200051"
  val IDMERIT_NAME_ADDRESS_DOB_MATCH = s"$vendor.200052"
  val IDMERIT_NAME_DOB_MATCH = s"$vendor.200053"
  val IDMERIT_NAME_ADDRESS_MATCH = s"$vendor.200054"
  val IDMERIT_PHONE_PERSON_NAMES = s"$vendor.900002"
  val IDMERIT_PHONE_BUSINESS_NAMES = s"$vendor.900003"
  val IDMERIT_PHONE_PERSON_IDS = s"$vendor.900019"
  val IDMERIT_PHONE_FULL_ADDRESS_LIST = s"$vendor.900016"
  val IDMERIT_MATCH_ADDRESS = s"$vendor.200003"

  val IMIVL_200001 = s"$vendor.200001"
  val IMIVL_200002 = s"$vendor.200002"
  val IMIVL_200003 = s"$vendor.200003"
  val IMIVL_200009 = s"$vendor.200009"
  val IMIVL_200010 = s"$vendor.200010"
  val IMIVL_200004 = s"$vendor.200004"
  val IMIVL_200016 = s"$vendor.200016"
  val IMIVL_200017 = s"$vendor.200017"
  val IMIVL_200018 = s"$vendor.200018"
  val IMIVL_200019 = s"$vendor.200019"
  val IMIVL_200020 = s"$vendor.200020"
  val IMIVL_200021 = s"$vendor.200021"
  val IMIVL_200022 = s"$vendor.200022"
  val IMIVL_200023 = s"$vendor.200023"
  val IMIVL_900011 = s"$vendor.900011"
  val IMIVL_900012 = s"$vendor.900012"
  val IMIVL_900013 = s"$vendor.900013"

  override def generate(
                         ruleCodeResponse: RuleCodeResponse,
                         requestJsonAST: JsonAST.JValue,
                         dbJsonMap: Map[String, JsonAST.JValue],
                         errorResponseMap: Map[String, ErrorResponse],
                         ruleCodeDataFileLookup: RulecodeDataFileLookup): RuleCodeResponse = {
    try {
      generateRuleCodes(
        Seq(computeImivl200024, computeImivl200025, computeImivl200026, computeImivl200050, computeImivl200051, computeImivl200052,
          computeImivl200053, computeImivl200054, computeImivl900016_200003
        ),
        ruleCodeResponse,
        requestJsonAST,
        dbJsonMap
      )
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occured while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }

  }

  private def generateRuleCodes(
                                 generators: Seq[DerivedRuleCodeGenerator],
                                 ruleCodeResponse: RuleCodeResponse,
                                 requestJsonAST: JsonAST.JValue,
                                 dbJsonMap: Map[String, JsonAST.JValue]
                               ): RuleCodeResponse = {
    generators.foldLeft(ruleCodeResponse)((r, f) => f(r, requestJsonAST, dbJsonMap))
  }

  private val computeImivl200024: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val imivl200016 = getResult(IMIVL_200016,ruleCodeResponse)
    val imivl200017 = getResult(IMIVL_200017,ruleCodeResponse)
    val imivl200018 = getResult(IMIVL_200018,ruleCodeResponse)
    val imivl200019 = getResult(IMIVL_200019,ruleCodeResponse)
    val imivl200020 = getResult(IMIVL_200020,ruleCodeResponse)
    val imivl200021 = getResult(IMIVL_200021,ruleCodeResponse)
    val imivl200022 = getResult(IMIVL_200022,ruleCodeResponse)
    val imivl200023 = getResult(IMIVL_200023,ruleCodeResponse)
    val result = if(imivl200016.contains(1.0) || imivl200017.exists(_ > 0.70)) Some(0.98)
    else if ( (imivl200020.contains(1.0) || imivl200023.contains(1.0)) && (imivl200018.contains(1.0) || imivl200022.contains(1.0)) ) Some(0.95)
    else if ( (imivl200019.exists(_ > 0.9) || imivl200022.contains(1.0)) && (imivl200021.exists(_ > 0.9) || imivl200023.contains(1.0)) ) Some(0.90)
    else if ( (imivl200019.exists(_ > 0.7) || imivl200022.contains(1.0)) && (imivl200021.exists(_ > 0.7) || imivl200023.contains(1.0)) ) Some(0.86)
    else if ( imivl200020.contains(1.0) || imivl200023.contains(1.0)) Some(0.84)
    else if ( imivl200021.exists(_ > 0.7 )) Some(0.79)
    else if ( imivl200018.contains(1.0) || imivl200022.contains(1.0)) Some(0.72)
    else if ( imivl200019.exists(_ > 0.7 )) Some(0.68)
    else if ( imivl200021.exists(_ <= 0.7 ) && imivl200019.exists(_ <= 0.7 )) Some(0.03)
    else if ( imivl200021.exists(_ <= 0.7 ) || imivl200020.contains(0.0)) Some(0.07)
    else if ( imivl200019.exists(_ <= 0.7 ) || imivl200018.contains(0.0)) Some(0.11)
    else None

    result match {
      case Some(v) =>
        RuleCodeResponse (
          ruleCodeResponse.numericalRuleCodes ++ Seq(
            NumericalRuleCode(IDMERIT_PHONE_NAME_MATCH_LEVEL, v)
          ),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private val computeImivl200025: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val imivl200018 = getResult(IMIVL_200018,ruleCodeResponse)
    val imivl200019 = getResult(IMIVL_200019,ruleCodeResponse)
    val imivl200022 = getResult(IMIVL_200022,ruleCodeResponse)
    val result = if(imivl200018.contains(1.0) || imivl200022.contains(1.0)) Some(2.0)
    else if(imivl200019.exists(_ > 0.7) && imivl200018.contains(0.0) && imivl200022.contains(0.0)) Some(1.0)
    else if ( (imivl200019.isEmpty || imivl200019.exists(_ <= 0.7)) && imivl200018.contains(0.0) && imivl200022.contains(0.0)) Some(-1.0)
    else if (imivl200018.isEmpty) Some(0.0)
    else None

    result match {
      case Some(v) =>
        RuleCodeResponse (
          ruleCodeResponse.numericalRuleCodes ++ Seq(
            NumericalRuleCode(IDMERIT_PHONE_FIRSTNAME_MATCH_TIERS, v)
          ),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private val computeImivl200026: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val imivl200020 = getResult(IMIVL_200020,ruleCodeResponse)
    val imivl200021 = getResult(IMIVL_200021,ruleCodeResponse)
    val imivl200023 = getResult(IMIVL_200023,ruleCodeResponse)
    val result = if(imivl200020.contains(1.0) || imivl200023.contains(1.0)) Some(2.0)
    else if(imivl200021.exists(_ > 0.7) && imivl200020.contains(0.0) && imivl200023.contains(0.0)) Some(1.0)
    else if ( (imivl200021.isEmpty || imivl200021.exists(_ <= 0.7)) && imivl200020.contains(0.0) && imivl200023.contains(0.0)) Some(-1.0)
    else if (imivl200020.isEmpty) Some(0.0)
    else None

    result match {
      case Some(v) =>
        RuleCodeResponse (
          ruleCodeResponse.numericalRuleCodes ++ Seq(
            NumericalRuleCode(IDMERIT_PHONE_LASTNAME_MATCH_TIERS, v)
          ),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private val computeImivl200050: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val inputCity = Json4sUtility.getOptionalString(requestJsonAST, "city")
    val inputState = Json4sUtility.getOptionalString(requestJsonAST, "state")
    val inputZip = Json4sUtility.getOptionalString(requestJsonAST, "zip")
    val imivl200009 = getResult(IMIVL_200009,ruleCodeResponse)
    val imivl200010 = getResult(IMIVL_200010,ruleCodeResponse)
    val imivl200004 = getResult(IMIVL_200004,ruleCodeResponse)

    val result = if(imivl200009.contains(0.0) || imivl200010.contains(0.0) || imivl200004.contains(0.0) ||
      (inputCity.isDefined && imivl200009.isEmpty) ||
      (inputState.isDefined && imivl200010.isEmpty) ||
      (inputZip.isDefined && imivl200004.isEmpty)) Some(1.0) else Some(0.0)
    result match {
      case Some(v) =>
        RuleCodeResponse (
          ruleCodeResponse.numericalRuleCodes ++ Seq(
            NumericalRuleCode(IDMERIT_ADDRESS_MISMATCH_CITY_STATE_ZIP, v)
          ),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private val computeImivl200051: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val imivl900011 = getCategoricalResult(IMIVL_900011,ruleCodeResponse).filter(_!="")
    val imivl900012 = getCategoricalResult(IMIVL_900012,ruleCodeResponse).filter(_!="")
    val imivl900013 = getCategoricalResult(IMIVL_900013,ruleCodeResponse).filter(_!="")

    val imivl900011Val = if(imivl900011.isDefined) 1.0 else 0.0
    val imivl900012Val = if(imivl900012.isDefined) 1.0 else 0.0
    val imivl900013Val = if(imivl900013.isDefined) 1.0 else 0.0

    val result = if((imivl900011Val + imivl900012Val + imivl900013Val) == 1.0 || (imivl900011Val + imivl900012Val + imivl900013Val) == 2.0) Some(1.0) else Some(0.0)

    result match {
      case Some(v) =>
        RuleCodeResponse (
          ruleCodeResponse.numericalRuleCodes ++ Seq(
            NumericalRuleCode(IDMERIT_ADDRESS_PARTIAL, v)
          ),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private val computeImivl200052: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val imivl200001 = getResult(IMIVL_200001,ruleCodeResponse)
    val imivl200002 = getResult(IMIVL_200002,ruleCodeResponse)
    val imivl200003 = getResult(IMIVL_200003,ruleCodeResponse)

    val result = if(imivl200001.contains(1.0) && imivl200002.contains(1.0) && imivl200003.contains(1.0)) Some(1.0) else Some(0.0)

    result match {
      case Some(v) =>
        RuleCodeResponse (
          ruleCodeResponse.numericalRuleCodes ++ Seq(
            NumericalRuleCode(IDMERIT_NAME_ADDRESS_DOB_MATCH, v)
          ),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private val computeImivl200053: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val imivl200001 = getResult(IMIVL_200001,ruleCodeResponse)
    val imivl200002 = getResult(IMIVL_200002,ruleCodeResponse)

    val result = if(imivl200001.contains(1.0) && imivl200002.contains(1.0)) Some(1.0) else Some(0.0)

    result match {
      case Some(v) =>
        RuleCodeResponse (
          ruleCodeResponse.numericalRuleCodes ++ Seq(
            NumericalRuleCode(IDMERIT_NAME_DOB_MATCH, v)
          ),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private val computeImivl200054: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val imivl200001 = getResult(IMIVL_200001,ruleCodeResponse)
    val imivl200003 = getResult(IMIVL_200003,ruleCodeResponse)

    val result = if(imivl200001.contains(1.0) && imivl200003.contains(1.0)) Some(1.0) else Some(0.0)

    result match {
      case Some(v) =>
        RuleCodeResponse (
          ruleCodeResponse.numericalRuleCodes ++ Seq(
            NumericalRuleCode(IDMERIT_NAME_ADDRESS_MATCH, v)
          ),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private val computeImivl900016_200003: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {

    val addressesVerificationRequestOpt = dbJsonMap.get("http.idmerit") match {
      case Some(lookup) =>
        Json4sUtility.getOptionalArray(lookup, "responses.response.profile.data.persondetails") match {
          case Some(s) =>
            val addressesValue = s.flatMap {
            case JArray(arr) =>
              arr.collect {
                case obj @ JObject(_) =>
                  Some(Address(
                    addressLine1 = Json4sUtility.getOptionalString(obj,"address1").getOrElse(""),
                    addressLine2 = Json4sUtility.getOptionalString(obj,"address2"),
                    city = Json4sUtility.getOptionalString(obj,"city"),
                    state = Json4sUtility.getOptionalString(obj,"state"),
                    zipcode = Json4sUtility.getOptionalString(obj,"zip"),
                    country = Json4sUtility.getOptionalString(obj,"country").getOrElse("")
                  ))
              }.flatten
            case obj @ JObject(_) =>
              Some(Address(
                addressLine1 = Json4sUtility.getOptionalString(obj,"address1").getOrElse(""),
                addressLine2 = Json4sUtility.getOptionalString(obj,"address2"),
                city = Json4sUtility.getOptionalString(obj,"city"),
                state = Json4sUtility.getOptionalString(obj,"state"),
                zipcode = Json4sUtility.getOptionalString(obj,"zip"),
                country = Json4sUtility.getOptionalString(obj,"country").getOrElse("")
              ))
            case _ => None
          }
            Some(AddressesVerificationRequest(
              transactionId = Json4sUtility.getOptionalString(requestJsonAST,"transactionId").getOrElse(""),
              accountId =  Json4sUtility.getOptionalLong(requestJsonAST,"accountId").getOrElse(0L),
              maskPiiEnabled = Json4sUtility.getOptionalBoolean(requestJsonAST,"maskPii").getOrElse(false),
              query = Seq(AddressVerificationQuery.PREPROCESS),
              addresses = addressesValue
            ))
          case _ => None
        }
      case None => None
    }

    addressesVerificationRequestOpt match {
      case Some(addressesVerificationRequest) =>
        try {
          val fullAddresses = Try(Await.result(callAddressService(addressesVerificationRequest = addressesVerificationRequest), Duration.apply(30, TimeUnit.SECONDS))) match {
            case Success(Right(response)) =>
              response.addressesDetails.map(address =>
              createFullAddress(address.components.addressLine1, address.components.addressLine2, address.components.city, address.components.state, address.components.zipcode)
            )
            case Success(Left(error)) =>
              logger.info(s"Address service returned error: ${error.message}")
              Seq.empty
            case Failure(ex) =>
              logger.error(s"Timeout or error calling address service", ex)
              Seq.empty
          }

          val inputAddress1 = Json4sUtility.getOptionalString(requestJsonAST, "std_address").getOrElse("")
          val inputAddress2 = Json4sUtility.getOptionalString(requestJsonAST, "stdAddress2").getOrElse("")
          val inputCity = Json4sUtility.getOptionalString(requestJsonAST, "city").getOrElse("")
          val inputState = Json4sUtility.getOptionalString(requestJsonAST, "state").getOrElse("")
          val inputZip = Json4sUtility.getOptionalString(requestJsonAST, "zip").getOrElse("")
          val inputFullAddress = createFullAddress(inputAddress1, inputAddress2, inputCity, inputState, inputZip)

          val imivl900016 = fullAddresses.mkString(",")
          val imivl200003 = if (fullAddresses.map(_.toLowerCase).contains(inputFullAddress.toLowerCase)) 1.0 else 0.0

          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(
              NumericalRuleCode(IDMERIT_MATCH_ADDRESS, imivl200003)
            ),
            ruleCodeResponse.categoricalRuleCodes ++ Seq(
              CategoricalRuleCode(IDMERIT_PHONE_FULL_ADDRESS_LIST, imivl900016)
            ),
            ruleCodeResponse.errors
          )
        } catch {
          case ex: TimeoutException =>
            logger.error("Address service call timed out", ex)
            ruleCodeResponse.copy(
              errors = ruleCodeResponse.errors :+ "For IMIVL Vendor, Address service timeout"
            )
          case ex: Exception =>
            logger.error("Error processing address verification", ex)
            ruleCodeResponse.copy(
              errors = ruleCodeResponse.errors :+ "For IMIVL Vendor, Internal error processing address"
            )
        }
      case None =>
        logger.warn("No valid addresses found in IDMerit response")
        ruleCodeResponse
    }
  }

  private def createFullAddress(address1: String, address2:String, city: String, state: String, zip:String) : String = {
    address1 + address2 + city + "," + state + zip
  }

  private def callAddressService(addressesVerificationRequest: AddressesVerificationRequest, useMock: Boolean = false)(implicit ec: ExecutionContext): Future[Either[ErrorResponse, AddressesVerificationResponse]] = {
    implicit val trxnId = TrxId(addressesVerificationRequest.transactionId)
    try {
      val futureWrapper = FutureWithStartTime(addressSecuredClient.verifyGlobalAddresses(addressesVerificationRequest, useMock))
      futureWrapper.future.map { response =>
        if (response.result.isLeft) {
          val errorResponse = response.result.left.get
          logger.error(s"Address service returned an error with status code : ${errorResponse.code} and message : ${errorResponse.message} ")
          internalWorkLogger.error(trxnId.value, InternalWorkLogSources.AddressServiceClient,
            "callAddressService", s"Address service returned an error with status code : ${errorResponse.code} and message : ${errorResponse.message} ",
            emitMetrics = true)
        }
        response.result
      }
    } catch {
      case e : Exception =>
        logger.error("Address service returned an error"+e)
        Future.successful(Left(ErrorResponse(500, s"Error calling address service: ${e.getMessage}")))
    }
  }

  private def getResult(ruleName: String, ruleCodeResponse: RuleCodeResponse): Option[Double] = {
    ruleCodeResponse.numericalRuleCodes.find(r => ruleName.equals(r.name)).map(_.value)
  }
  private def getCategoricalResult(ruleName: String, ruleCodeResponse: RuleCodeResponse): Option[String] = {
    ruleCodeResponse.categoricalRuleCodes.find(r => ruleName.equals(r.name)).map(_.value)
  }

}
