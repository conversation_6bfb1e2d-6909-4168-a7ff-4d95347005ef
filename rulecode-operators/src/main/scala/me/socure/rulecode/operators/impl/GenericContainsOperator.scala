package me.socure.rulecode.operators.impl

import me.socure.rulecode.operators.OperatorV2
import me.socure.rulecode.operators.options.{ContainsConditions, ContainsValueType, GenericContainsOptions}
import org.json4s.JsonAST.{JArray, J<PERSON><PERSON>, <PERSON><PERSON>othing, JValue}
import org.json4s.{DefaultFormats, Formats}

/**
 * <b>GenericContainsOperator</b> checks if the values of the first JArray is present in the second JArray based on the condition provided.
 *
 * @param inputs  - two JArrays has to be provided as the input. If there are two JArrays arr1 and arr2, the values of arr2 will be looked up in arr1.
 * @param options - the <b>condition</b> based on which the contains operation should be performed. The values can be <b>all|none|any</b>
 * @example
 * <p>
 * <pre>
 * GenericContainsOperator.process(List(parse("""["1","2"]"""), parse("""["1","2"]""")), GenericContainsOptions(condition = ContainsConditions.all)) // returns true
 * </pre>
 */
object GenericContainsOperator extends OperatorV2[JValue, GenericContainsOptions] {

  private implicit val formats: Formats = DefaultFormats

  override def process(inputs: Seq[JValue], options: GenericContainsOptions): Option[JBool] = {
    if (inputs.nonEmpty) {
      val jVal1 = inputs.head
      val jArr2 = inputs.tail.foldLeft(Seq.empty[JValue]) { (out, v) =>
        v match {
          case JArray(l) => out ++ l
          case x: JValue => out ++ Seq(x)
        }
      }

      val jArr1 = jVal1 match {
        case jArr: JArray => jArr
        case jVal: JValue => JArray(List(jVal))
        case _ => JNothing
      }

      options.valueType match {
        case ContainsValueType.String =>
          (jArr1, jArr2) match {
            case (JArray(arr1), arr2) if arr1.exists(_ != JNothing) =>
              val str1 = jArr1.apply(0).extractOpt[String]
              str1 match {
                case Some(s) =>
                  if (arr2.exists(_ != JNothing)) {
                    val condition = options.condition
                    condition match {
                      case ContainsConditions.all =>
                        Option(JBool(arr2.forall(item => contains(s, item))))
                      case ContainsConditions.none =>
                        Option(JBool(arr2.forall(item => !contains(s, item))))
                      case ContainsConditions.any =>
                        Option(JBool(arr2.exists(item => contains(s, item))))
                      case _ => None
                    }
                  } else {
                    Some(JBool(false))
                  }
                case _ => None
              }
            case _ => None
          }
        case _ =>
          (jArr1, jArr2) match {
            case (JArray(arr1), arr2) if arr1.exists(_ != JNothing) && arr2.exists(_ != JNothing) =>
              val condition = options.condition
              condition match {
                case ContainsConditions.all =>
                  Option(JBool(arr2.forall(item => arr1.contains(item))))
                case ContainsConditions.none =>
                  Option(JBool(arr2.forall(item => !arr1.contains(item))))
                case ContainsConditions.any =>
                  Option(JBool(arr1.exists(elem => arr2.contains(elem))))
                case ContainsConditions.anySubString =>
                  val s1 = arr1.flatMap(_.extractOpt[String])
                  val s2 = arr2.flatMap(_.extractOpt[String])
                  Option(JBool(s1.exists(s1 => s2.exists(s2 => s1.contains(s2)))))
                case ContainsConditions.anyNonEmptySubString =>
                  val s1 = arr1.flatMap(_.extractOpt[String]).filter(_.trim.nonEmpty)
                  val s2 = arr2.flatMap(_.extractOpt[String]).filter(_.trim.nonEmpty)
                  Option(JBool(s1.nonEmpty && s2.nonEmpty && s1.exists(s1 => s2.exists(s2 => s1.contains(s2)))))
                case _ => None
              }
            case _ => None
          }
      }
    } else None
  }

  private def contains(str: String, item: JValue): Boolean = {
    item match {
      case JNothing => true
      case _ =>
        item.extractOpt[String] match {
          case Some(itemStr) => str.contains(itemStr)
          case _ => false
        }
    }
  }
}
