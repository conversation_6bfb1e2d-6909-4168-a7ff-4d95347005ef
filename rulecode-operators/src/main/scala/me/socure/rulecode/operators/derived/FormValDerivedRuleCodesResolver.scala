package me.socure.rulecode.operators.derived

import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat
import com.rockymadden.stringmetric.similarity.LevenshteinMetric
import com.socure.common.enrichments.{RichDouble, RichStringSplit}
import com.socure.common.struct.Countries.Country
import com.socure.common.struct.{Countries, Email, SubmissionDate}
import com.socure.common.time.basicDateUTC
import com.socure.domain.socure.ProfileProviders.ProfileProvider
import com.socure.domain.socure.device.{Device => FormDevice}
import com.socure.domain.socure.dob.{BirthDate, DateOfBirth}
import com.socure.domain.socure.payments.{Payments => FormPayments}
import com.socure.domain.socure.postal.AddressTypes.AddressType
import com.socure.domain.socure.postal.{AddressTypes, PostalAddress, RawPostalAddress}
import com.socure.domain.socure.request.{Form, OrderChannels, ProviderInfo}
import com.socure.domain.socure.scoring.Score
import com.socure.domain.socure.{GeographicCoordinates, Name, Phone, ProfileProviders}
import dispatch.Future
import me.socure.address.common.data.model.Units
import me.socure.address.common.data.{AreacodeResolverService, ZipAreacodeResolverService, ZipcodeResolverService}
import me.socure.common.json.bean.v3_0.Address
import me.socure.common.json.bean.v3_0.device.Device
import me.socure.common.json.bean.v3_0.payment.Payments
import me.socure.common.smartystreets.factory.SmartyPreprocessorFactory
import me.socure.common.smartystreets.model.StreetAddressRequest
import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import me.socure.pii.standardization.common.model.response.PIIStandardizationResponse
import me.socure.rulecode.common.logic.RulecodeLogicRegistry
import me.socure.rulecode.common.logic.util.HelperAlgorithms
import me.socure.rulecode.common.models._
import me.socure.rulecode.common.processors.{CleanStringForStopWordsProcessor, GetTimeAdjustedDate}
import me.socure.rulecode.common.utilities.MatcherUtility.{cleanN, dEdit, roundUpScore}
import me.socure.rulecode.common.utilities.{Json4sUtility, MatcherUtility}
import me.socure.rulecode.filedata.{EmailIntelligenceFiles, RulecodeDataFileLookup}
import me.socure.rulecode.operators.models.{AthleteData, NGramGenerationInput}
import me.socure.rulecode.operators.util.AthletesDataResolverUtil.{filterClosestMatchedAthletes, isActiveAthletesPresent, isRetiredAthletesPresent, loadAthletesData}
import me.socure.rulecode.operators.util.FMVALCommonValidationsUtils.{getPIIResponse, logger}
import me.socure.rulecode.operators.util.PhoneMnoUtil.cleanString
import me.socure.rulecode.operators.util._
import me.socure.util.{PhonePreprocessorUtil, Utilities}
import org.apache.commons.lang3.StringUtils
import org.joda.time._
import org.joda.time.format.{DateTimeFormat, DateTimeFormatter, ISODateTimeFormat}
import org.json4s.JsonAST.{JArray, JString}
import org.json4s.jackson.Serialization
import org.json4s.{JInt, JNothing, JValue, JsonAST}
import org.slf4j.LoggerFactory
import scalaz.Scalaz.ToOptionIdOps

import java.net.InetAddress
import java.time.{Instant, ZoneId}
import java.util.Date
import java.util.regex.Pattern
import scala.Option.option2Iterable
import scala.annotation.tailrec
import scala.collection.mutable.ArrayBuffer
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext}
import scala.io.Source
import scala.util.{Failure, Success, Try}
import scala.util.matching.Regex

object FormValDerivedRuleCodesResolver extends DerivedRuleCodesResolver {

  private val logger = LoggerFactory.getLogger(getClass)

  type DerivedRuleCodeGenerator = (RuleCodeResponse, String, Map[String, JsonAST.JValue], RulecodeDataFileLookup) => RuleCodeResponse

  private lazy val zipCodeResolver: ZipcodeResolverService = new ZipcodeResolverService
  private lazy val zipAreacodeResolverService: ZipAreacodeResolverService = new ZipAreacodeResolverService(zipCodeResolver, new AreacodeResolverService)
  lazy val dobFormats = Seq(ISODateTimeFormat.date(), ISODateTimeFormat.dateTime(), DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ssZ"), DateTimeFormat.forPattern("yyyy-MM-dd"),
    DateTimeFormat.forPattern("yyyyMMdd"), DateTimeFormat.forPattern("yyyy/MM/dd"), DateTimeFormat.forPattern("MM/dd/yyyy"))
  private val NYTimeZone = DateTimeZone.forID("America/New_York")
  private val HolidayFormatUS = DateTimeFormat.forPattern("yyyy-MM-dd").withZone(NYTimeZone)
  private val UsHolidays = Source
    .fromInputStream(getClass.getResourceAsStream("/form/holidays.txt"))
    .getLines()
    .map(_.trim)
    .filter(_.nonEmpty)
    .map(HolidayFormatUS.parseLocalDate)
    .toSet

  val popularDomains = Source
    .fromInputStream(getClass.getResourceAsStream("/email/email_providers_by_country.csv"))
    .getLines()
    .drop(1)
    .map(row => row.split(",")(0))
    .toSet
  val compactOrderedDate: DateTimeFormatter = DateTimeFormat.forPattern("YYYY-MM-dd")
  lazy val AthletesData: Map[String, Seq[AthleteData]] = loadAthletesData
  val DualApplicantPattern: Pattern = Pattern.compile("(?i:\\s(and|or)\\s)")
  val sanctionedCountries = List("AF", "BY", "CF", "CU", "CD", "ET", "HK", "IR", "IQ", "LB", "LY", "ML", "NI", "KP", "RU", "SO", "SS", "SD", "SY", "UA", "VE", "YE", "BI", "TM", "ZW")

  val ActiveAthleteStatus = "active"
  val RetiredAthleteStatus = "retired"

  private val ruleDateFormat = DateTimeFormat.forPattern("yyyyMMdd")


  override def vendor: String = "FMVAL"

  val FM_EMAIL_CONTAIN_FIRST_NAME = s"$vendor.300002"
  val FM_EMAIL_CONTAIN_SURNAME = s"$vendor.300003"
  val FM_EMAIL_CONTAINS_DOB_YEAR = s"$vendor.300004"
  val FM_EMAIL_CONTAINS_FIRSTNAME_NICKNAME = s"$vendor.300054"
  val FM_EMAIL_CONTAINS_SURNAME_NICKNAME = s"$vendor.300055"
  val FM_EMAIL_FIRSTNAME_FUZZY_MATCH = s"$vendor.300056"
  val FM_EMAIL_SURNAME_FUZZY_MATCH = s"$vendor.300057"
  val FM_EMAIL_IS_TUMBLED = s"$vendor.300058"
  val EMAIL_HAS_A_FIRSTNAME_MATCH = s"$vendor.300061"
  val EMAIL_HAS_A_SURNAME_MATCH = s"$vendor.300062"
  val FM_EMAIL_HANDLE_PERPLEXITY_RAW = s"$vendor.300091"
  val FM_EMAIL_HANDLE_PERPLEXITY_NORMALIZED = s"$vendor.300092"
  val FM_NATIONALID_PERPLEXITY_RAW = s"$vendor.300107"
  val FM_NATIONALID_PERPLEXITY_NORMALIZED = s"$vendor.300108"
  val FM_RANDOM_NATIONALID_PERPLEXITY_RAW = s"$vendor.300109"
  val FM_RANDOM_NATIONALID_PERPLEXITY_NORMALIZED = s"$vendor.300110"
  val FM_PHONE_PERPLEXITY_RAW = s"$vendor.300111"
  val FM_PHONE_PERPLEXITY_NORM = s"$vendor.300112"
  val FM_PHYSICALADDRESS_PERPLEXITY_RAW = s"$vendor.300113"
  val FM_PHYSICALADDRESS_PERPLEXITY_NORMALIZED = s"$vendor.300114"

  private val FM_SSN_ISSUE_STATE_MATCH = s"$vendor.300674"
  private val FM_SSN_LAST4_CONSECUTIVE_DIGITS = s"$vendor.300675"
  private val FM_SSN_LAST4_YOB_MATCH = s"$vendor.300676"
  private val FM_SSN_LAST4_REPEATED_DIGITS = s"$vendor.300677"
  private val FM_SSN_AREACODE_BETWEEN_800_899 = s"$vendor.300678"
  private val FM_SSN_IS_IN_POCKETBOOK_LIST = s"$vendor.300679"
  private val FM_SSN_ISSUEYEAR_YOB_GAP = s"$vendor.300680"
  private val FM_SSN_ISSUEYEAR_YOB_ANOMALY_LT_1990 = s"$vendor.300681"
  private val FM_SSN_ISSUEYEAR_YOB_ANOMALY_GTE_1990 = s"$vendor.300682"

  val FM_BILLING_FULL_NAME_MISMATCH_BILLING_EMAIL = s"$vendor.400037"

  val FM_IP_IS_INTERNAL = s"$vendor.500120"

  val FM_EMAIL_DOMAIN_IS_FORTUNE_500_COMPANY = s"$vendor.600004"
  val FM_EMAIL_FORTUNE_500_COMPANY_RANKING = s"$vendor.600005"
  val FM_EMAIL_TRIGRAM_MEDIAN_FREQUENCIES_IN_ALIAS = s"$vendor.600016"
  val FM_EMAIL_BIGRAM_MEDIAN_FREQUENCIES_IN_ALIAS = s"$vendor.600017"
  val FM_EMAIL_USERNAME_STRING_SCRAMBLED_SCORE = s"$vendor.600022"
  val FM_EMAIL_CONTAINS_HS_OR_COLLEGE_GRADUATION_DATE = s"$vendor.600023"
  val FM_EMAIL_USERNAME_CONTAINS_NAME = s"$vendor.600026"
  val FM_EMAIL_CONTAINS_DOB_CONFIDENCE = s"$vendor.600038"
  val EMAIL_SUBDOMAIN_POSSIBLY_TYPO = s"$vendor.600039"
  val EMAIL_TLD_POSSIBLY_TYPO = s"$vendor.600040"
  val FM_EMAIL_IS_FAKE_ADDRESS = s"$vendor.600075"
  val FM_EMAIL_IS_CORPORATE = s"$vendor.600076"
  val FM_EMAIL_IS_DISTRIBUTION_LIST = s"$vendor.600073"
  val FM_EMAIL_IS_SV4_FILLER = s"$vendor.600074"
  val FM_EMAIL_IS_SMALL_BUSINESS = s"$vendor.600072"

  val FM_EMAIL_HANDLE_ENTROPY = s"$vendor.600077"
  val FM_EMAIL_HANDLE_NORM_ENTROPY = s"$vendor.600078"
  val FM_EMAIL_HANDLE_REDUNDANCY_SCORE = s"$vendor.600079"
  val FM_EMAIL_HANDLE_UPPERCASE_ENTROPY = s"$vendor.600080"
  val FM_EMAIL_HANDLE_LOWERCASE_ENTROPY = s"$vendor.600081"
  val FM_EMAIL_HANDLE_DIGITS_ENTROPY = s"$vendor.600082"
  val FM_EMAIL_HANDLE_SPECIAL_CHARS_ENTROPY = s"$vendor.600083"
  val FM_EMAIL_HANDLE_CHARACTER_DIVERSITY_RATIO = s"$vendor.600084"
  val FM_EMAIL_HANDLE_DIGITS_LETTERS_RATIO = s"$vendor.600085"
  val FM_EMAIL_HANDLE_UPPERCASE_LOWERCASE_RATIO = s"$vendor.600086"

  val FM_FIRSTNAME_PERPLEXITY_RAW = s"$vendor.300115"
  val FM_FIRSTNAME_PERPLEXITY_NORMALIZED = s"$vendor.300116"
  val FM_SURNAME_PERPLEXITY_RAW = s"$vendor.300117"
  val FM_SURNAME_PERPLEXITY_NORMALIZED = s"$vendor.300118"
  val FM_FULLNAME_PERPLEXITY_RAW = s"$vendor.300119"
  val FM_FULLNAME_PERPLEXITY_NORMALIZED = s"$vendor.300120"

  val EXACT = "Exact"
  val ONE = "One"
  val TWOPLUS = "Two+"

  private val EMAIL_FILLER_COMMON_PATTERN = s"$vendor.710001"
  private val EMAIL_POSSIBLE_FILLER = s"$vendor.700001"

  private val FM_SMARTY_STORE_LATITUDE = s"$vendor.900027"
  private val FM_SMARTY_STORE_LONGITUDE = s"$vendor.900028"
  private val FM_INPUT_ZIP_TO_STORE_ZIP_DISTANCE_IN_MILES = s"$vendor.900003"
  private val HOME_TO_STORE_ADDRESS_DISTANCE = s"$vendor.300025"
  private val FM_INPUT_PHONE_TO_STORE_ZIP_DISTANCE_IN_MILES = s"$vendor.900004"
  private val FM_INPUT_ZIP_TO_PHONE_DISTANCE_IN_MILES = s"$vendor.900005"
  private val HOME_TO_BILLING_ADDRESS_DISTANCE = s"$vendor.300023"
  private val HOME_TO_SHIPPING_ADDRESS_DISTANCE = s"$vendor.300024"
  private val BILLING_TO_SHIPPING_ADDRESS_DISTANCE = s"$vendor.300026"
  private val BILLING_TO_STORE_ADDRESS_DISTANCE = s"$vendor.300027"
  private val SHIPPING_TO_STORE_ADDRESS_DISTANCE = s"$vendor.300028"
  private val FM_PHONE_TOLL_NUMBER = s"$vendor.300040"
  private val FM_PHONE_FIRST_AREA_CODE_DIGIT_NOT_2TO9 = s"$vendor.300041"
  private val FM_PHONE_CENTRAL_OFFICE_CODE_DIGIT_NOT_2TO9 = s"$vendor.300042"
  private val FM_PHONE_NUMBER_OF_CONSECUTIVE_DIGITS = s"$vendor.300043"
  private val FM_PHONE_NUMBER_OF_UNIQUE_DIGITS = s"$vendor.300044"
  private val FM_PHONE_US_OR_CANADA = s"$vendor.300046"
  private val FM_PHONE_INVALID = s"$vendor.300050"
  private val FM_PREV_ORDER_COUNT = s"$vendor.400003"
  private val FM_PREV_UNPAID_ORDER_COUNT = s"$vendor.400013"
  private val FM_ORDER_AMOUNT = s"$vendor.400005"

  private val FM_NUMBER_DAYS_SINCE_LAST_ORDER = s"$vendor.300015"
  private val FM_NUMBER_DAYS_SINCE_ACCOUNT_CREATION_DATE = s"$vendor.300016"
  private val FM_SUBMISSIONDATE_IS_US_BUSINESS_DAY = s"$vendor.900011"
  private val FM_SUBMISSIONDATE_IS_US_HOLIDAY = s"$vendor.900012"
  private val FM_INPUT_STATE_TO_STORE_STATE_MATCH = s"$vendor.900001"
  private val FM_INPUT_ZIP_TO_STORE_ZIP_MATCH = s"$vendor.900002"
  private val FM_NUMBER_OF_CONSECUTIVE_DIGITS_IN_INPUT_ADDRESS = s"$vendor.900016"
  private val FM_MAX_LENGTH_OF_REPEATING_IN_INPUT_ADDRESS = s"$vendor.900017"
  private val FM_EMAIL_MAX_LENGTH_OF_REPEATING_CHARACTER_IN_HANDLE = s"$vendor.300014"
  private val FM_EMAIL_POPULAR_DOMAIN = s"$vendor.300007"
  private val FM_EMAIL_TOP_DOMAIN = s"$vendor.300008"
  private val FM_EMAIL_FREE_DOMAIN = s"$vendor.300009"
  private val FM_EMAIL_RISKY_DOMAIN = s"$vendor.300010"
  private val FM_EMAIL_TLD_NOT_IN_IANA = s"$vendor.300011"
  private val FM_EMAIL_TLD_IS_COUNTRY_CODE = s"$vendor.300012"
  private val FM_EMAIL_TLD_IS_US = s"$vendor.300013"
  private val FM_EMAIL_RISKY_TOP_LEVEL_DOMAIN = s"$vendor.300034"
  private val FM_EMAIL_TLD_VS_FREE_TLD_LEVENSHTEIN_SCORE = s"$vendor.300032"
  private val FIRSTNAME_IS_LIKELY_NON_PERSON = s"$vendor.300084"
  private val SURNAME_IS_LIKELY_NON_PERSON = s"$vendor.300085"
  private val POSSIBLE_DUAL_APPLICANT = s"$vendor.300086"
  private val FM_DOB_AGE_LESS_16_OR_MORE_100 = s"$vendor.300067"
  private val FM_DOB_AGE_LESS_13_OR_MORE_120 = s"$vendor.300068"
  private val FM_AGE_YEARS = s"$vendor.300667"
  private val FM_AGE_YEARS_ADJ = s"$vendor.300668"
  private val FM_AGE_13_15 = s"$vendor.300670"
  private val FM_AGE_16_17 = s"$vendor.300671"
  private val FM_AGE_18_20 = s"$vendor.300672"
  private val FM_AGE_21_OVER = s"$vendor.300673"
  private val FM_DOB_IS_FUTURE_DATE = s"$vendor.300669"
  private val FM_DOB_NOT_VALID_YEAR = s"$vendor.300069"
  private val FM_PHONE_NOT_ALLOWED_FOR_CONSUMERS = s"$vendor.300047"
  private val FM_WIKI_ATHLETE = s"$vendor.900032"
  private val FM_WIKI_ATHLETE_RETIRED = s"$vendor.900033"
  private val FM_PRE_PROCESSED_PHYSICAL_ADDRESS = s"$vendor.900018"
  private val FM_PHONE_NANP_STATE = s"$vendor.100146"
  private val FM_PHONE_COUNTRY = s"$vendor.300049"
  private val FM_PHONE_AREA_CODE = s"$vendor.300048"
  private val FM_INPUT_STATE = s"$vendor.100100"
  private val FM_PHONE_LINE_TYPE = s"$vendor.300051"
  private val FM_EMAL_DOMAIN = s"$vendor.300001"
  private val FM_EMAIL_TLD = s"$vendor.300035"
  private val FM_EMAIL_TLD_TYPE = s"$vendor.300036"
  private val FM_ORDER_CHANNEL = s"$vendor.400001"
  private val FM_LAST_ORDER_DATE = s"$vendor.400002"
  private val FM_ACCOUNT_CREATION_DATE = s"$vendor.400004"
  private val FM_SUBMISSION_DATE = s"$vendor.400006"
  private val FM_SUBMISSIONDATE_DAY_OF_WEEK = s"$vendor.900010"
  private val FM_GENDER = s"$vendor.100151"
  private val FM_EMAIL_DOMAIN_VS_FREE_DOMAINS_LEVENSHTEIN_SCORE = s"$vendor.300030"
  private val FM_EMAIL_DOMAIN_VS_FREE_DOMAINS_LEVENSHTEIN_CAT = s"$vendor.300031"
  private val FM_PAYMENTS_RECIPIENT_COUNTRY = s"$vendor.400007"
  private val FM_PAYMENTS_PAYMENT_TYPE = s"$vendor.400008"
  private val FM_PAYMENTS_DISBURSEMENT_TYPE = s"$vendor.400009"
  private val FM_DEVICE_OPERATING_SYSTEM = s"$vendor.400012"
  private val FM_DEVICE_TYPE = s"$vendor.400010"
  private val FM_DEVICE_INTERFACE = s"$vendor.400011"
  private val FM_WIKI_ATHLETE_ASSOCIATED_SPORT_NAME = s"$vendor.900037"
  private val FM_WIKI_ATHLETE_ASSOCIATED_SPORT_LEAGUE = s"$vendor.900035"
  private val FM_WIKI_ATHLETE_SOURCE_ATTRIBUTION_LINKS = s"$vendor.900036"
  private val FM_EMAIL_TLD_VS_FREE_TLD_LEVENSHTEIN_CAT = s"$vendor.300033"
  private val FM_ADDRESS_REQUIRED_PREPROCESSING = s"$vendor.900019"
  private val PHONE_NO_DIFF_COUNTRY = s"$vendor.100143"
  private val FM_PHONE_STATE_MATCHES_INPUT_STATE = s"$vendor.300045"
  private val FM_PHONE_IS_PREMIUM = s"$vendor.300052"
  private val FM_PHONE_COUNTRY_CODE_OFAC_SANCTIONED_COUNTRY = s"$vendor.900038"
  private val EMAIL_TLD_OFAC_SANCTIONED_COUNTRY = s"$vendor.600071"
  private val FM_SMARTY_STD_DELIVERY_LINE1 = s"$vendor.900020"
  private val FM_SMARTY_STD_CITY = s"$vendor.900021"
  private val FM_SMARTY_STD_STATE = s"$vendor.900022"
  private val FM_SMARTY_STD_ZIP = s"$vendor.900023"
  private val FM_SMARTY_STD_ZIP4 = s"$vendor.900024"
  private val FM_SMARTY_LATITUDE = s"$vendor.900025"
  private val FM_SMARTY_LONGITUDE = s"$vendor.900026"
  private val FM_INPUT_LATLONG_TO_STORE_LATLONG_DISTANCE_IN_MILES = s"$vendor.900029"
  private val FM_MISSING_FIRST_NAME = s"$vendor.500111"
  private val FM_MISSING_PROVIDER = s"$vendor.500112"
  private val FM_FIRST_NAME_SYNTACTICALLY_VALID = s"$vendor.300017"
  private val FM_LAST_NAME_SYNTACTICALLY_VALID = s"$vendor.300018"
  private val FM_STATE_TWO_CHAR = s"$vendor.500101"
  private val FM_COUNTRY_ISO_STD = s"$vendor.500102"
  private val FM_STATE_ONLY_IN_US = s"$vendor.500103"
  private val FM_ADDRESS_NEED_ATLEAST_ZIP = s"$vendor.500104"
  private val FM_ADDRESS_NEED_ZIP = s"$vendor.500105"
  private val FM_COUNTRY_CODE_NEED = s"$vendor.500106"
  private val FM_ = s"$vendor.500107"
  private val FM_IP_MEET_IP_STD = s"$vendor.500108"
  private val FM_GEOCODE_INVALID = s"$vendor.500109"
  private val FM_SUPPORT_PHONE_FORMAT = s"$vendor.500110"
  private val FM_INVALID_DRIVING_LICENSE = s"$vendor.500117"
  private val FM_INVALID_NATIONAL_ID = s"$vendor.500118"
  private val FM_ADDRESS_PROVIDED = s"$vendor.300021"
  private val FM_FULL_US_ADDRESS_PROVIDED = s"$vendor.300022"
  private val PHONE_NO_FAKE = s"$vendor.100142"
  private val PHONE_NO_UNMATCH = s"$vendor.100144"
  private val AGE_YEAR = s"$vendor.300666"
  private val FM_EMAIL_LENGTH = s"$vendor.900006"
  private val FM_PROPORTION_OF_SYMBOLS_IN_EMAIL = s"$vendor.900007"
  private val FM_ADDRESS_IS_FILLER = s"$vendor.300122"
  private val FAKE_PHYSICAL_ADDRESS= s"$vendor.100140"

  private val FM_NUMBER_OF_SUBDOMAINS= s"$vendor.600087"
  private val FM_NUMBER_OF_DIGITS_IN_EMAIL_DOMAIN= s"$vendor.600088"
  private val FM_NUM_SPECIAL_CHARS_EMAIL_DOMAIN= s"$vendor.600089"
  private val FM_RATIO_VOWELS_CONSONANTS_EMAIL_DOMAIN= s"$vendor.600090"

  private val FM_EMAIL_DOMAIN_ENTROPY= s"$vendor.600091"
  private val FM_EMAIL_DOMAIN_CONTAINS_HOMOGRAPH= s"$vendor.600092"
  private val FM_EMAIL_DOMAIN_TYPOSQUATTING_DETECTION= s"$vendor.600093"
  private val FM_EMAIL_DOMAIN_REPETITIVE_CHARACTERS= s"$vendor.600094"
  private val FM_EMAIL_DOMAIN_GENERATED_ALGORITHMICALLY= s"$vendor.600095"

  private val FM_PII_EMAIL_DOMAIN= s"$vendor.600051"

  private val TrigramFileName = "trigrams"
  private val BigramFileName = "bigrams"
  private val trigramProbability = "trigramProbability"
  private val fortune500Cleaned = "fortune_500_cleaned"
  private val DomainTypo = "domain_typo"
  private val ssnIssueYearLookup = "ssnIssueYearLookup"
  private val validAddressProbabilities = "validAddressProbabilities"
  private val addressPerplexityQuantileMap = "address_perplexity_raw_to_norm"
  private val ssnPerplexityTrigramProbability = "ssnPerplexityTrigramProbability"
  private val ssnPerplexityQuantileMap = "ssnPerplexityQuantileMap"
  private val randomSsnPerplexityTrigramProbability = "randomSsnPerplexityTrigramProbability"
  private val randomSsnPerplexityQuantileMap = "randomSsnPerplexityQuantileMap"

  private val recognizedDomains = Set(
    "gmail.com",
    "yahoo.com",
    "aol.com",
    "hotmail.com"
  )
  private val otherDomains = "Other"

  private val top10Domains = Seq("gmail", "yahoo", "icloud", "hotmail", "aol", "outlook", "comcast", "live", "msn", "ymail", "mail", "email")
  private val commonTld = Seq("com", "net", "edu", "org", "appleid.com", "seis.com", "rr.com", "me", "gov", "biz", "info", "att.net", "usbank.com", "midco.net")
  private val swapTypos = Seq("ocm", "cmo", "ent", "nte", "deu", "eud", "rog", "ogr", "papleid.com",
    "aplpeid.com", "appelid.com", "applied.com", "appledi.com", "applei.dcom",
    "appleidc.om", "appleid.ocm", "appleid.cmo", "esis.com", "sies.com", "sesi.com",
    "sei.scom", "seisc.om", "seis.ocm", "seis.cmo", "r.rcom", "rrc.om", "rr.ocm", "rr.cmo",
    "em", "ogv", "gvo", "ibz", "bzi", "nifo", "ifno", "inof", "tat.net", "at.tnet", "attn.et",
    "att.ent", "att.nte", "subank.com", "ubsank.com", "usabnk.com", "usbnak.com", "usbakn.com",
    "usban.kcom", "usbankc.om", "usbank.ocm", "usbank.cmo", "imdco.net", "mdico.net",
    "micdo.net", "midoc.net", "midc.onet", "midcon.et", "midco.ent", "midco.nte")
  private val ssnPocketbookList = Seq("*********", "*********", "*********", "*********", "*********", "*********",
    "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********",
    "*********", "*********", "*********", "*********", "*********", "*********", "*********", "*********")
  private val emailFillers = Seq("email", "123", "non", "xxx.xxx", "atest", "no", "x", "noemail", "abc.abc", "fakeuser",
    "unknown", "offlineorder", "xxx", "xx", "refused", "nomail", "abe", "fakeusername", "notset", "xxxx", "none",
    "noname", "test", "null", "abc", "na", "theunknown", "testdata",
    "unknown")
  private val distributionList = Seq("^sales@.+$", "^info@.+$", "^question[s]?@.+$", "^frontdesk@.+$", "contact@.+$", "contactus@.+$", ".*support@.+$",
    "^customerservice@.+$", "^inquiries@.+$", "^billing@.+$", "^hr@.+$", "^admin@.+$", "^marketing@.+$", "^feedback@.+$",
    "^partnership[s]?@.+$", "^partner[s]?@.+$", "^media@.+$", "^careers@.+$", "^pr@.+$", "^public@.+$", "^publicrelations@.+$",
    "^events@.+$", "^qualityassurance@.+$", "^management@.+$", "^mgmt@.+$", "^recruitment*@.+$", "^suggestions@.+$",
    "^clientservices@.+$", "^compliance@.+$", "^investorrelations@.+$", "^.*noreply.*@.+$", "^.*mailbox.*@.+$")

  private val emailDomainFillers = Seq("arpa.com", "example.com", "invalid.com",
    "local.com", "onion.com", "test.com")

  private val emailUsernameFillers = Seq("non", "no", "x", "xxx", "xx", "abe", "abc", "na",
    "xxx", "atest", "noemail", "abcabc", "fakeuser", "unknown", "offlineorder",
    "refused", "nomail", "fakeusername", "notset", "xxxx", "none", "noname",
    "test", "null", "theunknown", "testdata", "unknown", "socuretest", "johndoe",
    "rogerplotz667", "jsmith", "any", "all", "everyone", "no-one", "noreply", "none",
    "employees", "empty", "no-email", "user", "account", "daemon", "admin",
    "user", "support", "postmaster", "info", "sales", "services", "faq", "questions", "example")

  private val smallBusinessPatterns = Seq("^.*cleaning.*@.+$", "^.*services.*@.+$", "^.*llc@.+$", "^.*corp@.+$",
    "^.*corporation@.+$", "^.*production[s]?@.+$", "^.*solution[s]?@.+$", "^.*ltd@.+$", "^.*group@.+$", "^.*grp@.+$",
    "^.*incorporated@.+$", "^.*inc@.+$", "^.*limited@.+$", "^.*consulting@.+$", "^.*consultant[s]?@.+$", "^.*advisory@.+$",
    "^.*construction@.+$", "^.*contractor[s]?@.+$", "^.*advisor[s]?@.+$", "^.*law@.+$", "^.*associate[s]?@.+$", "^.*medical@.+$",
    "^.*medicine@.+$", "^.*andsons@.+$", "^.*dentist[ry]*@.+$", "^.*andco@.+$", "^.*hvac@.+$", "^.*lawn@.+$", "^.*hardware@.+$",
    "^.*roof[ing]*@.+$", "^.*paint[ingers]+@.+$", "^.*company@.+$", "^.*financ[eial]+@.+$", "^.*development@.+$", "^.*legal@.+$",
    "^.*design@.+$", "^.*architect[s]?@.+$", "^.*offices@.+$", "^.*supply@.+$", "^.*garden@.+$", "^.*heating@.+$", "^.*cooling@.+$",
    "^.*plumbing@.+$", "^.*plumber[s]?@.+$", "^.*landscap*@.+$", "^.*global@.+$", "^.*society@.+$", "^.*shop@.+$", "^.*accountants@.+$",
    "^.*studio[s]?@.+$", "^.*boutique@.+$", "^.*books@.+$", "^.*cafe@.+$", "^.*catering@.+$", "^.*handyman@.+$", "^.*salon@.+$",
    "^.*renovation@.+$", "^.*wedding@.+$", "^.*planning@.+$", "^.*event@.+$", "^.*coordinator@.+$", "^.*tutor[sing]*@.+$",
    "^.*grooming@.+$", "^.*auto@.+$", "^.*repair@.+$", "^.*photography@.+$", "^.*management@.+$", "^.*bookkeep[ersing]?@.+$",
    "^.*partners@.+$", "^.+management@.+$")

  val FMVAL_300683 = s"$vendor.300683"
  val FMVAL_300684 = s"$vendor.300684"

  val EMAIL_FRAUD_RISK_EXACT_MATCH = s"$vendor.300686"
  val EMAIL_FRAUD_RISK_FUZZY_MATCH = s"$vendor.300687"

  private val emailIntelligenceRuleCode: Map[String, List[String]] = Map(
    "malicious_words" -> List(EMAIL_FRAUD_RISK_EXACT_MATCH, EMAIL_FRAUD_RISK_FUZZY_MATCH)
  )

  implicit val exe = ExecutionContext.Implicits.global
  implicit val formats = org.json4s.DefaultFormats

  override def generate(
                         ruleCodeResponse: RuleCodeResponse,
                         requestJsonAST: JsonAST.JValue,
                         dbJsonMap: Map[String, JsonAST.JValue],
                         errorResponseMap: Map[String, ErrorResponse],
                         ruleCodeDataFileLookup: RulecodeDataFileLookup
                       ): RuleCodeResponse = {

    implicit val trxId = TrxId.apply(Json4sUtility.getOptionalString(requestJsonAST, "transactionId").getOrElse(""))

    val emailUserNameRulecodes = try {
      getEmailUserName(requestJsonAST) match {
        case Some(emailUserName) if emailUserName.nonEmpty => generateRuleCodes(
          Seq(computeFmval600016, computeFmval600017, computeFmval600022, computeFmval300091, computeFmval300092),
          ruleCodeResponse,
          emailUserName,
          dbJsonMap,
          ruleCodeDataFileLookup
        )
        case _ => ruleCodeResponse
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }

    val nameRelatedRulecodes = try {
      generateNamePerplexityRulecodes(emailUserNameRulecodes, ruleCodeDataFileLookup, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for Name specific rulecode: ", exception)
        emailUserNameRulecodes
    }

    val phoneNumberRulecodes = try {
      generatePhoneSpecificRulecodes(nameRelatedRulecodes, ruleCodeDataFileLookup, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        nameRelatedRulecodes
    }

    val result600026 = try {
      computeFmval600026(phoneNumberRulecodes, requestJsonAST, ruleCodeDataFileLookup)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_IP_IS_INTERNAL : ", exception)
        phoneNumberRulecodes
    }

    val result500120 = try {
      computeFmval500120(result600026, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_IP_IS_INTERNAL : ", exception)
        result600026
    }

    val result300058 = try {
      computeFmval300058(result500120, requestJsonAST)
    } catch {
      case e: JsonFieldNotFoundException =>
        logger.debug(s"JsonFieldNotFoundException while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED :", e.getMessage)
        result500120
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED : ", exception)
        result500120
    }

    val result300002 = try {
      computeFmval300002(result300058, requestJsonAST,FM_EMAIL_CONTAIN_FIRST_NAME)
    } catch {
      case e: JsonFieldNotFoundException =>
        logger.debug(s"JsonFieldNotFoundException while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED :", e.getMessage)
        result300058
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED : ", exception)
        result300058
    }

    val result300003 = try {
      computeFmval300003(result300002, requestJsonAST,FM_EMAIL_CONTAIN_SURNAME)
    } catch {
      case e: JsonFieldNotFoundException =>
        logger.debug(s"JsonFieldNotFoundException while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED :", e.getMessage)
        result300002
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED : ", exception)
        result300002
    }

    val result300056 = try {
      computeFmval300056(result300003, requestJsonAST,FM_EMAIL_CONTAIN_FIRST_NAME,FM_EMAIL_FIRSTNAME_FUZZY_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_FIRSTNAME_FUZZY_MATCH : ", exception)
        result300003
    }

    val result300057 = try {
      computeFmval300057(result300056, requestJsonAST,FM_EMAIL_CONTAIN_SURNAME,FM_EMAIL_SURNAME_FUZZY_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_SURNAME_FUZZY_MATCH : ", exception)
        result300056
    }

    val result300054 = try {
      computeFmval300054(result300057, dbJsonMap, requestJsonAST,FM_EMAIL_CONTAIN_FIRST_NAME,FM_EMAIL_CONTAINS_FIRSTNAME_NICKNAME)
    } catch {
      case e: JsonFieldNotFoundException =>
        logger.debug(s"JsonFieldNotFoundException while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED :", e.getMessage)
        result300057
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED : ", exception)
        result300057
    }

    val result300055 = try {
      computeFmval300055(result300054, dbJsonMap, requestJsonAST,FM_EMAIL_CONTAIN_SURNAME,FM_EMAIL_CONTAINS_SURNAME_NICKNAME)
    } catch {
      case e: JsonFieldNotFoundException =>
        logger.debug(s"JsonFieldNotFoundException while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED :", e.getMessage)
        result300054
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_TUMBLED : ", exception)
        result300054
    }

    val result300061 = try {
      computeFmval300061(result300055,FM_EMAIL_CONTAIN_FIRST_NAME,FM_EMAIL_CONTAINS_FIRSTNAME_NICKNAME,FM_EMAIL_FIRSTNAME_FUZZY_MATCH,EMAIL_HAS_A_FIRSTNAME_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $EMAIL_HAS_A_FIRSTNAME_MATCH : ", exception)
        result300055
    }

    val result300062 = try {
      computeFmval300062(result300061,FM_EMAIL_CONTAIN_SURNAME,FM_EMAIL_CONTAINS_SURNAME_NICKNAME,FM_EMAIL_SURNAME_FUZZY_MATCH,EMAIL_HAS_A_SURNAME_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $EMAIL_HAS_A_SURNAME_MATCH : ", exception)
        result300061
    }

    val result600004 = try {
      computeFmval600004(result300062, requestJsonAST, ruleCodeDataFileLookup)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_DOMAIN_IS_FORTUNE_500_COMPANY : ", exception)
        result300062
    }

    val result600005 = try {
      computeFmval600005(result600004, requestJsonAST, ruleCodeDataFileLookup)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_FORTUNE_500_COMPANY_RANKING : ", exception)
        result600004
    }

    val result600038 = try {
      computeFmval600038(result600005, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_CONTAINS_DOB_CONFIDENCE : ", exception)
        result600005
    }

    val result600039 = try {
      computeFmval600039(result600038, requestJsonAST, ruleCodeDataFileLookup)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $EMAIL_SUBDOMAIN_POSSIBLY_TYPO : ", exception)
        result600038
    }

    val result600040 = try {
      computeFmval600040(result600039, requestJsonAST, ruleCodeDataFileLookup)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $EMAIL_TLD_POSSIBLY_TYPO : ", exception)
        result600039
    }

    val result300674 = try {
      computeFmval300674(result600040, requestJsonAST, ruleCodeDataFileLookup,FM_SSN_ISSUE_STATE_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_ISSUE_STATE_MATCH : ", exception)
        result600040
    }

    val result300675 = try {
      computeFmval300675(result300674, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_LAST4_CONSECUTIVE_DIGITS : ", exception)
        result300674
    }

    val result300676 = try {
      computeFmval300676(result300675, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_LAST4_YOB_MATCH : ", exception)
        result300675
    }

    val result300677 = try {
      computeFmval300677(result300676, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_LAST4_REPEATED_DIGITS : ", exception)
        result300676
    }

    val result300678 = try {
      computeFmval300678(result300677, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_AREACODE_BETWEEN_800_899 : ", exception)
        result300677
    }

    val result300679 = try {
      computeFmval300679(result300678, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_IS_IN_POCKETBOOK_LIST : ", exception)
        result300678
    }

    val result300680 = try {
      computeFmval300680(result300679, requestJsonAST, ruleCodeDataFileLookup,FM_SSN_ISSUEYEAR_YOB_GAP)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_ISSUEYEAR_YOB_GAP : ", exception)
        result300679
    }

    val result300681 = try {
      computeFmval300681(result300680, requestJsonAST, ruleCodeDataFileLookup,FM_SSN_ISSUEYEAR_YOB_ANOMALY_LT_1990)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_ISSUEYEAR_YOB_ANOMALY_LT_1990 : ", exception)
        result300680
    }

    val result300682 = try {
      computeFmval300682(result300681, requestJsonAST, ruleCodeDataFileLookup,FM_SSN_ISSUEYEAR_YOB_ANOMALY_GTE_1990)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_ISSUEYEAR_YOB_ANOMALY_GTE_1990 : ", exception)
        result300681
    }

    val result710001 = try {
      computeFmval710001(result300682, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $EMAIL_FILLER_COMMON_PATTERN : ", exception)
        result300682
    }

    val result700001 = try {
      computeFmval700001(result710001)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $EMAIL_POSSIBLE_FILLER : ", exception)
        result710001
    }

    val result400037 = try {
      computeFmval400037(result700001, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating rulecodes derived from $FM_EMAIL_USERNAME_CONTAINS_NAME : ", exception)
        result700001
    }

    val result400038 = try {
      computeFmval400038(result400037, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating rulecodes derived from $FM_EMAIL_USERNAME_CONTAINS_NAME : ", exception)
        result400037
    }

    val result400039 = try {
      computeFmval400039(result400038, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating rulecodes derived from $FM_EMAIL_USERNAME_CONTAINS_NAME : ", exception)
        result400038
    }

    // SSN perplexity rulecode codes
    val result300110 = try {
      getNationalId(requestJsonAST) match {
        case Some(nationalId) if nationalId.trim.nonEmpty =>
          generateSsnPerplexityRuleCodes(
            result400039,
            nationalId,
            dbJsonMap,
            ruleCodeDataFileLookup
          )
        case _ => result400039
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived ssn perplexity rulecodes : ", exception)
        result400039
    }

    val result300113 = try {
      computeFmval300113(result300110, requestJsonAST, ruleCodeDataFileLookup)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating rulecodes derived from $FM_PHYSICALADDRESS_PERPLEXITY_RAW : ", exception)
        result300110
    }

    val result300114 = try {
      computeFmval300114(result300113, requestJsonAST, ruleCodeDataFileLookup)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating rulecodes derived from $FM_PHYSICALADDRESS_PERPLEXITY_NORMALIZED : ", exception)
        result300113
    }

    val result300683 = try {
      computeFmval300683(result300114, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating rulecodes for vendor $FMVAL_300683 : ", exception)
        result300114
    }
    val result300684 = try {
      computeFmval300684(result300683, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating rulecodes for vendor $FMVAL_300684 : ", exception)
        result300683
    }
    val emailRiskRuleCodes = try {
      getOptionalEmailFromRequest(requestJsonAST) match {
        case Some(email) => generateRuleCodes(
          Seq(computeFmval600075, computeFmval600076, computeFmval600073, computeFmval600074, computeFmval600072),
          result300684,
          email,
          dbJsonMap,
          ruleCodeDataFileLookup
        )
        case _ => result300684
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived email risk rulecodes for vendor $vendor : ", exception)
        result300684
    }
    val idPlusRuleCodes = try {
      computeIdPlusRuleCodes(emailRiskRuleCodes, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating rulecodes for idplus : ", exception)
        emailRiskRuleCodes
    }
    val entropyRuleCodes = try {
      getEmailHandle(requestJsonAST) match {
        case Some(email) => generateRuleCodes(
          Seq(computeFmval600077, computeFmval600078, computeFmval600079, computeFmval600080,computeFmval600081,
             computeFmval600082, computeFmval600083, computeFmval600084, computeFmval600085, computeFmval600086),
          idPlusRuleCodes,
          email,
          dbJsonMap,
          ruleCodeDataFileLookup
        )
        case _ => idPlusRuleCodes
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating entropy rulecodes : ", exception)
        idPlusRuleCodes
    }
    val emailIntelligenceRuleCodes = try {
      computeEmailIntelligenceRules(entropyRuleCodes, requestJsonAST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating rulecodes for email intelligence : ", exception)
        entropyRuleCodes
    }

    try {
      getEmailDomain(requestJsonAST) match {
        case Some(emailDomain) => generateRuleCodes(
          Seq(computeFmval600087,computeFmval600088,computeFmval600089,computeFmval600090,
            computeFmval600091,computeFmval600092,computeFmval600093,computeFmval600094,computeFmval600095
          ),
          emailIntelligenceRuleCodes,
          emailDomain,
          dbJsonMap,
          ruleCodeDataFileLookup
        )
        case _ => emailIntelligenceRuleCodes
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating domainBasedRuleCodes rulecodes : ", exception)
        emailIntelligenceRuleCodes
    }

  }

  private def generateRuleCodes(
                                 generators: Seq[DerivedRuleCodeGenerator],
                                 ruleCodeResponse: RuleCodeResponse,
                                 emailUserName: String,
                                 dbJsonMap: Map[String, JsonAST.JValue],
                                 ruleCodeDataFileLookup: RulecodeDataFileLookup
                               ): RuleCodeResponse = {
    generators.foldLeft(ruleCodeResponse)((r, f) => f(r, emailUserName, dbJsonMap, ruleCodeDataFileLookup))
  }

  def generateSsnPerplexityRuleCodes(ruleCodeResponse: RuleCodeResponse,
                                     nationalId: String,
                                     dbJsonMap: Map[String, JsonAST.JValue],
                                     ruleCodeDataFileLookup: RulecodeDataFileLookup
                                    ): RuleCodeResponse = {
    generateRuleCodes(
      Seq(computeFmval300107, computeFmval300108, computeFmval300109, computeFmval300110),
      ruleCodeResponse,
      nationalId,
      dbJsonMap,
      ruleCodeDataFileLookup
    )
  }

  /**
   * Check to see whether or not the billing email handle contains a name,
   * if so check to see if there is a mismatch between the name in the billing email handle and the billing full name
   * 1. If billing first name, billing last name, billing email are missing or FMVAL.600026 != 1, return null
   * 2. If billing first name and billing last name are not in the billing email, or have a low match ratio,
   * and FMVAL.600026 = 1, return 1
   * 3. Return 0 for all others
   */
  private def computeFmval400037(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val FM_BILLING_FIRST_NAME_PCT_FUZZY_BILLING_EMAIL = s"$vendor.400018"
    val FM_BILLING_LAST_NAME_FUZZY_PCT_BILLING_EMAIL = s"$vendor.400022"
    val fmval600026 = getNumericalMatchResult(FM_EMAIL_USERNAME_CONTAINS_NAME, ruleCodeResponse)
    val fmval400018 = getNumericalMatchResult(FM_BILLING_FIRST_NAME_PCT_FUZZY_BILLING_EMAIL, ruleCodeResponse)
    val fmval400022 = getNumericalMatchResult(FM_BILLING_LAST_NAME_FUZZY_PCT_BILLING_EMAIL, ruleCodeResponse)
    val billingFirstName = getInputName(requestJsonAST, "orderDetails.billingFirstName")
    val billingSurname = getInputName(requestJsonAST, "orderDetails.billingSurName")
    val billingEmailHandle = getInputName(requestJsonAST, "orderDetails.billingEmailHandle")
    val result = (billingFirstName, billingSurname, billingEmailHandle, fmval400018, fmval400022, fmval600026) match {
      case (Some(billingFirstName), Some(billingSurname), Some(billingEmailHandle), Some(fmval400018), Some(fmval400022), Some(fmval600026)) => {
        val billingFirstNameNotInsideBillingEmailHandle = !billingEmailHandle.contains(billingFirstName)
        val billingSurNameNotInsideBillingEmailHandle = !billingEmailHandle.contains(billingSurname)
        if (fmval600026 != 1) {
          None
        }
        else if (billingFirstNameNotInsideBillingEmailHandle &&
          billingSurNameNotInsideBillingEmailHandle &&
          fmval400018 < 0.65 && fmval400022 < 0.65) {
          Some(1)
        }
        else {
          Some(0)
        }
      }
      case _ => None
    }
    result match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_BILLING_FULL_NAME_MISMATCH_BILLING_EMAIL, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  /**
   * Check to see whether or not the shipping email handle contains a name,
   * if so check to see if there is a mismatch between the name in the shipping email handle and the billing full name
   * 1. If billing first name, billing last name, shipping email are missing or FMVAL.600026 != 1, return null
   * 2. If billing first name and billing last name are not in the shipping email, or have a low match ratio, and FMVAL.600026 = 1, return 1
   * 3. Return 0 for all others
   */
  private def computeFmval400038(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val rulecodeName = s"$vendor.400038"
    val FM_BILLING_FIRST_NAME_FUZZY_PCT_SHIPPING_EMAIL = s"$vendor.400019"
    val FM_BILLING_LAST_NAME_FUZZY_PCT_SHIPPING_EMAIL = s"$vendor.400023"
    val fmval600026 = getNumericalMatchResult(FM_EMAIL_USERNAME_CONTAINS_NAME, ruleCodeResponse)
    val fmval400019 = getNumericalMatchResult(FM_BILLING_FIRST_NAME_FUZZY_PCT_SHIPPING_EMAIL, ruleCodeResponse)
    val fmval400023 = getNumericalMatchResult(FM_BILLING_LAST_NAME_FUZZY_PCT_SHIPPING_EMAIL, ruleCodeResponse)
    val billingFirstName = getInputName(requestJsonAST, "orderDetails.billingFirstName")
    val billingSurname = getInputName(requestJsonAST, "orderDetails.billingSurName")
    val shippingEmailHandle = getInputName(requestJsonAST, "shippingDetails.shippingEmailHandle")
    val result = (billingFirstName, billingSurname, shippingEmailHandle, fmval400019, fmval400023, fmval600026) match {
      case (Some(billingFirstName), Some(billingSurname), Some(shippingEmailHandle), Some(fmval400019), Some(fmval400023), Some(fmval600026)) => {
        val billingFirstNameNotInsideShippingEmailHandle = !shippingEmailHandle.contains(billingFirstName)
        val billingSurNameNotInsideShippingEmailHandle = !shippingEmailHandle.contains(billingSurname)
        if (fmval600026 != 1) {
          None
        }
        else if (billingFirstNameNotInsideShippingEmailHandle &&
          billingSurNameNotInsideShippingEmailHandle &&
          fmval400019 < 0.65 && fmval400023 < 0.65) {
          Some(1)
        }
        else {
          Some(0)
        }
      }
      case _ => None
    }
    result match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(rulecodeName, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  /**
   * Check to see whether or not the shipping email handle contains a name,
   * if so check to see if there is a mismatch between the name in the shipping email handle and the shipping full name
   * 1. If shipping first name, shipping last name, shipping email are missing or FMVAL.600026 != 1, return null
   * 2. If shipping first name and shipping last name are not in the shipping email, or have a low match ratio, and FMVAL.600026 = 1, return 1
   * 3. Return 0 for all others
   */
  private def computeFmval400039(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val rulecodeName = s"$vendor.400039"
    val FM_SHIPPING_FIRST_NAME_PCT_FUZZY_SHIPPING_EMAIL = s"$vendor.400049"
    val FM_SHIPPING_LAST_NAME_PCT_FUZZY_SHIPPING_EMAIL = s"$vendor.400055"
    val fmval600026 = getNumericalMatchResult(FM_EMAIL_USERNAME_CONTAINS_NAME, ruleCodeResponse)
    val fmval400049 = getNumericalMatchResult(FM_SHIPPING_FIRST_NAME_PCT_FUZZY_SHIPPING_EMAIL, ruleCodeResponse)
    val fmval400055 = getNumericalMatchResult(FM_SHIPPING_LAST_NAME_PCT_FUZZY_SHIPPING_EMAIL, ruleCodeResponse)
    val billingFirstName = getInputName(requestJsonAST, "shippingDetails.shippingFirstName")
    val billingSurname = getInputName(requestJsonAST, "shippingDetails.shippingSurName")
    val shippingEmailHandle = getInputName(requestJsonAST, "shippingDetails.shippingEmailHandle")
    val result = (billingFirstName, billingSurname, shippingEmailHandle, fmval400049, fmval400055, fmval600026) match {
      case (Some(billingFirstName), Some(billingSurname), Some(shippingEmailHandle), Some(fmval400049), Some(fmval400055), Some(fmval600026)) => {
        val billingFirstNameNotInsideShippingEmailHandle = !shippingEmailHandle.contains(billingFirstName)
        val billingSurNameNotInsideShippingEmailHandle = !shippingEmailHandle.contains(billingSurname)
        if (fmval600026 != 1) {
          None
        }
        else if (billingFirstNameNotInsideShippingEmailHandle &&
          billingSurNameNotInsideShippingEmailHandle &&
          fmval400049 < 0.65 && fmval400055 < 0.65) {
          Some(1)
        }
        else {
          Some(0)
        }
      }
      case _ => None
    }
    result match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(rulecodeName, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private val computeFmval600016: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailUserName, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    val score = computeNGram(emailUserName, 3, TrigramFileName, ruleCodeDataFileLookup)
    score.map(d => RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes,
      ruleCodeResponse.categoricalRuleCodes ++ Seq(CategoricalRuleCode(FM_EMAIL_TRIGRAM_MEDIAN_FREQUENCIES_IN_ALIAS, d.toString)),
      ruleCodeResponse.errors
    )).getOrElse(ruleCodeResponse)
  }

  private val computeFmval600017: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailUserName, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    val score = computeNGram(emailUserName, 2, BigramFileName, ruleCodeDataFileLookup)
    score.map(d => RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes,
      ruleCodeResponse.categoricalRuleCodes ++ Seq(CategoricalRuleCode(FM_EMAIL_BIGRAM_MEDIAN_FREQUENCIES_IN_ALIAS, d.toString)),
      ruleCodeResponse.errors
    )).getOrElse(ruleCodeResponse)
  }

  private val computeFmval600022: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, _, _, _) => {
    val trigramFreq = getMatchResult(FM_EMAIL_TRIGRAM_MEDIAN_FREQUENCIES_IN_ALIAS, ruleCodeResponse)
    val bigramFreq = getMatchResult(FM_EMAIL_BIGRAM_MEDIAN_FREQUENCIES_IN_ALIAS, ruleCodeResponse)
    val scrambledScore = math.sqrt(trigramFreq * bigramFreq)
    RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes,
      ruleCodeResponse.categoricalRuleCodes ++ Seq(CategoricalRuleCode(FM_EMAIL_USERNAME_STRING_SCRAMBLED_SCORE, scrambledScore.toString)),
      ruleCodeResponse.errors
    )
  }

  def computeFmval300056(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue,fmval300002:String,resultRuleCode:String): RuleCodeResponse = {
    computeFMVal30056And300057Common(ruleCodeResponse, requestJsonAST, fmval300002, "first_name") match {
      case Some(n) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, n)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case None => ruleCodeResponse
    }

  }

  def computeFmval300057(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue,fmval300003:String,resultRuleCode:String): RuleCodeResponse = {
    computeFMVal30056And300057Common(ruleCodeResponse, requestJsonAST, fmval300003, "last_name") match {
      case Some(n) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, n)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case None => ruleCodeResponse
    }
  }

   def computeFmval300061(ruleCodeResponse: RuleCodeResponse,fmval_300002:String,fmval_300054:String,fmval_300056:String,resultRuleCode:String): RuleCodeResponse = {
    val fmval300002 = getNumericalMatchResult(fmval_300002, ruleCodeResponse)
    val fmval300054 = getNumericalMatchResult(fmval_300054, ruleCodeResponse)
    val fmval300056 = getNumericalMatchResult(fmval_300056, ruleCodeResponse)
    val result = fmval300002 match {
      case Some(n) => if (n == 1 || fmval300054.getOrElse(0) == 1 || fmval300056.getOrElse(0) == 1) Some(1) else Some(0)
      case None => None
    }
    result match {
      case Some(n) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, n)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case None => ruleCodeResponse
    }
  }

   def computeFmval300062(ruleCodeResponse: RuleCodeResponse,fmval_300003:String,fmval_300055:String,fmval_300057:String,resultRuleCode:String): RuleCodeResponse = {
    val fmval300003 = getNumericalMatchResult(fmval_300003, ruleCodeResponse)
    val fmval300055 = getNumericalMatchResult(fmval_300055, ruleCodeResponse)
    val fmval300057 = getNumericalMatchResult(fmval_300057, ruleCodeResponse)
    val result = fmval300003 match {
      case Some(n) => if (n == 1 || fmval300055.getOrElse(0) == 1 || fmval300057.getOrElse(0) == 1) Some(1) else Some(0)
      case None => None
    }
    result match {
      case Some(n) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, n)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case None => ruleCodeResponse
    }
  }

  def computeFMVal30056And300057Common(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dependantFMVal: String, inputFieldNamePath: String): Option[Double] = {
    val fmvalDep = getNumericalMatchResult(dependantFMVal, ruleCodeResponse)
    fmvalDep match {
      case Some(n) => if (n == 1) Some(0) else {
        val emailUsername = getEmailUserNameWithGmailHandling(requestJsonAST).getOrElse("")
        val names = getInputName(requestJsonAST, inputFieldNamePath).getOrElse("").split(" ")
        val nGramEmailCache: scala.collection.mutable.Map[Int, List[String]] = scala.collection.mutable.Map.empty
        for (name <- names) {
          val nGramSize = if (name.length < 10) 4 else 5
          if (!nGramEmailCache.contains(nGramSize)) nGramEmailCache.put(nGramSize, getNGram(emailUsername, nGramSize))
          val listOfEmailUserNames = nGramEmailCache.get(nGramSize)
          val listOfInputNames = getNGram(name, nGramSize)
          if (listOfEmailUserNames.exists(n => listOfInputNames.exists(n.contains))) return Some(1)
        }
        Some(0)
      }
      case None => None
    }
  }

  private def computeFmval500120(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val numericalRuleCodes = (Json4sUtility.getOptionalString(requestJsonAST, "ip") match {
      case Some(x) if x.contains(":") && x.contains(".") => Some(0.0)
      case Some(x) if x.contains(".") => Some(validateIpv4(x))
      case Some(x) if x.contains(":") => Some(validateIpv6(x))
      case _ => None
    }) match {
      case Some(x) => ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_IP_IS_INTERNAL, x))
      case _ => ruleCodeResponse.numericalRuleCodes
    }

    RuleCodeResponse(
      numericalRuleCodes,
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )
  }

  private val computeFmval300091: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailUserName, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    if (emailUserName.length < 3) {
      ruleCodeResponse
    } else {
      val score = computeNGramProbability(emailUserName, 3, trigramProbability, ruleCodeDataFileLookup)
      score.map(_ => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_HANDLE_PERPLEXITY_RAW, score.getOrElse(0))),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )).getOrElse(ruleCodeResponse)
    }
  }

  private def generateNamePerplexityRulecodes(ruleCodeResponse: RuleCodeResponse, ruleCodeDataFileLookup: RulecodeDataFileLookup, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val firstName = getCleanedName(requestJsonAST, "first_name")
    val firstNameRulecodes = firstName.length < 3 match {
      case false =>
        val firstNameInput = NGramGenerationInput(firstName, "firstname_perplexity_trigram_probabilities",
          FM_FIRSTNAME_PERPLEXITY_RAW, "firstname_perplexity_raw_to_norm", FM_FIRSTNAME_PERPLEXITY_NORMALIZED, 7, 4)
        generate3GramRawAndNormalizesRC(firstNameInput, ruleCodeDataFileLookup)
      case true => Seq.empty[NumericalRuleCode]
    }

    val surName = getCleanedName(requestJsonAST, "last_name")
    val surNameRulecodes = surName.length < 3 match {
      case false =>
        val surNameInput = NGramGenerationInput(surName, "surname_perplexity_trigram_probabilities", FM_SURNAME_PERPLEXITY_RAW,
          "surname_perplexity_raw_to_norm", FM_SURNAME_PERPLEXITY_NORMALIZED, 7, 4)
        generate3GramRawAndNormalizesRC(surNameInput, ruleCodeDataFileLookup)
      case true => Seq.empty[NumericalRuleCode]
    }

    val fullName = firstName + surName
    val fullNameRulecodes = fullName.length < 3 match {
      case false =>
        val fullNameInput = NGramGenerationInput(fullName, "fullname_perplexity_trigram_probabilities", FM_FULLNAME_PERPLEXITY_RAW,
          "fullname_perplexity_raw_to_norm", FM_FULLNAME_PERPLEXITY_NORMALIZED, 7, 4)
        generate3GramRawAndNormalizesRC(fullNameInput, ruleCodeDataFileLookup)
      case true => Seq.empty[NumericalRuleCode]
    }

    RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ firstNameRulecodes ++ surNameRulecodes ++ fullNameRulecodes,
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )
  }

  private def generatePhoneSpecificRulecodes(ruleCodeResponse: RuleCodeResponse, ruleCodeDataFileLookup: RulecodeDataFileLookup, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val phoneNumber = getPhoneNumber(requestJsonAST).getOrElse("")
    val generatedNumericalRuleCodes = phoneNumber.length < 10 match {
      case false =>
        val phoneInput = NGramGenerationInput(phoneNumber, "phone_perplexity_trigram_probabilities",
          FM_PHONE_PERPLEXITY_RAW, "phone_perplexity_raw_to_norm", FM_PHONE_PERPLEXITY_NORM, 3, 3)
        generate3GramRawAndNormalizesRC(phoneInput, ruleCodeDataFileLookup)
      case true => Seq.empty[NumericalRuleCode]
    }

    RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ generatedNumericalRuleCodes,
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )
  }

  private def generate3GramRawAndNormalizesRC(input: NGramGenerationInput, ruleCodeDataFileLookup: RulecodeDataFileLookup): Seq[NumericalRuleCode] = {
    val rawScore: Double = computeNGramProbability(input.pii, 3, input.rawFileName, ruleCodeDataFileLookup, input.rawDecimalPrecision).getOrElse(0)
    ruleCodeDataFileLookup.getPerplexityQuantileRangeMapWithKey(input.normFileName, rawScore) match {
      case Some(normalizedScore) =>
        val roundedNormScore = MatcherUtility.roundUpScore(normalizedScore, input.normDecimalPrecision)
        Seq(NumericalRuleCode(input.rawRuleCode, rawScore), NumericalRuleCode(input.normalizedRuleCode, roundedNormScore))
      case None => Seq(NumericalRuleCode(input.rawRuleCode, rawScore))
    }
  }

  private def computeFmval300113(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, ruleCodeDataFileLookup: RulecodeDataFileLookup): RuleCodeResponse = {
    val fullAddressCleaned = getFullAddressCleaned(requestJsonAST)
    if (fullAddressCleaned.length < 3) {
      ruleCodeResponse
    } else {
      val score = computeNGramProbability(fullAddressCleaned, 3, validAddressProbabilities, ruleCodeDataFileLookup, decimalPoints = 6)
      score.map(_ => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_PHYSICALADDRESS_PERPLEXITY_RAW, score.getOrElse(0))),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )).getOrElse(ruleCodeResponse)
    }
  }

  private def computeFmval600004(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup): RuleCodeResponse = {
    val score = Json4sUtility.getOptionalString(requestJsonAST, "raw.emailDomain") match {
      case Some(d) =>
        val cleanDomain = d.split("\\.")(0)
        rulecodeDataFileLookup.get(fortune500Cleaned) match {
          case Some(fileMap) =>
            Some(if (fileMap.keySet.contains(cleanDomain)) 1 else 0)
          case _ => None
        }
      case _ => None
    }
    val response = score.map(_ => RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_DOMAIN_IS_FORTUNE_500_COMPANY, score.get)),
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )).getOrElse(ruleCodeResponse)
    response
  }

  private def computeFmval600005(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup): RuleCodeResponse = {
    val score = Json4sUtility.getOptionalString(requestJsonAST, "raw.emailDomain") match {
      case Some(d) =>
        val cleanDomain = d.split("\\.")(0)
        rulecodeDataFileLookup.get(fortune500Cleaned) match {
          case Some(fileMap) =>
            if (fileMap.keySet.contains(cleanDomain)) Some((fileMap(cleanDomain) \ "rank").values.toString.toDouble) else None
          case _ => None
        }
      case _ => None
    }
    score.map(_ => RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_FORTUNE_500_COMPANY_RANKING, score.get)),
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )).getOrElse(ruleCodeResponse)
  }

  private val computeFmval300092: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailUserName, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    if (emailUserName.length < 3) {
      ruleCodeResponse
    } else {
      val emailHandlePerplexityRaw = getNumericalMatchResult(FM_EMAIL_HANDLE_PERPLEXITY_RAW, ruleCodeResponse).getOrElse(0.0)
      val double = MatcherUtility.roundUpScore(ruleCodeDataFileLookup.getPerplexity(emailHandlePerplexityRaw), 3)
      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_HANDLE_PERPLEXITY_NORMALIZED, double)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
    }
  }

  private def computeFmval300114(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, ruleCodeDataFileLookup: RulecodeDataFileLookup): RuleCodeResponse = {
    val addressPerplexityRaw = getNumericalMatchResult(FM_PHYSICALADDRESS_PERPLEXITY_RAW, ruleCodeResponse).getOrElse(0.0)
    ruleCodeDataFileLookup.getPerplexityQuantileRangeMapWithKey(addressPerplexityQuantileMap, addressPerplexityRaw) match {
      case Some(score) =>
        val addressPerplexityNorm = MatcherUtility.roundUpScore(score, 4)
        RuleCodeResponse(
          ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_PHYSICALADDRESS_PERPLEXITY_NORMALIZED, addressPerplexityNorm)),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case None =>
        ruleCodeResponse
    }
  }

  private def computeFmval600039(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup): RuleCodeResponse = {
    (Json4sUtility.getOptionalString(requestJsonAST, "raw.emailDomain") match {
      case Some(d) =>
        val cleanDomain = d.split("\\.")(0)
        if (top10Domains.map(a => MatcherUtility.levenshteinMatch(a, cleanDomain, true)).min == 1) Some(1)
        else {
          rulecodeDataFileLookup.get(DomainTypo) match {
            case Some(fileMap) if fileMap.keySet.contains(cleanDomain) => Some(1)
            case _ => None
          }
        }
      case _ => None
    }) match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(EMAIL_SUBDOMAIN_POSSIBLY_TYPO, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private def computeFmval600040(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup): RuleCodeResponse = {
    (Json4sUtility.getOptionalString(requestJsonAST, "raw.emailDomain") match {
      case Some(d) =>
        val tld = d.split("\\.", 2)
        if (tld.size == 2) {
          if (commonTld.map(a => MatcherUtility.levenshteinMatch(a, tld(1), true)).min == 1) Some(1)
          else if (swapTypos.contains(tld(1))) Some(1)
          else None
        } else None
      case _ => None
    }) match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(EMAIL_TLD_POSSIBLY_TYPO, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private def validateIpv4(ip: String): Double = {
    val ipParts = ip.split("\\.")
    if (ipParts.length == 4) {
      val part2 = try {
        ipParts(1).toInt
      } catch {
        case e: Exception =>
          logger.error(s"Exception occurred parsing IP $vendor : ", e)
          0
      }
      if (ipParts(0).equals("10") || (ipParts(0).equals("192") && ipParts(1).equals("168")) || (ipParts(0).equals("172") && part2 >= 16 && part2 <= 31)) 1.0
      else 0.0
    }
    else 0.0
  }

  private def validateIpv6(ip: String): Double = {
    val ipParts = ip.split(":")
    if (ipParts.length == 8 && ((ipParts(0).toLowerCase.startsWith("fc") && ipParts(0).length >= 4) || (ipParts(0).toLowerCase.startsWith("fd") && ipParts(0).length >= 4) || ipParts(0).equalsIgnoreCase("fe80") || ipParts(0).equalsIgnoreCase("100"))) 1.0
    else 0.0
  }

  private def getEmailUserName(requestJsonAST: JsonAST.JValue): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, "raw.email")
      .flatMap(_.split("@").headOption.map(_.trim.toLowerCase))
  }

  private def getNationalId(requestJsonAST: JsonAST.JValue): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, "raw.ssn")
      .map(_.replaceAll("[^0-9]", "").trim)
  }

  private def getPhoneNumber(requestJsonAST: JsonAST.JValue): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, "phone_number")
      .map(_.filter(_.isDigit).takeRight(10))
  }

  private def getEmailHandle(requestJsonAST: JsonAST.JValue): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, "raw.inputEmail")
      .orElse(Json4sUtility.getOptionalString(requestJsonAST, "raw.email"))
      .flatMap(_.split("@").headOption)
  }

  private def getEmailDomain(requestJsonAST: JsonAST.JValue): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, "raw.emailDomain")
  }


  private def getFullAddressCleaned(requestJsonAST: JsonAST.JValue): String = {
    val physicalAddress: String = Json4sUtility.getOptionalString(requestJsonAST, "raw.physicalAddress").map(_.trim.toLowerCase).getOrElse("")
    val physicalAddress2: String = Json4sUtility.getOptionalString(requestJsonAST, "raw.physicalAddress2").map(_.trim.toLowerCase).getOrElse("")
    removeNonUtf8Characters(physicalAddress + physicalAddress2)
  }

  private def getCleanedName(requestJsonAST: JsonAST.JValue, label: String): String = {
    Json4sUtility.getOptionalString(requestJsonAST, label).map(removeNonUtf8Characters(_).trim.toLowerCase).getOrElse("")
  }

  private def removeNonUtf8Characters(input: String): String = input.replaceAll("[^\\x00-\\x7F]|\\s", "")

  private def computeFmval300058(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    getEmailUserName(requestJsonAST) match {
      case Some(x) =>
        val dotOccurrences = x.split("\\.").length
        val plusOccurrences = x.split("\\+").length
        val plusFollowedByNumbers = "\\+\\d+$".r.findAllIn(x).toList.length > 0
        val multipleDotOrPlus = if (dotOccurrences > 2 || plusOccurrences > 2) true else false
        val numberPattern: Regex = "\\d+".r
        val numericSubsequencesWith000Prefix = numberPattern.findAllIn(x).toList.filter(s => s.startsWith("000")).length > 0
        val emailContainsDobYear = getNumericalMatchResult(FM_EMAIL_CONTAINS_DOB_YEAR, ruleCodeResponse).getOrElse(0.0)
        val emailContainsCollegeGraduationDate = getNumericalMatchResult(FM_EMAIL_CONTAINS_HS_OR_COLLEGE_GRADUATION_DATE, ruleCodeResponse).getOrElse(0.0)
        val score = if (emailContainsDobYear == 1.0 || emailContainsCollegeGraduationDate == 1.0) {
          0.0
        } else if (multipleDotOrPlus || numericSubsequencesWith000Prefix || plusFollowedByNumbers) {
          1.0
        } else {
          0.0
        }
        RuleCodeResponse(
          ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_IS_TUMBLED, score)),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )

      case _ => ruleCodeResponse
    }
  }

  private def computeFmval600038(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    getEmailUserName(requestJsonAST) match {
      case Some(s) =>
        Json4sUtility.getOptionalString(requestJsonAST, "raw.dobString") match {
          case Some(dob) =>
            val score = getDobInEmailScore(s, dob)
            val result = if (score.nonEmpty) score.get else 0.0
            RuleCodeResponse(
              ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_CONTAINS_DOB_CONFIDENCE, result)),
              ruleCodeResponse.categoricalRuleCodes,
              ruleCodeResponse.errors
            )
          case _ => ruleCodeResponse
        }

      case _ => ruleCodeResponse
    }
  }

   def computeFmval300674(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup,resultRuleCode:String): RuleCodeResponse = {
    val score: Option[Double] = getSsnFromInputRequest(requestJsonAST) match {
      case Some(ssn) =>
        val issueState = getSsnIssueState(ssn, rulecodeDataFileLookup)
        Json4sUtility.getOptionalString(requestJsonAST, "state") match {
          case Some(state) =>
            if (state.isEmpty || issueState.isEmpty) None
            else Some(if (state.equalsIgnoreCase(issueState.get)) 1.0 else 0.0)
          case _ => None
        }
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private def computeFmval300675(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val score = getSsnFromInputRequest(requestJsonAST) match {
      case Some(ssn) =>
        val digits = ssn.substring(ssn.length - 4).map(_.asDigit)
        if ((digits, digits.tail).zipped.forall((a, b) => b == a + 1) || (digits, digits.tail).zipped.forall((a, b) => b == a - 1))
          Some(1.0)
        else
          Some(0.0)
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_SSN_LAST4_CONSECUTIVE_DIGITS, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private def computeFmval300676(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val score = getSsnFromInputRequest(requestJsonAST) match {
      case Some(ssn) =>
        Json4sUtility.getOptionalString(requestJsonAST, "raw.dobString") match {
          case Some(dob) =>
            val year = dob.split("-")(0)
            val last4Digits = ssn.substring(ssn.length - 4)
            if (year.equals(last4Digits)) Some(1.0) else Some(0.0)
          case _ => None
        }
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_SSN_LAST4_YOB_MATCH, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private def computeFmval300677(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val score = getSsnFromInputRequest(requestJsonAST) match {
      case Some(ssn) =>
        val last4Digits = ssn.substring(ssn.length - 4)
        if (last4Digits.split("").toSet.size == 1) Some(1.0) else Some(0.0)
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_SSN_LAST4_REPEATED_DIGITS, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  def computeFmval300002(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue,resultRuleCode:String): RuleCodeResponse = {
    val result = Json4sUtility.getOptionalString(requestJsonAST, "raw.email") match {
      case Some(email) =>
        //extract domain
        val subDomainHandledUserName: String = extractsubDomainHandledUserName(email)
        val firstNameOpt = Json4sUtility.getOptionalString(requestJsonAST, "first_name")
        val cleanedFirstName = firstNameOpt
          .map(HelperAlgorithms.compositeStringTransformer(HelperAlgorithms.ToUpper, HelperAlgorithms.RemoveStopWords))
          .map(MatcherUtility.cleanNV3)
        (cleanedFirstName, subDomainHandledUserName) match {
          case (None, _) => None
          case (Some(firstName), emailUserName) =>
            //split first user name by space and filter length greater than 2
            val firstNameParts = firstName.trim.split(" ").map(_.trim).filter(_.length > 2).toSet
            if (firstName.trim.size == 0 || firstNameParts.isEmpty) None else if (firstNameParts.exists(firstNamePart => {
              emailUserName.contains(firstNamePart)
            })) Some(1.0) else Some(0.0)
          case _ => None
        }
      case _ => None
    }
    result match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  def computeFmval300003(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue,resultRuleCode:String): RuleCodeResponse = {
    val result = Json4sUtility.getOptionalString(requestJsonAST, "raw.email") match {
      case Some(email) =>
        //extract domain
        val subDomainHandledUserName: String = extractsubDomainHandledUserName(email)
        val cleanedLastName = Json4sUtility.getOptionalString(requestJsonAST, "last_name")
          .map(HelperAlgorithms.compositeStringTransformer(HelperAlgorithms.ToUpper, HelperAlgorithms.RemoveStopWords))
          .map(MatcherUtility.cleanNV3)
        (cleanedLastName, subDomainHandledUserName) match {
          case (None, _) => None
          case (Some(lastName), emailUserName) =>
            //split first user name by space and filter length greater than 2
            val lastNameParts = lastName.trim.split(" ").map(_.trim).filter(_.length > 2).toSet
            if (lastName.trim.length == 0 || lastNameParts.isEmpty) None
            else if (lastNameParts.exists(lastNamePart => emailUserName.contains(lastNamePart)))
              Some(1.0) else Some(0.0)
          case _ => None
        }
      case _ => None
    }
    result match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  def computeFmval300054(ruleCodeResponse: RuleCodeResponse, dbJsonMap: Map[String, JsonAST.JValue], requestJsonAST: JsonAST.JValue,fmval300002:String,resultRuleCode:String): RuleCodeResponse = {
    val v300002Opt = getNumericalMatchResult(fmval300002, ruleCodeResponse)
    val firstNameNickName = Json4sUtility.parseJValue(path = "table.fmval_nick_name_first_name_lookup.names", dbJsonMap = dbJsonMap, requestJsonAST = null)
    val emailOpt = Json4sUtility.getOptionalString(requestJsonAST, "raw.email")
    val score = (v300002Opt, firstNameNickName, emailOpt) match {
      case (Some(v300002), JArray(nickNames), Some(email)) => if (v300002 == 1.0) Some(0.0) else {
        val firstNameNickNames = nickNames.map(jVal => jVal match {
          case JString(name) => List(name).filterNot(s => s.equals(""))
          case JArray(names) => names.flatMap(_.extractOpt[String]).filterNot(s => s.equals(""))
        }).flatten
        val cleanedEmail = MatcherUtility.cleanNV3(extractsubDomainHandledUserName(email))
        if (firstNameNickNames.isEmpty) {
          None
        } else if (firstNameNickNames.exists(nickName => {
          cleanedEmail.contains(nickName)
        })) Some(1.0)
        else Some(0.0)
      }
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  def computeFmval300055(ruleCodeResponse: RuleCodeResponse, dbJsonMap: Map[String, JsonAST.JValue], requestJsonAST: JsonAST.JValue,fmval300003:String,resultRuleCode:String): RuleCodeResponse = {
    val v300002Opt = getNumericalMatchResult(fmval300003, ruleCodeResponse)
    val lastNameNickName = Json4sUtility.parseJValue(path = "table.fmval_nick_name_last_name_lookup.names", dbJsonMap = dbJsonMap, requestJsonAST = null)
    val emailOpt = Json4sUtility.getOptionalString(requestJsonAST, "raw.email")
    val score = (v300002Opt, lastNameNickName, emailOpt) match {
      case (Some(v300002), JArray(nicknames), Some(email)) => if (v300002 == 1.0) Some(0.0) else {
        val lastNameNickNames = nicknames.map(jVal => jVal match {
          case JString(name) => List(name).filterNot(s => s.equals(""))
          case JArray(names) => names.flatMap(_.extractOpt[String]).filterNot(s => s.equals(""))
        }).flatten
        val cleanedEmail = MatcherUtility.cleanNV3(extractsubDomainHandledUserName(email))
        if (lastNameNickNames.isEmpty) {
          None
        } else if (lastNameNickNames.exists(nickName => {
          cleanedEmail.contains(nickName)
        })) Some(1.0)
        else Some(0.0)
      }
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }
  def extractsubDomainHandledUserName(email: String) = {
    val (emailUserName: String, domain: String) = getEmailUsernameAndDomain(email)
    val gmailHandledUserName = getGmailUserName(domain, emailUserName)
    val subDomainHandledUserName = domain.split("\\.").head.trim.toLowerCase() match {
      case (subDomain) if (Set("me", "mac", "icloud", "runbox", "outlook",
        "fastmail", "hotmail", "msn", "live").contains(subDomain)) => gmailHandledUserName.split("\\+").head
      case (subDomain) if (Set("ymail", "yahoo").contains(subDomain)) => gmailHandledUserName.split("\\-").head
      case _ => gmailHandledUserName
    }
    if (subDomainHandledUserName == null) subDomainHandledUserName else subDomainHandledUserName.trim.toLowerCase()
  }

  private def computeFmval300678(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val score = getSsnFromInputRequest(requestJsonAST) match {
      case Some(ssn) =>
        val first3Digits = ssn.substring(0, 3).toInt
        Some(if (first3Digits >= 800 && first3Digits <= 899) 1.0 else 0.0)
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_SSN_AREACODE_BETWEEN_800_899, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private def computeFmval300679(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val score = getSsnFromInputRequest(requestJsonAST) match {
      case Some(ssn) =>
        Some(if (ssnPocketbookList.contains(ssn)) 1.0 else 0.0)
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_SSN_IS_IN_POCKETBOOK_LIST, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

   def computeFmval300680(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup,resultRuleCode:String): RuleCodeResponse = {
    val score = getSsnIssueDobYearDifference(requestJsonAST, rulecodeDataFileLookup)
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  def computeFmval300681(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup,resultRuleCode:String): RuleCodeResponse = {
    val score = computeFmval300681682Common(requestJsonAST, rulecodeDataFileLookup) match {
      case (Some(d), Some(y)) => Some(if (d > 18 && y.toInt < 1990) 1.0 else 0.0)
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

   def computeFmval300682(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup,resultRuleCode:String): RuleCodeResponse = {
    val score = computeFmval300681682Common(requestJsonAST, rulecodeDataFileLookup) match {
      case (Some(d), Some(y)) => Some(if (Math.abs(d) > 2 && y.toInt >= 1990) 1.0 else 0.0)
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(resultRuleCode, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  def computeFmval300681682Common(requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup): (Option[Int], Option[String]) = {
    getSsnIssueDobYearDifference(requestJsonAST, rulecodeDataFileLookup) match {
      case Some(d) =>
        getDobYearFromRequest(requestJsonAST) match {
          case Some(year) => (Some(d), Some(year))
          case _ => (Some(d), None)
        }
      case _ => (None, None)
    }
  }

  private def computeFmval710001(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val score = getEmailUserName(requestJsonAST) match {
      case Some(username) => Some(if (emailFillers.contains(username)) 1.0 else 0.0)
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(EMAIL_FILLER_COMMON_PATTERN, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private def computeFmval700001(ruleCodeResponse: RuleCodeResponse): RuleCodeResponse = {
    val fmval710001 = getNumericalMatchResult(EMAIL_FILLER_COMMON_PATTERN, ruleCodeResponse)
    val score = fmval710001 match {
      case Some(v) => if (v == 1.0) Some(1.0) else None
      case _ => None
    }
    score match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(EMAIL_POSSIBLE_FILLER, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private val computeFmval300107: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, nationalId, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    if (nationalId.length != 9) {
      ruleCodeResponse
    } else {
      val score = computeNGramProbability(nationalId, 3, ssnPerplexityTrigramProbability, ruleCodeDataFileLookup, 4)
      score.map(_ => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_NATIONALID_PERPLEXITY_RAW, score.getOrElse(0))),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )).getOrElse(ruleCodeResponse)
    }
  }

  private val computeFmval300108: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, nationalId, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    if (nationalId.length != 9) {
      ruleCodeResponse
    } else {
      val ssnPerplexityRaw = getNumericalMatchResult(FM_NATIONALID_PERPLEXITY_RAW, ruleCodeResponse).getOrElse(0.0)
      ruleCodeDataFileLookup.getPerplexityQuantileRangeMapWithKey(ssnPerplexityQuantileMap, ssnPerplexityRaw) match {
        case Some(score) =>
          val roundedScore = MatcherUtility.roundUpScore(score, 4)
          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_NATIONALID_PERPLEXITY_NORMALIZED, roundedScore)),
            ruleCodeResponse.categoricalRuleCodes,
            ruleCodeResponse.errors
          )
        case None =>
          ruleCodeResponse
      }
    }
  }

  private val computeFmval300109: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, nationalId, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    if (nationalId.length != 9) {
      ruleCodeResponse
    } else {
      val score = computeNGramProbability(nationalId, 3, randomSsnPerplexityTrigramProbability, ruleCodeDataFileLookup, 4)
      score.map(_ => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_RANDOM_NATIONALID_PERPLEXITY_RAW, score.getOrElse(0))),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )).getOrElse(ruleCodeResponse)
    }
  }

  private val computeFmval300110: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, nationalId, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    if (nationalId.length != 9) {
      ruleCodeResponse
    } else {
      val randomSsnPerplexityRaw = getNumericalMatchResult(FM_RANDOM_NATIONALID_PERPLEXITY_RAW, ruleCodeResponse).getOrElse(0.0)
      ruleCodeDataFileLookup.getPerplexityQuantileRangeMapWithKey(randomSsnPerplexityQuantileMap, randomSsnPerplexityRaw) match {
        case Some(score) =>
          val roundedScore = MatcherUtility.roundUpScore(score, 4)
          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_RANDOM_NATIONALID_PERPLEXITY_NORMALIZED, roundedScore)),
            ruleCodeResponse.categoricalRuleCodes,
            ruleCodeResponse.errors
          )
        case None =>
          ruleCodeResponse
      }
    }
  }

  private val computeFmval600075: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val isFakeEmail = ruleCodeDataFileLookup.getEmailRuleCodePatterns("fake_email_list") match {
        case Some(fakeEmailList) =>
          val username = email.split("@")(0).trim.toLowerCase()
          if (fakeEmailList.contains(username)) Some(1.0) else Some(0.0)
        case _ => None
      }
      constructReturnResponse(ruleCodeResponse, isFakeEmail, FM_EMAIL_IS_FAKE_ADDRESS)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_FAKE_ADDRESS : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600076: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val isPatternExist = ruleCodeDataFileLookup.getEmailRuleCodePatterns("corporate_address_list") match {
        case Some(corporateAddressList) =>
          val domain = email.split("@")(1).trim.toLowerCase()
          if (corporateAddressList.contains(domain)) Some(1.0) else Some(0.0)
        case _ => None
      }
      constructReturnResponse(ruleCodeResponse, isPatternExist, FM_EMAIL_IS_CORPORATE)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_CORPORATE : ", exception)
        ruleCodeResponse
    }

  }

  private def isPatternMatch(listOfPatterns: Seq[String], email: String): Option[Double] = {
    if (listOfPatterns.exists(pattern => email.matches(pattern))) Some(1.0) else Some(0.0)
  }

  private val computeFmval600072: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val isPatternExist = isPatternMatch(smallBusinessPatterns, email)
      constructReturnResponse(ruleCodeResponse, isPatternExist, FM_EMAIL_IS_SMALL_BUSINESS)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_SMALL_BUSINESS : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600077: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      EntropyCalculator.entropy(email) match {
        case Some(entropy) =>
          val roundedEntropy = MatcherUtility.roundUpScore(entropy, 4)
          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_HANDLE_ENTROPY, roundedEntropy)),
            ruleCodeResponse.categoricalRuleCodes,
            ruleCodeResponse.errors
          )
        case None =>
          ruleCodeResponse
      }
    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_HANDLE_ENTROPY : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600078: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
       getNumericalMatchResult(FM_EMAIL_HANDLE_ENTROPY, ruleCodeResponse) match {
        case Some(value) if value > 0 =>
          val normalizedEntropy = MatcherUtility.roundUpScore(value / EntropyCalculator.log2(value),4)
          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_HANDLE_NORM_ENTROPY, normalizedEntropy)),
            ruleCodeResponse.categoricalRuleCodes,
            ruleCodeResponse.errors
          )
        case _ =>  ruleCodeResponse
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_HANDLE_NORM_ENTROPY: ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600079: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
          // Compute maximum entropy based on the length of email handle
          val maxEntropy = EntropyCalculator.log2(email.length)
          getNumericalMatchResult(FM_EMAIL_HANDLE_ENTROPY, ruleCodeResponse) match {
            case Some(entropy) if entropy > 0 && maxEntropy > 0 =>
              val redundancyScore = math.max(0, 1 - (entropy / maxEntropy))
              RuleCodeResponse(
                ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_HANDLE_REDUNDANCY_SCORE,  MatcherUtility.roundUpScore(redundancyScore, 4))),
                ruleCodeResponse.categoricalRuleCodes,
                ruleCodeResponse.errors
              )
            case _ => ruleCodeResponse
          }
      } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_HANDLE_REDUNDANCY_SCORE: ", exception)
        ruleCodeResponse
    }
  }

  private def computeEntropy(ruleCodeResponse: RuleCodeResponse, email: String, pattern: String, ruleCode: String): RuleCodeResponse = {
    try {
      val chars = email.replaceAll(pattern, "")
      val entropyOpt = if (chars.nonEmpty) EntropyCalculator.entropy(chars) else None
      entropyOpt match {
        case Some(entropy) =>
          val roundedEntropy = MatcherUtility.roundUpScore(entropy, 4)
          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(ruleCode, roundedEntropy)),
            ruleCodeResponse.categoricalRuleCodes,
            ruleCodeResponse.errors
          )
        case None =>
          ruleCodeResponse
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $ruleCode: ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600080: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    computeEntropy(ruleCodeResponse, email, "[^A-Z]", FM_EMAIL_HANDLE_UPPERCASE_ENTROPY)
  }

  private val computeFmval600081: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    computeEntropy(ruleCodeResponse, email, "[^a-z]", FM_EMAIL_HANDLE_LOWERCASE_ENTROPY)
  }

  private val computeFmval600082: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    computeEntropy(ruleCodeResponse, email, "[^0-9]", FM_EMAIL_HANDLE_DIGITS_ENTROPY)
  }

  private val computeFmval600083: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    computeEntropy(ruleCodeResponse, email, "[a-zA-Z0-9]", FM_EMAIL_HANDLE_SPECIAL_CHARS_ENTROPY)
  }

  private def computeFmval600084: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val emailHandle = email.trim
      val diversityRatioOpt = if (emailHandle.nonEmpty) {
        val emailHandleLength = emailHandle.length
        val emailHandleCharArrLen = emailHandle.toCharArray.distinct.length
        Some(emailHandleCharArrLen.toDouble / emailHandleLength)
      } else {
        None
      }

      diversityRatioOpt match {
        case Some(diversityRatio) =>
          val roundedDiversityRatio = MatcherUtility.roundUpScore(diversityRatio, 4)
          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_HANDLE_CHARACTER_DIVERSITY_RATIO, roundedDiversityRatio)),
            ruleCodeResponse.categoricalRuleCodes,
            ruleCodeResponse.errors
          )
        case None =>
          ruleCodeResponse
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_HANDLE_CHARACTER_DIVERSITY_RATIO: ", exception)
        ruleCodeResponse
    }
  }

  private def computeRatio(ruleCodeResponse: RuleCodeResponse, email: String, pattern1: String, pattern2: String, ruleCode: String): RuleCodeResponse = {
    try {
      val emailHandle = email.trim
      val count1 = emailHandle.replaceAll(pattern1, "").length
      val count2 = emailHandle.replaceAll(pattern2, "").length

      val ratioOpt = if (count2 > 0) {
        Some(count1.toDouble / count2)
      } else {
        None
      }

      ratioOpt match {
        case Some(ratio) =>
          val roundedRatio = MatcherUtility.roundUpScore(ratio, 4)
          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(ruleCode, roundedRatio)),
            ruleCodeResponse.categoricalRuleCodes,
            ruleCodeResponse.errors
          )
        case None =>
          ruleCodeResponse
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Error computing ratio for rule code $ruleCode", exception)
        ruleCodeResponse
    }
  }

  private def computeFmval600085: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    computeRatio(ruleCodeResponse, email, "[^0-9]", "[^a-zA-Z]", FM_EMAIL_HANDLE_DIGITS_LETTERS_RATIO)
  }

  private def computeFmval600086: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    computeRatio(ruleCodeResponse, email, "[^A-Z]", "[^a-z]", FM_EMAIL_HANDLE_UPPERCASE_LOWERCASE_RATIO)
  }

  private val computeFmval600073: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val isPatternExist = isPatternMatch(distributionList, email)
      constructReturnResponse(ruleCodeResponse, isPatternExist, FM_EMAIL_IS_DISTRIBUTION_LIST)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_DISTRIBUTION_LIST : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600074: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, email, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val (emailUserName: String, domain: String) = getEmailUsernameAndDomain(email)
      val isPatternExist = Some(if (emailUsernameFillers.contains(emailUserName) || emailDomainFillers.contains(domain)) 1.0 else 0.0)
      constructReturnResponse(ruleCodeResponse, isPatternExist, FM_EMAIL_IS_SV4_FILLER)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_IS_SV4_FILLER : ", exception)
        ruleCodeResponse
    }

  }

  private val computeFmval600087: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailDomain, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
       val count = emailDomain.split("\\.").length
       val result = if(count > 2) count - 2 else 0
         RuleCodeResponse(
           ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_NUMBER_OF_SUBDOMAINS, result)),
           ruleCodeResponse.categoricalRuleCodes,
           ruleCodeResponse.errors
         )

    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for rulecode $FM_NUMBER_OF_SUBDOMAINS : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600088: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailDomain, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val digits = emailDomain.replaceAll("[^0-9]","").length
      val result = if(digits > 0) digits else 0
      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_NUMBER_OF_DIGITS_IN_EMAIL_DOMAIN, result)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )

    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for rulecode $FM_NUMBER_OF_DIGITS_IN_EMAIL_DOMAIN : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600089: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailDomain, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val domain = emailDomain.replaceAll("\\.(?=[^.]+$)","")
      val specialCharsCount = domain.replaceAll("[^\\W_]","").length
      val result = if(specialCharsCount > 0) specialCharsCount else 0
      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_NUM_SPECIAL_CHARS_EMAIL_DOMAIN, result)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )

    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for rulecode $FM_NUM_SPECIAL_CHARS_EMAIL_DOMAIN : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600090: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailDomain, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val domainClean  = emailDomain.replaceAll("[^a-zA-Z]","")
      val countVowels : Double = domainClean.replaceAll("[^aeiouAEIOU]","").length
      val countConsonants : Double= domainClean.length - countVowels
      if(countConsonants > 0) {
        RuleCodeResponse(
          ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_RATIO_VOWELS_CONSONANTS_EMAIL_DOMAIN, roundUpScore(countVowels/countConsonants,2))),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      }else ruleCodeResponse

    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for rulecode $FM_RATIO_VOWELS_CONSONANTS_EMAIL_DOMAIN : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600091: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailDomain, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      EntropyCalculator.entropy(emailDomain) match {
        case Some(entropy) =>
          val roundedEntropy = MatcherUtility.roundUpScore(entropy, 4)
          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_DOMAIN_ENTROPY, roundedEntropy)),
            ruleCodeResponse.categoricalRuleCodes,
            ruleCodeResponse.errors
          )
        case None =>
          ruleCodeResponse
      }
    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for rulecode $FM_EMAIL_DOMAIN_ENTROPY : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600092: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailDomain, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val result  = if( emailDomain.matches(".*(0|1|5|@|vv|rn|q|µ|¡|£|¢|§|©|Ⅱ|Ⅲ).*")
                        || emailDomain.matches(".*[^\\x00-\\x7F].*")
                        || emailDomain.matches("^xn--.*")
                      ) 1.0 else 0.0
        RuleCodeResponse(
          ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_DOMAIN_CONTAINS_HOMOGRAPH, result)),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for rulecode $FM_EMAIL_DOMAIN_CONTAINS_HOMOGRAPH : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600093: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailDomain, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val levenshteinDistance = popularDomains.flatMap(LevenshteinMetric.compare(_, emailDomain.toLowerCase)).min
      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_DOMAIN_TYPOSQUATTING_DETECTION, levenshteinDistance)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for rulecode $FM_EMAIL_DOMAIN_TYPOSQUATTING_DETECTION : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600094: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailDomain, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val repetitivePattern  = "(\\w)\\1{2,}".r
      val result =   repetitivePattern.findFirstIn(emailDomain) match {
        case Some(_) => 1.0
        case None    => 0.0
      }
      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_DOMAIN_REPETITIVE_CHARACTERS, result)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for rulecode $FM_EMAIL_DOMAIN_REPETITIVE_CHARACTERS : ", exception)
        ruleCodeResponse
    }
  }

  private val computeFmval600095: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, emailDomain, _, ruleCodeDataFileLookup: RulecodeDataFileLookup) => {
    try {
      val result  = if(emailDomain.matches("^[a-zA-Z0-9]{7,}\\.[a-z]{2,}$")) 1.0 else 0.0
      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_DOMAIN_GENERATED_ALGORITHMICALLY, result)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
    }catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for rulecode $FM_EMAIL_DOMAIN_GENERATED_ALGORITHMICALLY : ", exception)
        ruleCodeResponse
    }
  }

  private def constructReturnResponse(ruleCodeResponse: RuleCodeResponse, isPatternExist: Option[Double], ruleCodeType: String) = {
    isPatternExist match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(ruleCodeType, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private def computeFmval300683(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val emailMatcher = Json4sUtility.getOptionalString(requestJsonAST, "raw.email") match {
      case Some(email) => if (email.length == 35 && email.split("@")(1).toLowerCase().equalsIgnoreCase("privaterelay.appleid.com")) Some(1.0) else None
      case _ => None
    }
    emailMatcher.map(_ => RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FMVAL_300683, emailMatcher.get)),
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )).getOrElse(ruleCodeResponse)
  }

  private def computeFmval300684(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val emailMatcher = Json4sUtility.getOptionalString(requestJsonAST, "raw.email") match {
      case Some(email) => if (email.split("@")(1).toLowerCase().equalsIgnoreCase("icloud.com")) Some(1.0) else None
      case _ => None
    }
    emailMatcher.map(_ => RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FMVAL_300684, emailMatcher.get)),
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )).getOrElse(ruleCodeResponse)
  }


  private def getEmailUserNameWithGmailHandling(requestJsonAST: JsonAST.JValue): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, "raw.email") match {
      case Some(s) =>
        val (emailUserName: String, domain: String) = getEmailUsernameAndDomain(s)
        val username: String = getGmailUserName(domain, emailUserName)
        Some(cleanString(username.trim.toLowerCase))
      case None => None
    }
  }

  private def getOptionalEmailFromRequest(requestJsonAST: JsonAST.JValue): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, "raw.email") match {
      case Some(email) => Some(email.trim.toLowerCase)
      case None => None
    }
  }

  private def getEmailUsernameAndDomain(s: String) = {
    val emailParts = s.split("@")
    val domain = if (emailParts.length > 1) emailParts(1) else ""
    val emailUserName = emailParts(0)
    (emailUserName, domain)
  }

  private def getGmailUserName(domain: String, emailUserName: String) = {
    val username = if (domain.equalsIgnoreCase("gmail.com"))
      emailUserName.split("\\+").apply(0).replaceAll("\\.", "")
    else
      emailUserName
    username
  }

  private def getInputName(requestJsonAST: JsonAST.JValue, inputFieldNamePath: String): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, inputFieldNamePath) match {
      case Some(s) => Some(cleanString(s))
      case None => None
    }
  }

  private def getMatchResult(ruleName: String, ruleCodeResponse: RuleCodeResponse): Double = {
    ruleCodeResponse.categoricalRuleCodes.find(r => ruleName.equals(r.name)).map(_.value.toDouble).getOrElse(0.0)
  }

  private def getCategoricalMatchResult(ruleName: String, ruleCodeResponse: RuleCodeResponse): Option[String] = {
    ruleCodeResponse.categoricalRuleCodes.find(r => ruleName.equals(r.name)).map(_.value)
  }

  def getNumericalMatchResult(ruleName: String, ruleCodeResponse: RuleCodeResponse): Option[Double] = {
    ruleCodeResponse.numericalRuleCodes.find(r => ruleName.equals(r.name)).map(_.value)
  }

  def computeNGram(str: String, n: Int, fileName: String, rulecodeDataFileLookup: RulecodeDataFileLookup): Option[Double] = {
    rulecodeDataFileLookup.get(fileName) match {
      case Some(fileMap) =>
        val nGrams = getNGram(str, n)
        if (nGrams.nonEmpty) {
          val freqList = nGrams.flatMap {
            word =>
              fileMap.get(word) match {
                case Some(jVal) => Json4sUtility.getOptionalString(jVal, "freq").map(_.toDouble)
                case _ => None
              }
          }.sortWith(_ < _)
          val (lower, upper) = freqList.splitAt(freqList.size / 2)
          if (lower.nonEmpty && upper.nonEmpty) {
            val median = if (freqList.size % 2 == 0) (lower.last + upper.head) / 2.0 else upper.head
            Some(median)
          } else if (lower.nonEmpty) {
            Some(lower.last)
          } else if (upper.nonEmpty) {
            Some(upper.head)
          } else Some(0.0)
        } else None
      case _ => None
    }
  }

  def computeNGramProbability(str: String, n: Int, fileName: String, rulecodeDataFileLookup: RulecodeDataFileLookup, decimalPoints: Int = 3): Option[Double] = {
    rulecodeDataFileLookup.get(fileName) match {
      case Some(fileMap) =>
        val nGrams = getNGramRaw(str, n)
        var logSum: Double = 0.0
        val result: Double = if (nGrams.nonEmpty) {
          for (word <- nGrams) {
            val probability: Double = fileMap.get(word) match {
              case Some(v) => v(0).extractOpt[Double].get
              case _ => 0.0000000001
            }
            logSum += Math.log(probability) / Math.log(2)
          }
          logSum
        } else 0.0
        val perplexity = Math.pow(2, -result / nGrams.size)
        Some(MatcherUtility.roundUpScore(perplexity, decimalPoints))
      case _ => None
    }
  }

  def getNGram(str: String, n: Int): List[String] = {
    str.trim.toLowerCase.sliding(n).filter(_.matches(s"[a-z]{$n}")).toList
  }

  def getNGramRaw(str: String, n: Int): List[String] = {
    str.trim.toLowerCase.sliding(n).toList
  }

  private def getDobInEmailScore(_emailUsername: String, userDob: String): Option[Double] = {
    if (_emailUsername.isEmpty || userDob.isEmpty) return None
    var emailUsername = _emailUsername
    val userDobParts = userDob.split("-")
    val (year, month, date) = (userDobParts(0), userDobParts(1), userDobParts(2))
    var (isYearPresent, isMonthPresent, isDatePresent) = (false, false, false)

    val (year4, year2) = year match {
      case y if y.length == 4 => (Some(y), Some(y.substring(2)))
      case y if y.length == 3 => (None, Some(y.substring(1)))
      case y if y.length == 2 => (None, Some(y))
      case _ => (None, None)
    }

    val (month2, month1) = month match {
      case m if m.length == 2 && m.startsWith("0") => (Some(m), Some(m.substring(1)))
      case m if m.length == 2 => (Some(m), None)
      case m if m.length == 1 => (None, Some(m))
      case _ => (None, None)
    }

    val (date2, date1) = date match {
      case d if d.length == 2 && d.startsWith("0") => (Some(d), Some(d.substring(1)))
      case d if d.length == 2 => (Some(d), None)
      case d if d.length == 1 => (None, Some(d))
      case _ => (None, None)
    }

    if (year4.nonEmpty && emailUsername.contains(year4.get)) {
      isYearPresent = true
      emailUsername = emailUsername.replaceFirst(year4.get, "")
    } else if (year2.nonEmpty && emailUsername.contains(year2.get)) {
      isYearPresent = true
      emailUsername = emailUsername.replaceFirst(year2.get, "")
    }

    if (month2.nonEmpty && emailUsername.contains(month2.get)) {
      isMonthPresent = true
      emailUsername = emailUsername.replaceFirst(month2.get, "")
    }
    if (date2.nonEmpty && emailUsername.contains(date2.get)) {
      isDatePresent = true
      emailUsername = emailUsername.replaceFirst(date, "")
    }

    if (!isMonthPresent && month1.nonEmpty && emailUsername.contains(month1.get)) {
      isMonthPresent = true
      emailUsername = emailUsername.replaceFirst(month1.get, "")
    }
    if (!isDatePresent && date1.nonEmpty && emailUsername.contains(date1.get)) {
      isDatePresent = true
      emailUsername = emailUsername.replaceFirst(date1.get, "")
    }

    (isYearPresent, isMonthPresent, isDatePresent) match {
      case (a, b, c) if a && b && c => Some(5)
      case (a, b, c) if (a && b) || (a && c) => Some(4)
      case (_, b, c) if b && c => Some(3)
      case (a, _, _) if a => Some(2)
      case (_, b, c) if b || c => Some(1)
      case _ => Some(0)
    }
  }

  def getSsnFromInputRequest(requestJsonAST: JsonAST.JValue): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, "ssn") match {
      case Some(ssn) =>
        val s = ssn.replaceAll("-", "")
        if (s.length == 9) Some(s) else None
      case _ => None
    }
  }

  def getSsnIssueState(ssn: String, rulecodeDataFileLookup: RulecodeDataFileLookup): Option[String] = {
    if (ssn.isEmpty || ssn.length != 9) return None

    val ssnFirst3 = ssn.substring(0, 3)
    val ssnFirst5 = ssn.substring(0, 5)

    if (ssnFirst5.equals("23230")) Some("NC")
    else if ((580 <= ssnFirst3.toInt) && (584 >= ssnFirst3.toInt)) Some("PR")
    else if (ssnFirst3.equals("586")) Some("PR")
    else {
      val lookupValue = rulecodeDataFileLookup.getSsnState(ssnFirst3.toDouble)
      val ssnStateFileData = if (lookupValue != JNothing) lookupValue \ "state_code" else JNothing
      if (ssnStateFileData != JNothing) Some(ssnStateFileData.extractOpt[String].get) else None
    }
  }

  def getSsnIssueDobYearDifference(requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup): Option[Int] = {
    getSsnFromInputRequest(requestJsonAST) match {
      case Some(ssn) =>
        val first5Digits = ssn.substring(0, 5)
        getSsnIssuedYear(first5Digits, rulecodeDataFileLookup) match {
          case Some(issuedYear) =>
            getDobYearFromRequest(requestJsonAST) match {
              case Some(dob) =>
                val dobYear = dob.split("-")(0)
                Some(issuedYear - dobYear.toInt)
              case _ => None
            }
          case _ => None
        }
      case _ => None
    }
  }

  private def getSsnIssuedYear(first5Digits: String, rulecodeDataFileLookup: RulecodeDataFileLookup): Option[Int] = {
    rulecodeDataFileLookup.get(ssnIssueYearLookup, first5Digits) match {
      case Some(fileMap) => fileMap(0) match {
        case JInt(v) => Some(v.toInt)
        case _ => None
      }
      case _ => None
    }
  }

  private def getDobYearFromRequest(requestJsonAST: JsonAST.JValue): Option[String] = {
    Json4sUtility.getOptionalString(requestJsonAST, "raw.dobString") match {
      case Some(dob) => Some(dob.split("-")(0))
      case _ => None
    }
  }

  private def applyFuzzyMatch(dataSetMap: collection.mutable.Map[String, Set[String]], email: String, emailSubStr: String): Boolean = {
    val considerSet = dataSetMap.get(emailSubStr)
    considerSet match {
      case Some(x) =>
        x.foreach {
          str =>
            val aClean = cleanN(str)
            val bClean = cleanN(email)
            if (aClean.isEmpty || bClean.isEmpty) {
              return false
            } else if (aClean == bClean) {
              return true
            } else if (aClean.length >= 3 && bClean.contains(aClean)) {
              return true
            } else if (bClean.length >= 3 && aClean.contains(bClean)) {
              return true
            } else if (aClean.length >= 3 && aClean.length < 7) {
              if (dEdit(aClean, bClean) <= 1) return true
            } else if (aClean.length >= 7 && aClean.length < 10) {
              if (dEdit(aClean, bClean) <= 2) return true
            } else if (aClean.length >= 10 && dEdit(aClean, bClean) <= 3) {
              return true
            }
        }
      case None => return false
    }
    false
  }

  private def evaluateEmailIntelligenceFuzzyMatch(dataSetMap: collection.mutable.Map[String, Set[String]], splitEmailWords: List[String]): Boolean = {
    splitEmailWords.foreach { x =>
      val emailSubStr = x.substring(0, 2)
      if (applyFuzzyMatch(dataSetMap, x, emailSubStr)) return true
    }
    false
  }

  private def evaluateExactMatch(dataSetMap: collection.mutable.Map[String, Set[String]], splitEmailWords: List[String]): Boolean = {
    splitEmailWords.foreach { x =>
      val emailSubStr = x.substring(0, 2)
      if (dataSetMap.getOrElse(emailSubStr, Set.empty).contains(x)) return true
    }
    false
  }

  private def computeEmailIntelligenceRules(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue): RuleCodeResponse = {
    val email = getEmailUserName(requestJsonAST)
    if (email.isEmpty || email.get.length < 3) return ruleCodeResponse
    val numericalRuleCode: List[NumericalRuleCode] = EmailIntelligenceFiles.dataSet.map { case (key, value) =>
      val dataSetMap = value

      val splitEmailWords = try {
        splitEmailHandle(email.get).filter(_.length > 2)
      } catch {
        case e: Exception =>
          logger.error(s"Exception while splitting email handle : ", e)
          List(email.get)
      }
      val exactCase = if (evaluateExactMatch(dataSetMap, splitEmailWords)) {
        Some(NumericalRuleCode(emailIntelligenceRuleCode(key).headOption.get, 1.0))
      } else None
      val fuzzyCase = if (exactCase.isEmpty && evaluateEmailIntelligenceFuzzyMatch(dataSetMap, splitEmailWords)) {
        Some(NumericalRuleCode(emailIntelligenceRuleCode(key).lift(1).get, 1.0))
      } else None
      List(exactCase, fuzzyCase).filter(_.isDefined).map(a => a.get)
    }.toList.flatten
    RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ numericalRuleCode,
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )
  }

  private def splitEmailHandle(s: String): List[String] = {
    val SPLIT_RE = "[^a-zA-Z0-9']+".r
    val texts: Array[String] = SPLIT_RE.split(s)
    val newTexts: Array[ArrayBuffer[String]] = texts.map(_split)
    newTexts.flatMap(_.toList).toList
  }

  private def _split(s: String): ArrayBuffer[String] = {
    val cost: ArrayBuffer[Double] = ArrayBuffer(0)

    def bestMatch(i: Int): (Double, Int) = {
      val candidates: ArrayBuffer[(Double, Int)] = cost.slice(math.max(0, i - EmailIntelligenceFiles.splitMaxWord), i).reverse.zipWithIndex
      candidates.map { case (c, k) =>
        (c + EmailIntelligenceFiles.splitWordCost.getOrElse(s.slice(i - k - 1, i).toLowerCase, Double.PositiveInfinity), k + 1)
      }.min
    }

    for (i <- 1 to s.length) {
      val (c, k) = bestMatch(i)
      cost.append(c)
    }

    val out: ArrayBuffer[String] = ArrayBuffer()
    var i: Int = s.length
    while (i > 0) {
      val (c, k) = bestMatch(i)
      assert(c == cost(i))
      var newToken: Boolean = true
      if (s.slice(i - k, i) != "'") {
        if (out.nonEmpty) {
          if (out.last == "'s" || (s(i - 1).isDigit && out.last.head.isDigit)) {
            val updatedLast: String = s.slice(i - k, i) + out.last
            out(out.length - 1) = updatedLast
            newToken = false
          }
        }
      }
      if (newToken) {
        out.append(s.slice(i - k, i))
      }
      i -= k
    }
    out.reverse
  }

  private def computeFmval600026(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, rulecodeDataFileLookup: RulecodeDataFileLookup): RuleCodeResponse = {
    (Json4sUtility.getOptionalString(requestJsonAST, "raw.email") match {
      case Some(d) =>
        (d.trim.toLowerCase().split("@").map(_.trim).map(JString).toList.headOption match {
          case Some(jName) if jName.s.length > 2 =>
            CleanStringForStopWordsProcessor.process(jName) match {
              case JString(s) => Option(s)
              case _ => None
            }
          case _ => None
        }) match {
          case Some(s) => if (rulecodeDataFileLookup.getNameLookup(s.toLowerCase)) Some(1) else Some(0)
          case _ => None
        }
      case _ => None
    }) match {
      case Some(v) => RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_EMAIL_USERNAME_CONTAINS_NAME, v)),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
      case _ => ruleCodeResponse
    }
  }

  private def
  computeIdPlusRuleCodes(ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue)(implicit trxId: TrxId, ec: ExecutionContext): RuleCodeResponse = {
    val isFeaturePlatformCall = Json4sUtility.getOptionalBoolean(requestJsonAST, "isFeaturePlatformCall")
    if (isFeaturePlatformCall.isDefined && isFeaturePlatformCall.get) {
      val form = createForm(requestJsonAST)
      lazy val preprocessedStreetAddress: StreetAddressRequest = SmartyPreprocessorFactory.process(StreetAddressRequest(
        street = extractOptString("raw.physicalAddress", requestJsonAST),
        street2 = extractOptString("raw.physicalAddress2", requestJsonAST),
        secondary = extractOptString("raw.physicalAddressRaw", requestJsonAST),
        city = extractOptString("city", requestJsonAST),
        state = extractOptString("state", requestJsonAST),
        zipcode = extractOptString("zip", requestJsonAST),
        country = extractOptString("country", requestJsonAST),
        lastLine = None,
        addressee = None,
        urbanization = None,
        `match` = null
      )) // getting used in fetch pre processed address of id plus refer that
      val rcResponse1 = computeStoreAddressRuleCodes(requestJsonAST, resolveRuleScoresAsync(form, ruleCodeResponse))
      val rcResponse2 = resolveRuleScores(form, rcResponse1)
      val rcResponse3 = updateRuleValues(form, preprocessedStreetAddress, rcResponse2)
      val rcResponse4 = updateRuleScores(requestJsonAST, rcResponse3, preprocessedStreetAddress)
      val rcResponse5 = updatePhoneBasedRuleScores(rcResponse4, form)
      val FMVALValidationRuleCodes = computeValidationFMVALRuleCodes(rcResponse5, requestJsonAST)
      computeDerivedRuleCodes(requestJsonAST, FMVALValidationRuleCodes)
    } else ruleCodeResponse
  }

  def opts(prop: String, requestJsonAST: JsonAST.JValue) = extractOptString(prop, requestJsonAST).map(_.trim)

  def tryLocalDate(d: String): Option[LocalDate] = {
    dobFormats.find(df => Try(df.parseLocalDate(d)).isSuccess).map(_.parseLocalDate(d))
  }

  def millisToDateString(millis: Long, format: String = "yyyy-MM-dd", zoneId: String = "UTC"): String = {
    val instant = Instant.ofEpochMilli(millis)
    val formatter = java.time.format.DateTimeFormatter.ofPattern(format).withZone(ZoneId.of(zoneId))
    formatter.format(instant)
  }

  def createForm(requestJsonAST: JsonAST.JValue): Form = {
    val ruleCodeRequest = jsonToRuleCodeRequest(requestJsonAST)
    val hasPostalAddress = Seq("raw.physicalAddress", "raw.physicalAddress2", "raw.physicalAddressRaw", "city", "state", "country", "zip", "raw.geoCode").map(value => extractOptString(value, requestJsonAST)).exists(_.isDefined)
    val addresses = if (ruleCodeRequest.isDefined && ruleCodeRequest.get.raw.isDefined && ruleCodeRequest.get.raw.get.addresses.isDefined) ruleCodeRequest.get.raw.get.addresses.get else Set.empty[Address]
    val payments = if (ruleCodeRequest.isDefined && ruleCodeRequest.get.payments.isDefined) Some(ruleCodeRequest.get.payments.get) else None
    val device = if (ruleCodeRequest.isDefined && ruleCodeRequest.get.device.isDefined) Some(ruleCodeRequest.get.device.get) else None
    val submissionDate = extractOptString("raw.submissionDate", requestJsonAST).getOrElse("")
    val transactionDate = extractOptString("raw.transactionDate", requestJsonAST).getOrElse("")
    val timeAdjustedDate = Json4sUtility.getJStringAsString(GetTimeAdjustedDate.process(JArray(List(JString(submissionDate), JString(transactionDate)))).toOption)
    val timeAdjustedDateInDateFormat = millisToDateString(timeAdjustedDate.toLong)
    val timeAdjustedDateInDateTime = new DateTime(timeAdjustedDateInDateFormat)
    Form(
      email = Email.optFromStr(extractOptString("raw.email", requestJsonAST).getOrElse("")),
      name = Some(Name(firstName = extractOptString("first_name", requestJsonAST), surName = extractOptString("last_name", requestJsonAST))),
      postalAddress = if (hasPostalAddress) RawPostalAddress(
        line1 = extractOptString("raw.physicalAddress", requestJsonAST).orElse(extractOptString("raw.physicalAddressRaw", requestJsonAST).map(_.trim)),
        line2 = extractOptString("raw.physicalAddress2", requestJsonAST),
        zip = extractOptString("raw.zip", requestJsonAST),
        city = extractOptString("raw.city", requestJsonAST),
        state = extractOptString("raw.state", requestJsonAST),
        country = extractOptString("raw.country", requestJsonAST),
        coordinates = extractOptString("raw.geoCode", requestJsonAST).flatMap(GeographicCoordinates.apply)
      ).parse.some
      else None,
      ipAddress = extractOptString("raw.ip", requestJsonAST).flatMap(i => Try(InetAddress.getByName(i)).toOption),
      geoCode = extractOptString("raw.geoCode", requestJsonAST).flatMap(GeographicCoordinates.apply),
      phone = extractOptString("raw.mobileNumber", requestJsonAST).flatMap(mn => Try(Phone(mn.trim)).toOption),
      // TODO : check the date format
      DOB = extractOptString("raw.dobString", requestJsonAST).flatMap(d => {
        Try(DateOfBirth(d, dobFormats: _*)).toOption
      }).flatten,
      orderChannel = opts("orderDetails.orderChannel", requestJsonAST).flatMap(OrderChannels.byNameCaseInsensitive),
      lastOrderDate = opts("orderDetails.lastOrderDate", requestJsonAST).flatMap(tryLocalDate),
      prevOrderCount = extractOptDouble("orderDetails.prevOrderCount", requestJsonAST).map(_.toLong),
      accountCreationDate = opts("orderDetails.accountCreationDate", requestJsonAST).flatMap(tryLocalDate),
      orderAmount = opts("orderDetails.orderAmount", requestJsonAST).map(_.toDouble),
      submissionDate = SubmissionDate.from(input = Some(timeAdjustedDateInDateFormat), transactionDate = timeAdjustedDateInDateTime).toOption,
      addresses = toFormAddresses(addresses),
      payments = toFormPayments(payments),
      device = toFormDevice(device),
      prevUnpaidOrderCount = opts("orderDetails.prevUnpaidOrderCount", requestJsonAST).map(_.toLong)
    )
  }


  def extractOptString(path: String, jval: JValue): Option[String] = {
    Json4sUtility.getOptionalString(jval, path) match {
      case Some(v) if !v.trim.isEmpty => Some(v)
      case _ => None
    }
  }

  def extractOptDouble(path: String, jval: JValue): Option[Double] = {
    Json4sUtility.getOptionalDouble(jval, path) match {
      case Some(v) => Some(v)
      case _ => None
    }
  }

  def jsonToRuleCodeRequest(jValue: JValue): Option[RuleCodeRequest] = {
    implicit val formats = org.json4s.DefaultFormats
    try {
      Some(jValue.extract[RuleCodeRequest])
    } catch {
      case _: Exception => None
    }
  }

  private def toFormAddresses(addresses: Set[Address]): Map[AddressType, PostalAddress] = {
    addresses.map { address =>
      val addressType = AddressTypes.withName(address.addressType.getValue)
      val rawPostalAddress = RawPostalAddress(
        unit = None,
        line1 = address.line1.clean(),
        line2 = address.line2.clean(),
        zip = Option(address.postalCode).clean(),
        city = Option(address.city).clean(),
        state = Option(address.state).clean(),
        country = Option(address.country).clean()
      )
      val postalAddress = rawPostalAddress.parse
      addressType -> postalAddress
    }.toMap
  }

  private def toFormDevice(device: Option[Device]): Option[FormDevice] = {
    device.map(d => {
      FormDevice(d.operatingSystem, d.deviceType, d.deviceInterface)
    })
  }

  private def toFormPayments(payments: Option[Payments]): Option[FormPayments] = {
    payments.map(p => {
      FormPayments(p.recipientCountry, p.paymentType, p.disbursementType,p.paymentTypeDetail,p.productType,p.cardFirst6,p.cardLast4,p.currencyType)
    })
  }

  def toProviderInfo(provider: Option[String],
                     accessToken: Option[String],
                     accessTokenSecret: Option[String],
                     refreshToken: Option[String]): Option[ProviderInfo] = {

    val profileProvider: Option[ProfileProvider] = provider collect {
      case "fb" => ProfileProviders.Facebook
      case "tw" => ProfileProviders.Twitter
      case "in" => ProfileProviders.LinkedIn
      case "gp" => ProfileProviders.Google
    }

    profileProvider.map(p => {
      ProviderInfo(
        provider = Option(p),
        accessToken = accessToken,
        accessTokenSecret = accessTokenSecret,
        refreshToken = refreshToken
      )
    })
  }

  def computeDerivedRuleCodes(requestJsonAst: JsonAST.JValue, ruleCodeResponse: RuleCodeResponse)(implicit trxId: TrxId): RuleCodeResponse = {
    try {
      val rcResponse = computeFmval600071(ruleCodeResponse)
      computeFmval900029(addStdAddressRuleCode(rcResponse, requestJsonAst))
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }
  }

  def addStdAddressRuleCode(ruleCodeResponse: RuleCodeResponse, requestJsonAst: JsonAST.JValue): RuleCodeResponse = {
    val street = Json4sUtility.getOptionalString(requestJsonAst, "street")
    val city = Json4sUtility.getOptionalString(requestJsonAst, "city")
    val state = Json4sUtility.getOptionalString(requestJsonAst, "state")
    val zip = Json4sUtility.getOptionalString(requestJsonAst, "zip")
    val plus4Code = Json4sUtility.getOptionalString(requestJsonAst, "zip4")
    val latitude = Json4sUtility.getOptionalString(requestJsonAst, "latitude")
    val longitude = Json4sUtility.getOptionalString(requestJsonAst, "longitude")

    (street, city, state, zip, plus4Code, latitude, longitude) match {
      case (None, None, None, None, None, None, None) => ruleCodeResponse
      case _ =>
        val categoricalRc = Seq(
          CategoricalRuleCode(FM_SMARTY_STD_DELIVERY_LINE1, street.getOrElse("").toLowerCase()),
          CategoricalRuleCode(FM_SMARTY_STD_CITY, city.getOrElse("null").toLowerCase()),
          CategoricalRuleCode(FM_SMARTY_STD_STATE, state.getOrElse("null").toLowerCase()),
          CategoricalRuleCode(FM_SMARTY_STD_ZIP, zip.getOrElse("null")),
          CategoricalRuleCode(FM_SMARTY_STD_ZIP4, plus4Code.getOrElse("null")),
          CategoricalRuleCode(FM_SMARTY_LATITUDE, latitude.getOrElse("null")),
          CategoricalRuleCode(FM_SMARTY_LONGITUDE, longitude.getOrElse("null")))

        RuleCodeResponse(
          ruleCodeResponse.numericalRuleCodes,
          ruleCodeResponse.categoricalRuleCodes ++ categoricalRc,
          ruleCodeResponse.errors
        )
    }
  }

  def computeValidationFMVALRuleCodes(ruleCodeResponse: RuleCodeResponse, requestJsonAst: JsonAST.JValue): RuleCodeResponse = {
    try {
      val firstName = Json4sUtility.getOptionalString(requestJsonAst, "first_name")
      val surName = Json4sUtility.getOptionalString(requestJsonAst, "last_name")
      val email = Json4sUtility.getOptionalString(requestJsonAst, "raw.email")
      val state = Json4sUtility.getOptionalString(requestJsonAst, "raw.state")
      val country = Json4sUtility.getOptionalString(requestJsonAst, "raw.country")
      val city = Json4sUtility.getOptionalString(requestJsonAst, "raw.city")
      val physicalAddress = Json4sUtility.getOptionalString(requestJsonAst, "raw.physicalAddress")
      val zip = Json4sUtility.getOptionalString(requestJsonAst, "raw.zip")
      val dob = Json4sUtility.getOptionalString(requestJsonAst, "raw.dobString")
      val ipAddress = Json4sUtility.getOptionalString(requestJsonAst, "raw.ip")
      val geocode = Json4sUtility.getOptionalString(requestJsonAst, "raw.geoCode")
      val mobileNumber = Json4sUtility.getOptionalString(requestJsonAst, "raw.mobileNumber")
      val driverLicense = Json4sUtility.getOptionalString(requestJsonAst, "raw.driverLicense")
      val nationalId = Json4sUtility.getOptionalString(requestJsonAst, "raw.ssn")
      val pIIStandardizationResponse = Json4sUtility.getOptionalString(requestJsonAst,"piiStdResponse")
      val piiEmailDomain = Json4sUtility.getOptionalString(requestJsonAst, "piiStdResponse.pii.emailV2.emailDomain")

      val fmValNumericalRuleCodes = ArrayBuffer.empty[NumericalRuleCode]
      val fmValCategoricalRuleCodes = ArrayBuffer.empty[CategoricalRuleCode]

      fmValNumericalRuleCodes += computePiiBasedAddressRuleScores(pIIStandardizationResponse)
      if(piiEmailDomain.isDefined){
        fmValCategoricalRuleCodes += CategoricalRuleCode(FM_PII_EMAIL_DOMAIN, piiEmailDomain.get)
      }

      if (!FMVALCommonValidationsUtils.empty(mobileNumber) && mobileNumber.isDefined) {
        val cityFromPhone = Utilities.getCityFromNumber(mobileNumber.get)
        val countryFromPhone = Utilities.getCountryFromNumber(mobileNumber.get)
        if (StringUtils.isBlank(cityFromPhone) || StringUtils.isBlank(countryFromPhone))
          fmValNumericalRuleCodes += NumericalRuleCode(PHONE_NO_FAKE, 0.0)
        if (!country.getOrElse("").equalsIgnoreCase(countryFromPhone))
          fmValNumericalRuleCodes += NumericalRuleCode(PHONE_NO_UNMATCH, 0.0)
      }

      if (!FMVALCommonValidationsUtils.empty(dob) && dob.isDefined) {
        val date = Utilities.strtoDate(dob.get)
        val birthdate = new DateMidnight(date)
        val now = new DateTime()
        val age = Years.yearsBetween(birthdate, now)
        fmValNumericalRuleCodes += NumericalRuleCode(AGE_YEAR, age.getYears().toDouble)
      }

      if (!FMVALCommonValidationsUtils.empty(email) && email.isDefined) {
        val username = Utilities.getEmailUsername(email.get)
        val usernameLength = username.length().toDouble
        fmValNumericalRuleCodes += NumericalRuleCode(FM_EMAIL_LENGTH, usernameLength)

        val uniqueCharacters = username.chars().distinct().count();
        val uniquePercent = uniqueCharacters / usernameLength;
        fmValNumericalRuleCodes += NumericalRuleCode(FM_PROPORTION_OF_SYMBOLS_IN_EMAIL, 1 - uniquePercent)
      }

      if (FMVALCommonValidationsUtils.empty(firstName) || FMVALCommonValidationsUtils.empty(surName)) fmValNumericalRuleCodes += NumericalRuleCode(FM_MISSING_FIRST_NAME, 0.0)
      else if (FMVALCommonValidationsUtils.empty(email)) fmValNumericalRuleCodes += NumericalRuleCode(FM_MISSING_PROVIDER, 0.0)
      if (!FMVALCommonValidationsUtils.empty(firstName))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_FIRST_NAME_SYNTACTICALLY_VALID, if (FMVALCommonValidationsUtils.isNameValid(firstName)) 1.0 else 0.0)
      if (!FMVALCommonValidationsUtils.empty(surName))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_LAST_NAME_SYNTACTICALLY_VALID, if (FMVALCommonValidationsUtils.isNameValid(surName)) 1.0 else 0.0)
      if (!FMVALCommonValidationsUtils.empty(state) && state.getOrElse("").length != 2)
        fmValNumericalRuleCodes += NumericalRuleCode(FM_STATE_TWO_CHAR, 0.0)
      if (!FMVALCommonValidationsUtils.empty(country) && country.getOrElse("").length != 2)
        fmValNumericalRuleCodes += NumericalRuleCode(FM_COUNTRY_ISO_STD, 0.0)
      if (!FMVALCommonValidationsUtils.empty(state) && !FMVALCommonValidationsUtils.empty(country) && !("US".equalsIgnoreCase(country.getOrElse("")) || "CA".equalsIgnoreCase(country.getOrElse(""))))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_STATE_ONLY_IN_US, 0.0)
      if (!FMVALCommonValidationsUtils.empty(physicalAddress) && FMVALCommonValidationsUtils.empty(city) && FMVALCommonValidationsUtils.empty(zip))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_ADDRESS_NEED_ATLEAST_ZIP, 0.0)
      if (!FMVALCommonValidationsUtils.empty(physicalAddress) && !FMVALCommonValidationsUtils.empty(city) && !FMVALCommonValidationsUtils.empty(country)
        && "US".equalsIgnoreCase(country.getOrElse("")) && FMVALCommonValidationsUtils.empty(state) && FMVALCommonValidationsUtils.empty(zip))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_ADDRESS_NEED_ZIP, 0.0)
      if ((!FMVALCommonValidationsUtils.empty(physicalAddress) || !FMVALCommonValidationsUtils.empty(city) || !FMVALCommonValidationsUtils.empty(zip) || !FMVALCommonValidationsUtils.empty(state) || !FMVALCommonValidationsUtils.empty(country))
        && (FMVALCommonValidationsUtils.empty(physicalAddress) || FMVALCommonValidationsUtils.empty(city) || FMVALCommonValidationsUtils.empty(zip) || FMVALCommonValidationsUtils.empty(state)) && FMVALCommonValidationsUtils.empty(country))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_COUNTRY_CODE_NEED, 0.0)
      if (!FMVALCommonValidationsUtils.empty(dob) && dob.isDefined && FMVALCommonValidationsUtils.strToDate(dob.get).isDefined)
        fmValNumericalRuleCodes += NumericalRuleCode(FM_, 0.0)
      if (!FMVALCommonValidationsUtils.empty(ipAddress) && !FMVALCommonValidationsUtils.isValidInetAddress(ipAddress.getOrElse("")))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_IP_MEET_IP_STD, 0.0)
      if (!FMVALCommonValidationsUtils.empty(geocode) && !FMVALCommonValidationsUtils.isValidGeoCode(geocode.getOrElse("")))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_GEOCODE_INVALID, 0.0)
      if (!FMVALCommonValidationsUtils.empty(mobileNumber) && !FMVALCommonValidationsUtils.isPossibleNumber(mobileNumber.getOrElse("")))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_SUPPORT_PHONE_FORMAT, 0.0)
      if (!FMVALCommonValidationsUtils.empty(driverLicense) && !FMVALCommonValidationsUtils.isPossibleDrivingLicense(driverLicense.getOrElse("")))
        fmValNumericalRuleCodes += NumericalRuleCode(FM_INVALID_DRIVING_LICENSE, 0.0)

      //National id format: Full 9 digits or last 4 digits
      if (nationalId != null
        && nationalId.isDefined
        && !nationalId.get.trim.isEmpty
        && country != null
        && country.isDefined
        && !country.get.trim.isEmpty
        && country.get.trim.equalsIgnoreCase("us")
        && FMVALCommonValidationsUtils.isValidNationalId(nationalId.get.trim, country.get)) fmValNumericalRuleCodes += NumericalRuleCode(FM_INVALID_NATIONAL_ID, 0.0)

      val validAddressProvided = (!FMVALCommonValidationsUtils.empty(physicalAddress) || !FMVALCommonValidationsUtils.empty(city) || !FMVALCommonValidationsUtils.empty(state) || !FMVALCommonValidationsUtils.empty(zip)) && !FMVALCommonValidationsUtils.empty(country)
      if (validAddressProvided)
        fmValNumericalRuleCodes += NumericalRuleCode(FM_ADDRESS_PROVIDED, 1.0)
      val validUSAddress = !FMVALCommonValidationsUtils.empty(country) && country.isDefined && "US".equalsIgnoreCase(country.get) && !FMVALCommonValidationsUtils.empty(physicalAddress) && (!FMVALCommonValidationsUtils.empty(city) && !FMVALCommonValidationsUtils.empty(state) || !FMVALCommonValidationsUtils.empty(zip))
      if (validUSAddress)
        fmValNumericalRuleCodes += NumericalRuleCode(FM_FULL_US_ADDRESS_PROVIDED, 1.0)

      fmValNumericalRuleCodes += NumericalRuleCode(FAKE_PHYSICAL_ADDRESS,0.0)

      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ fmValNumericalRuleCodes,
        ruleCodeResponse.categoricalRuleCodes ++ fmValCategoricalRuleCodes,
        ruleCodeResponse.errors
      )
    }

    catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for computeValidationFMVALRuleCodes : ", exception)
        ruleCodeResponse
    }
  }

  private def computePiiBasedAddressRuleScores(piiStandardizationResponseString: Option[String]): NumericalRuleCode = {
    val piiStandardizationResponse = FMVALCommonValidationsUtils.getPIIResponse(piiStandardizationResponseString)
    piiStandardizationResponse.isDefined match {
      case true =>
        val response = piiStandardizationResponse.get
        val fillers = Set("^null.*", "^none.*", "^line1.*", "^1111 some lane.*", "^123 fake.*",
          "^address_full_match.*", "^123 abc.*", "^example 1.*", "^address1.*", "^123 some address",
          "^not applicable.*", "^n/a.*", "^123 some .*", "^123 main.*", "^no address.*", "^unknown.*", "^tbd.*",
          "^sample address.*", "^filler address.*", "^undefined.*", "^homeless.*", "^parking lot.*",
          ".*new york state thruway.*", ".*address line 1.*", ".*passive street.*", ".*house#123.*", ".*12345.*")
          .map(_.r)
        val address = response.pii.addressV2.flatMap(_.preprocessed.concatenated)
        val matchesAnyRegex = address.exists(a => fillers.exists(_.findFirstIn(a.trim).isDefined))
        NumericalRuleCode(FM_ADDRESS_IS_FILLER , if (matchesAnyRegex) 1.0 else 0.0)
      case _ => NumericalRuleCode(FM_ADDRESS_IS_FILLER,0.0)
    }
  }

  def computeFmval900029(ruleCodeResponse: RuleCodeResponse): RuleCodeResponse = {
    try {
      val EarthRadiusMiles = 3958.8 // Radius of the Earth in miles
      val fmval900025 = getCategoricalMatchResult(FM_SMARTY_LATITUDE, ruleCodeResponse)
      val fmval900026 = getCategoricalMatchResult(FM_SMARTY_LONGITUDE, ruleCodeResponse)
      val fmval900027 = getNumericalMatchResult(FM_SMARTY_STORE_LATITUDE, ruleCodeResponse)
      val fmval900028 = getNumericalMatchResult(FM_SMARTY_STORE_LONGITUDE, ruleCodeResponse)

      if (fmval900025.isDefined && fmval900026.isDefined && fmval900027.isDefined && fmval900028.isDefined) {
        val dLat = (fmval900025.get.toDouble - fmval900027.get) * Math.PI / 180
        val dLon = (fmval900026.get.toDouble - fmval900028.get) * Math.PI / 180
        val a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos(fmval900027.get * Math.PI / 180) * Math.cos(fmval900025.get.toDouble * Math.PI / 180) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2)
        val c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
        RuleCodeResponse(
          ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(FM_INPUT_LATLONG_TO_STORE_LATLONG_DISTANCE_IN_MILES, "%.2f".format(EarthRadiusMiles * c).toDouble)),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      } else ruleCodeResponse
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }
  }


  def computeFmval600071(ruleCodeResponse: RuleCodeResponse): RuleCodeResponse = {
    try {
      val fmval300012 = getNumericalMatchResult(FM_EMAIL_TLD_IS_COUNTRY_CODE, ruleCodeResponse)
      val fmval300035 = getCategoricalMatchResult(FM_EMAIL_TLD, ruleCodeResponse)
      val fmval600071 = if (fmval300012.isDefined && fmval300012.get == 1.0 && fmval300035.isDefined && sanctionedCountries.contains(fmval300035.get.toUpperCase())) Some(1.0)
      else if (fmval300012.isDefined) Some(0.0)
      else None

      fmval600071 match {
        case Some(score) =>
          RuleCodeResponse(
            ruleCodeResponse.numericalRuleCodes ++ Seq(NumericalRuleCode(EMAIL_TLD_OFAC_SANCTIONED_COUNTRY, score)),
            ruleCodeResponse.categoricalRuleCodes,
            ruleCodeResponse.errors
          )
        case _ => ruleCodeResponse
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }
  }

  def updatePhoneBasedRuleScores(ruleCodeResponse: RuleCodeResponse, form: Form)(implicit trxId: TrxId): RuleCodeResponse = {
    try {
      val numericRuleCode = Seq(
        phoneNoDiffCountryStatus(form, ruleCodeResponse).map(s =>
          NumericalRuleCode(PHONE_NO_DIFF_COUNTRY, s)
        ),
        phoneStateMatchesInputState(form, ruleCodeResponse).map(s =>
          NumericalRuleCode(FM_PHONE_STATE_MATCHES_INPUT_STATE, s)
        ),
        phonePremiumNumber(form, ruleCodeResponse).map(s =>
          NumericalRuleCode(FM_PHONE_IS_PREMIUM, s)
        ),
        phoneCountryIsSanctioned(ruleCodeResponse).map(s =>
          NumericalRuleCode(FM_PHONE_COUNTRY_CODE_OFAC_SANCTIONED_COUNTRY, s)
        )
      ).filter(_.nonEmpty).map(_.get)
      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ numericRuleCode,
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }
  }

  def phonePremiumNumber(form: Form, ruleCodeResponse: RuleCodeResponse): Option[Double] = {
    val phoneUtil = PhoneNumberUtil.getInstance
    form.phone match {
      case Some(phoneNumber) if phoneNumber.value != null && !phoneNumber.value.equals("") =>
        val usCleanedN = usCleanedPhone(form.postalAddress.flatMap(_.countryStr), phoneNumber)
        val phone = usCleanedN.value.filterNot(c => c == '-' || c == '(' || c == ')').replaceAll("\\s", "")
        val number = if (!phone.startsWith("+")) "+" + phone else phone
        val numberProto = phoneUtil.parse(number, null)
        val regionCode = phoneUtil.getRegionCodeForNumber(numberProto)
        val fmPhoneCountry = phoneBasedCountryCodeRule(form).getOrElse(FM_PHONE_COUNTRY, "")
        val fmPhoneInvalid = getNumericalMatchResult(FM_PHONE_INVALID, ruleCodeResponse)
        if (fmPhoneCountry == null || fmPhoneCountry.isEmpty || (fmPhoneInvalid.isDefined && fmPhoneInvalid.get == 1.0)) {
          None
        } else if (PremiumPhoneUtil.isPremiumPhoneNumber(regionCode, numberProto.getNationalNumber)) {
          Some(1.0)
        } else {
          None
        }
      case _ => None
    }
  }

  def phoneStateMatchesInputState(form: Form, ruleCodeResponse: RuleCodeResponse): Option[Double] = {
    val stateCode = Set("CA", "US")
    form.phone match {
      case Some(p) if (p.value != null && !p.value.equals("")) =>
        val fmPhoneCountry = getCategoricalMatchResult(FM_PHONE_COUNTRY, ruleCodeResponse)
        val fmPhoneNANAPState = getCategoricalMatchResult(FM_PHONE_NANP_STATE, ruleCodeResponse)

        val inputState = form.postalAddress.flatMap {
          _.state
        } match {
          case Some(x) => x
          case _ => null
        }

        if (fmPhoneCountry == null || !stateCode.contains(fmPhoneCountry.get.toUpperCase) || inputState == null || fmPhoneNANAPState.isEmpty) {
          None
        } else if (stateCode.contains(fmPhoneCountry.get.toUpperCase) && inputState.equals(fmPhoneNANAPState.get)) {
          Some(1.0)
        } else {
          Some(0.0)
        }
      case _ => None
    }
  }

  private def phoneNoDiffCountryStatus(form: Form, ruleCodeResponse: RuleCodeResponse): Option[Double] = {
    form.phone match {
      case Some(p) if (p.value != null && !p.value.equals("")) =>
        val fmPhoneCountry = getCategoricalMatchResult(FM_PHONE_COUNTRY, ruleCodeResponse)
        form.postalAddress.flatMap(_.country).map(_.code2) match {
          case Some(code) if (fmPhoneCountry != null && fmPhoneCountry.isDefined) =>
            if (!code.toUpperCase.equals(fmPhoneCountry.get.toUpperCase)) {
              Some(1.0)
            } else {
              Some(0.0)
            }
          case _ => None
        }
      case _ => None
    }
  }

  def phoneCountryIsSanctioned( ruleCodeResponse: RuleCodeResponse): Option[Double] = {
    getCategoricalMatchResult(FM_PHONE_COUNTRY, ruleCodeResponse) match {
            case Some(country) => if(sanctionedCountries.contains(country.toUpperCase())) Some(1.0)
                                else Some(0.0)
            case None => None
         }
  }


  def updateRuleValues(form: Form, streetAddressRequest: StreetAddressRequest, ruleCodeResponse: RuleCodeResponse)(implicit trxId: TrxId, ec: ExecutionContext): RuleCodeResponse = {
    try {
      val phoneBasedAreaRule = phoneBasedAreaCodeRule(form)
      val phoneBasedCountryRule = phoneBasedCountryCodeRule(form)
      val categoricalRuleCode = resolveRuleValues(form) ++ fetchPreprocessedAddress(streetAddressRequest) ++ addressBasedRules(form) ++ phoneBasedAreaRule ++
        phoneBasedCountryRule ++ phoneBasedLineTypeRule(form) ++ resolveNANPState(ruleCodeResponse, form, (phoneBasedCountryRule, phoneBasedAreaRule))
      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes,
        ruleCodeResponse.categoricalRuleCodes ++ parseToCategoricalRuleCodes(categoricalRuleCode),
        ruleCodeResponse.errors
      )
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }
  }

  def fetchPreprocessedAddress(streetAddressRequest: StreetAddressRequest): Map[String, String] = {
    if (streetAddressRequest == null) Map.empty
    else {
      streetAddressRequest.street match {
        case Some(x) => Map(FM_PRE_PROCESSED_PHYSICAL_ADDRESS -> x.toLowerCase)
        case _ => Map.empty
      }
    }
  }

  def resolveNANPState(ruleCodeResponse: RuleCodeResponse, form: Form, phoneAndAreaBasedAreaRule: (Map[String, String], Map[String, String])): Map[String, String] = {
    getPhoneNANPState(ruleCodeResponse, form, phoneAndAreaBasedAreaRule) match {
      case Some(s) => Map(FM_PHONE_NANP_STATE -> s.toUpperCase)
      case _ => Map.empty
    }
  }

  def getPhoneNANPState(ruleCodeResponse: RuleCodeResponse, form: Form, phoneAndAreaBasedAreaRule: (Map[String, String], Map[String, String])): Option[String] = {
    val stateCode = Set("AG", "AI", "AS", "BB", "BM", "BS", "DM", "DO", "GD", "GU", "JM", "KN", "KY", "LC", "MP", "MS", "PR", "SX", "TC", "TT", "VC", "VG", "VI")
    form.phone match {
      case Some(p) if p.value != null && !p.value.equals("") =>
        val fmPhoneCountry = phoneAndAreaBasedAreaRule._1.get(FM_PHONE_COUNTRY).getOrElse("")
        val fmPhoneAreaCode = phoneAndAreaBasedAreaRule._2.get(FM_PHONE_AREA_CODE).getOrElse("")
        val fmPhoneInvalidState = getNumericalMatchResult(FM_PHONE_INVALID, ruleCodeResponse)
        if (fmPhoneInvalidState.isEmpty || fmPhoneInvalidState.get == 1.0) {
          None
        } else if (stateCode.contains(fmPhoneCountry)) {
          Some(fmPhoneCountry)
        } else if (fmPhoneCountry != null && fmPhoneAreaCode != null && ("CA".equals(fmPhoneCountry.toUpperCase) || "US".equals(fmPhoneCountry.toUpperCase))) {
          PhoneAreaMapping.byPhoneAreaCode(fmPhoneAreaCode) match {
            case Some(x) => Some(x.stateCode)
            case None => None
          }
        } else {
          None
        }
      case _ => None
    }
  }

  def addressBasedRules(form: Form): Map[String, String] = {
    form.postalAddress.flatMap(addr => {
      addr.state.map(state => {
        Map(FM_INPUT_STATE -> state.toUpperCase)
      })
    }).getOrElse(Map.empty)
  }

  def phoneBasedLineTypeRule(form: Form): Map[String, String] = {
    form.phone.flatMap(ph => {
      getLineType(Option(usCleanedPhone(form.postalAddress.flatMap(_.countryStr), ph))).map(s =>
        Map(FM_PHONE_LINE_TYPE -> s)
      )
    }).getOrElse(Map.empty)
  }

  def getLineType(phone: Option[Phone]): Option[String] = {
    val phoneUtil = PhoneNumberUtil.getInstance
    phone match {
      case Some(p) if (p.value != null && !p.value.equals("")) =>
        val mobNum = p.value.filterNot(c => c == '-' || c == '(' || c == ')').replaceAll("\\s", "")
        val number = if (!mobNum.startsWith("+")) "+" + mobNum else mobNum
        val numberProto = phoneUtil.parse(number, null)
        Option(phoneUtil.getNumberType(numberProto).name())
      case _ => None
    }
  }

  def phoneBasedCountryCodeRule(form: Form): Map[String, String] = {
    form.phone.flatMap(ph => {
      getCountryCode(Option(usCleanedPhone(form.postalAddress.flatMap(_.countryStr), ph))).map(s =>
        Map(FM_PHONE_COUNTRY -> s.toUpperCase)
      )
    }).getOrElse(Map.empty)
  }

  def getPhoneCountryCode(form: Form): Option[String] = {
    form.phone.flatMap(ph => {
      getCountryCode(Option(usCleanedPhone(form.postalAddress.flatMap(_.countryStr), ph))).map(s =>
        s.toUpperCase
      )
    })
  }

  def resolveRuleValues(form: Form): Map[String, String] = {

    val submissionDate = submissionDateTime(form)

    val ruleValues = Seq(
      resolveDomain(form.email).map(FM_EMAL_DOMAIN -> _),

      resolveTLD(form.email).map(FM_EMAIL_TLD -> _.toString),

      resolveTLDType(form.email).map(FM_EMAIL_TLD_TYPE -> _.toString),

      form.orderChannel.map(FM_ORDER_CHANNEL -> _.toString),

      form.lastOrderDate.map(FM_LAST_ORDER_DATE -> ruleDateFormat.print(_)),

      form.accountCreationDate.map(FM_ACCOUNT_CREATION_DATE -> ruleDateFormat.print(_)),

      submissionDate.map(FM_SUBMISSION_DATE -> SubmissionDate.dateTimeFormat.print(_)),

      submissionDate.map(FM_SUBMISSIONDATE_DAY_OF_WEEK -> _.withZone(NYTimeZone).dayOfWeek().getAsShortText),

      form.name.flatMap(_.firstName.flatMap(fname => {
        RulecodeLogicRegistry.getRulecodeValue("FMVAL.100151", List(fname)) match {
          case Some(result) =>
            result match {
              case Right(gender) => Some(FM_GENDER -> gender)
              case Left(_) => None
            }
          case None => None
        }
      }))).flatten.toMap

    val formEmail = form.email.map(_.value.trim.toLowerCase)
    val emailDistanceRuleValues = formEmail.map(e =>
      Seq(inputEmailDistance(e).map {
        case v if v._1 == FM_EMAIL_DOMAIN_VS_FREE_DOMAINS_LEVENSHTEIN_SCORE =>
          val ruleValue = if (v._2 == 0.0) EXACT
          else if (v._2 == 1.0) ONE
          else TWOPLUS
          FM_EMAIL_DOMAIN_VS_FREE_DOMAINS_LEVENSHTEIN_CAT -> ruleValue
        case v if v._1 == FM_EMAIL_TLD_VS_FREE_TLD_LEVENSHTEIN_SCORE =>
          val ruleValue = if (v._2 == 0.0) EXACT
          else if (v._2 == 1.0) ONE
          else TWOPLUS
          FM_EMAIL_TLD_VS_FREE_TLD_LEVENSHTEIN_CAT -> ruleValue
      }).flatten.toMap
    ).getOrElse(Map.empty)

    val paymentRuleValues = form.payments.map(payments => {
      Seq(
        payments.recipientCountry.map(recipientCountry => FM_PAYMENTS_RECIPIENT_COUNTRY -> recipientCountry.toUpperCase),
        payments.paymentType.map(paymentType => FM_PAYMENTS_PAYMENT_TYPE -> paymentType),
        payments.disbursementType.map(disbursementType => FM_PAYMENTS_DISBURSEMENT_TYPE -> disbursementType)
      ).flatten.toMap
    }).getOrElse(Map.empty)

    val deviceRuleValues = form.device.map(device => {
      Seq(
        device.os.map(os => FM_DEVICE_OPERATING_SYSTEM -> os),
        device.deviceType.map(deviceType => FM_DEVICE_TYPE -> deviceType),
        device.interface.map(interface => FM_DEVICE_INTERFACE -> interface)
      ).flatten.toMap
    }).getOrElse(Map.empty)

    val athleteRuleValues = athleteBasedRuleValues(form.name, form.DOB).getOrElse(Map.empty)

    ruleValues ++ emailDistanceRuleValues ++ paymentRuleValues ++ deviceRuleValues ++ athleteRuleValues
  }

  private def athleteBasedRuleValues(name: Option[Name], dobOpt: Option[DateOfBirth]): Option[Map[String, String]] = {
    dobOpt match {
      case Some(dob) => {
        val firstName = name.flatMap(_.firstName).map(_.trim.toLowerCase).getOrElse("")
        val surName = name.flatMap(_.surName).map(_.trim.toLowerCase).getOrElse("")
        val formattedDob = compactOrderedDate.print(basicDateUTC.parseDateTime(dob.toBasicDateUTC))
        val dobMatchedAthletes = AthletesData.get(formattedDob)

        val filteredClosestMatchedAthletesOpt = filterClosestMatchedAthletes(dobMatchedAthletes, firstName, surName)

        val filteredActiveClosestMatchedAthletes = filteredClosestMatchedAthletesOpt.map(_.filter(_.status.equalsIgnoreCase(ActiveAthleteStatus))).getOrElse(Seq.empty)
        val filteredRetiredClosestMatchedAthletes = filteredClosestMatchedAthletesOpt.map(_.filter(_.status.equalsIgnoreCase(RetiredAthleteStatus))).getOrElse(Seq.empty)

        if (filteredActiveClosestMatchedAthletes.nonEmpty)
          Some(
            Map(
              FM_WIKI_ATHLETE_ASSOCIATED_SPORT_NAME -> Serialization.write(filteredActiveClosestMatchedAthletes.flatMap(_.sport).map(_.toUpperCase).filter(_.nonEmpty).toSet),
              FM_WIKI_ATHLETE_ASSOCIATED_SPORT_LEAGUE -> Serialization.write(filteredActiveClosestMatchedAthletes.flatMap(_.league).map(_.toUpperCase).filter(_.nonEmpty).toSet),
              FM_WIKI_ATHLETE_SOURCE_ATTRIBUTION_LINKS -> Serialization.write(filteredActiveClosestMatchedAthletes.flatMap(_.link).filter(_.nonEmpty).toSet)
            )
          )
        else if (filteredRetiredClosestMatchedAthletes.nonEmpty)
          Some(
            Map(
              FM_WIKI_ATHLETE_ASSOCIATED_SPORT_NAME -> Serialization.write(filteredRetiredClosestMatchedAthletes.flatMap(_.sport).map(_.toUpperCase).filter(_.nonEmpty).toSet),
              FM_WIKI_ATHLETE_ASSOCIATED_SPORT_LEAGUE -> Serialization.write(filteredRetiredClosestMatchedAthletes.flatMap(_.league).map(_.toUpperCase).filter(_.nonEmpty).toSet),
              FM_WIKI_ATHLETE_SOURCE_ATTRIBUTION_LINKS -> Serialization.write(filteredRetiredClosestMatchedAthletes.flatMap(_.link).filter(_.nonEmpty).toSet)
            )
          )
        else None
      }
      case _ => None
    }
  }

  private def resolveDomain(emailOpt: Option[Email]): Option[String] = {
    emailOpt.map(_.domain.trim.toLowerCase).map {
      case domain if recognizedDomains.contains(domain) => domain
      case _ => otherDomains
    }
  }

  private def resolveTLD(emailOpt: Option[Email]): Option[String] = {
    emailOpt.map(_.domain.trim.toLowerCase).flatMap {
      case domain => EmailValidationHelper.extractTLDFromDomain(domain)
      case _ => None
    }
  }

  private def resolveTLDType(emailOpt: Option[Email]): Option[String] = {
    emailOpt.map(_.domain.trim.toLowerCase).flatMap {
      case domain => EmailValidationHelper.tldType(domain)
      case _ => None
    }
  }

  def getCountryCode(phone: Option[Phone]): Option[String] = {
    val phoneUtil = PhoneNumberUtil.getInstance
    phone match {
      case Some(p) if (p.value != null && !p.value.equals("")) =>
        val mobNum = p.value.filterNot(c => c == '-' || c == '(' || c == ')').replaceAll("\\s", "")
        val number = if (!mobNum.startsWith("+")) "+" + mobNum else mobNum
        val numberProto = phoneUtil.parse(number, null)
        val regionCode = phoneUtil.getRegionCodeForNumber(numberProto)
        Option(regionCode)

      case _ => None
    }
  }

  def phoneBasedAreaCodeRule(form: Form): Map[String, String] = {
    form.phone.flatMap(ph => {
      getAreaCode(Option(usCleanedPhone(form.postalAddress.flatMap(_.countryStr), ph))).map(s =>
        Map(FM_PHONE_AREA_CODE -> s)
      )
    }).getOrElse(Map.empty)
  }

  def getAreaCode(phone: Option[Phone]):Option[String] = {
    val phoneUtil = PhoneNumberUtil.getInstance
    phone match {
      case Some(p) if (p.value != null && !p.value.equals("")) =>
        val mobNum = p.value.filterNot(c => c == '-' || c == '(' || c == ')').replaceAll("\\s", "")
        val number = if (!mobNum.startsWith("+")) "+" + mobNum else mobNum

        try {
          val numberProto = phoneUtil.parse(number, null) // Parse without assuming country
          val regionCode = phoneUtil.getRegionCodeForNumber(numberProto)

          if (regionCode == "GB") {         // Special handling for UK numbers
            val nationalNumber = phoneUtil.getNationalSignificantNumber(numberProto) // e.g., "1382698341"
            Some(nationalNumber.take(4)) // Default to first 4 digits
          } else {
            // Default handling for non-UK numbers (preserves original functionality)
            val nationalFormattedPhone = phoneUtil.format(numberProto, PhoneNumberFormat.NATIONAL)
            if (nationalFormattedPhone.contains("(") && nationalFormattedPhone.contains(")")) {
              Some(nationalFormattedPhone.substring(nationalFormattedPhone.indexOf("(") + 1, nationalFormattedPhone.indexOf(")")))
            } else {
              None
            }
          }
        } catch {
          case _: Exception => None
        }
      case _ => None
    }
  }

  def updateRuleScores(request: JsonAST.JValue, ruleCodeResponse: RuleCodeResponse, preProcessedAddress: StreetAddressRequest)(implicit trxId: TrxId): RuleCodeResponse = {
    try {
      val preprocessedStreet = preProcessedAddress.street
      val numericRuleCode = preprocessedStreet.flatMap(processedStreet => {
        Json4sUtility.getOptionalString(request, "raw.physicalAddressRaw").map(physicalAddress => {
          NumericalRuleCode(FM_ADDRESS_REQUIRED_PREPROCESSING, boolToDouble(processedStreet.toLowerCase != physicalAddress.toLowerCase).toDouble)
        })
      })
      numericRuleCode match {
        case Some(x) => RuleCodeResponse(
          ruleCodeResponse.numericalRuleCodes ++ Seq(x),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
        case _ => ruleCodeResponse
      }
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }
  }

  def resolveRuleScores(form: Form, ruleCodeResponse: RuleCodeResponse): RuleCodeResponse = {
    try {
      val submissionDate = submissionDateTime(form)
      val customerAddress: Option[PostalAddress] = form.postalAddress
      val storeAddress: Option[PostalAddress] = form.addresses.find { case (addressType, _) => {
        addressType == AddressTypes.Store
      }
      }.map(address => address._2)
      val formEmail = form.email.map(_.value.trim.toLowerCase)

      val phoneBasedRules = Seq(

        form.phone.flatMap(ph => {
          isTollNumber(ph).map(isToll => {
            FM_PHONE_TOLL_NUMBER -> boolToDouble(isToll)
          })
        }),

        form.phone.flatMap(ph => {
          suspiciousAreaCode(ph).map(isSuspicious => {
            FM_PHONE_FIRST_AREA_CODE_DIGIT_NOT_2TO9 -> boolToDouble(isSuspicious)
          })
        }),

        form.phone.map(ph => {
          val badCentralNumber = suspiciousCentralOffice(ph)
          FM_PHONE_CENTRAL_OFFICE_CODE_DIGIT_NOT_2TO9 -> boolToDouble(badCentralNumber)
        }),

        form.phone.map(ph => {
          val consecutive = maxConsecutivePhoneDigits(ph)
          FM_PHONE_NUMBER_OF_CONSECUTIVE_DIGITS -> consecutive.toDouble
        }),

        form.phone.map(ph => {
          val uniqueDigits = uniquePhoneLetterCount(ph)
          FM_PHONE_NUMBER_OF_UNIQUE_DIGITS -> uniqueDigits.toDouble
        }),

        form.phone.flatMap(ph => {
          getRegion(Option(usCleanedPhone(form.postalAddress.flatMap(_.countryStr), ph))).map(n => {
            FM_PHONE_US_OR_CANADA -> n
          })
        }),

        form.phone.flatMap(ph => {
          isPhoneValid(Option(usCleanedPhone(form.postalAddress.flatMap(_.countryStr), ph))).map(n => {
            FM_PHONE_INVALID -> n
          })
        })

      ).flatten.toMap

      val orderBasedRules = Seq(
        form.prevOrderCount.map(_.toDouble).map(FM_PREV_ORDER_COUNT -> _),
        form.prevUnpaidOrderCount.map(_.toDouble).map(FM_PREV_UNPAID_ORDER_COUNT -> _),
        form.orderAmount.map(_.roundTo(2)).map(FM_ORDER_AMOUNT -> _),
        form.lastOrderDate.map(Days.daysBetween(_, LocalDate.now(DateTimeZone.UTC)).getDays.toDouble).map(FM_NUMBER_DAYS_SINCE_LAST_ORDER -> _),
        form.accountCreationDate.map(Days.daysBetween(_, LocalDate.now(DateTimeZone.UTC)).getDays.toDouble).map(FM_NUMBER_DAYS_SINCE_ACCOUNT_CREATION_DATE -> _)
      ).flatten.toMap

      val timeZonePredictorRules = Seq(
        submissionDate.map(dt => FM_SUBMISSIONDATE_IS_US_BUSINESS_DAY -> boolToDouble(!isUsHoliday(dt) && isUsWeekDay(dt))),
        submissionDate.map(dt => FM_SUBMISSIONDATE_IS_US_HOLIDAY -> boolToDouble(isUsHoliday(dt)))
      ).flatten.toMap

      val storeStateMatchesForm = storeStateMatches(customerAddress, storeAddress).map(isMatching => {
        FM_INPUT_STATE_TO_STORE_STATE_MATCH -> boolToDouble(isMatching)
      }).toMap

      val storeZipMatchesForm = storeZipMatches(customerAddress, storeAddress).map(isMatching => {
        FM_INPUT_ZIP_TO_STORE_ZIP_MATCH -> boolToDouble(isMatching)
      }).toMap

      val maxConsecutiveDigits = customerAddress.flatMap(addr => {
        addr.line1.flatMap(physicalAddress => {
          val streetNumber = physicalAddress.split(" ").find(_.nonEmpty)
          streetNumber.map(sn => {
            val maxSequence = calculateMaxSequence(sn)
            Map(FM_NUMBER_OF_CONSECUTIVE_DIGITS_IN_INPUT_ADDRESS -> maxSequence.toDouble)
          })
        })
      }).getOrElse(Map.empty)

      val maxRLE = customerAddress
        .flatMap(_.line1)
        .flatMap(s => Option(s))
        .map(_.trim)
        .filter(_.nonEmpty)
        .flatMap(_.split(" ").headOption)
        .map(_
          .toLowerCase
          .replace("[a-z]+", " ")
          .replaceAll("[+-._,:]", "")
        )
        .map(RunLength.getHighestRunLengthDigits)
        .map(score => FM_MAX_LENGTH_OF_REPEATING_IN_INPUT_ADDRESS -> (if (score <= 1) 0.0 else score))
        .toMap

      val emailDomainTldbasedRuleScores: Map[String, Double] = formEmail.map(e =>
        inputEmailRuleScores(e) ++ inputEmailDistance(e)
      ).getOrElse(Map.empty)

      val athleteRuleScores = athleteBasedRuleScores(form.name, form.DOB).getOrElse(Map.empty)

      val fieldValidationScores = isFirstNameNonPerson(form.name).getOrElse(Map.empty) ++
        isSurNameNonPerson(form.name).getOrElse(Map.empty) ++
        isPossibleDualApplicant(form.name).getOrElse(Map.empty) ++
        dobAgeOutliersRule(form.DOB).getOrElse(Map.empty) ++
        dobAgeOutliersAnotherRule(form.DOB).getOrElse(Map.empty) ++
        ageInYears(form.DOB).getOrElse(Map.empty) ++
        ageInYearsTimeAdj(form.DOB, coaleasceSubmissionWithCurrent(form)).getOrElse(Map.empty) ++
        isDobInfuture(form.DOB, coaleasceSubmissionWithCurrent(form)).getOrElse(Map.empty) ++
        invalidYearOfBirthRule(form.DOB).getOrElse(Map.empty) ++
        consumerPhoneRule(form.postalAddress.flatMap {
          _.country
        }, form.phone).getOrElse(Map.empty)

      val numericalRc: Map[String, Double] = phoneBasedRules ++ orderBasedRules ++ timeZonePredictorRules ++ storeStateMatchesForm ++ storeZipMatchesForm ++
        maxConsecutiveDigits ++ maxRLE ++ emailDomainTldbasedRuleScores ++ fieldValidationScores ++ athleteRuleScores

      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ parseToNumericalRuleCodes(numericalRc),
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }
  }

  private def submissionDateTime(form: Form): Option[DateTime] = {
    form.submissionDate.map(_.value.fold(identity, _.toDateTimeAtStartOfDay(DateTimeZone.UTC)))
  }

  private def storeStateMatches(customerAddress: Option[PostalAddress], storeAddress: Option[PostalAddress]): Option[Boolean] = {
    customerAddress.flatMap(addr => {
      addr.state.flatMap(customerState => {
        storeAddress.flatMap(storeAddr => {
          storeAddr.state.map(_ == customerState)
        })
      })
    })
  }

  private def athleteBasedRuleScores(name: Option[Name], dobOpt: Option[DateOfBirth]): Option[Map[String, Double]] = {
    dobOpt match {
      case Some(dob) => {
        val firstName = name.flatMap(_.firstName).map(_.trim.toLowerCase).getOrElse("")
        val surName = name.flatMap(_.surName).map(_.trim.toLowerCase).getOrElse("")
        val formattedDob = compactOrderedDate.print(basicDateUTC.parseDateTime(dob.toBasicDateUTC))
        val dobMatchedAthletes = AthletesData.get(formattedDob)

        val filteredClosestMatchedAthletes = filterClosestMatchedAthletes(dobMatchedAthletes, firstName, surName)

        if (filteredClosestMatchedAthletes.exists(isActiveAthletesPresent))
          Some(Map(FM_WIKI_ATHLETE -> Score(1.0)))
        else if (filteredClosestMatchedAthletes.exists(isRetiredAthletesPresent))
          Some(Map(FM_WIKI_ATHLETE_RETIRED -> Score(1.0)))
        else None
      }
      case _ => None
    }
  }

  private def isTollNumber(phone: Phone): Option[Boolean] = {
    val validTolls = Seq("800", "833", "844", "855", "866", "877", "888")
    phone.areaCode.map(areaCode => {
      validTolls.contains(areaCode)
    })
  }

  def resolveRuleScoresAsync(form: Form, ruleCodeResponse: RuleCodeResponse)(implicit trxId: TrxId, ec: ExecutionContext): RuleCodeResponse = {
    try {
      val customerAddress: Option[PostalAddress] = form.postalAddress
      val storeAddress: Option[PostalAddress] = form.addresses.find { case (addressType, _) =>
        addressType == AddressTypes.Store
      }.map(_._2)

      val billingAddress: Option[PostalAddress] = form.addresses.find { case (addressType, _) =>
        addressType == AddressTypes.Billing
      }.map(_._2)

      val shippingAddress: Option[PostalAddress] = form.addresses.find { case (addressType, _) =>
        addressType == AddressTypes.Shipping
      }.map(_._2)

      val phoneCountry = getPhoneCountryCode(form)

      val combinedFuture: Future[Seq[Option[(String, Double)]]] = Future.sequence(Seq(
        fetchZipToZipDistance(customerAddress, storeAddress)
          .map(_.map(s => FM_INPUT_ZIP_TO_STORE_ZIP_DISTANCE_IN_MILES -> s)),

        fetchZipToZipDistance(customerAddress, storeAddress)
          .map(_.map(s => HOME_TO_STORE_ADDRESS_DISTANCE -> s)),

        fetchPhoneToZipDistance(form.phone, storeAddress, phoneCountry)
          .map(_.map(s => FM_INPUT_PHONE_TO_STORE_ZIP_DISTANCE_IN_MILES -> s)),

        fetchPhoneToZipDistance(form.phone, customerAddress, phoneCountry)
          .map(_.map(s => FM_INPUT_ZIP_TO_PHONE_DISTANCE_IN_MILES -> s)),

        fetchZipToZipDistance(customerAddress, billingAddress)
          .map(_.map(s => HOME_TO_BILLING_ADDRESS_DISTANCE -> s)),

        fetchZipToZipDistance(customerAddress, shippingAddress)
          .map(_.map(s => HOME_TO_SHIPPING_ADDRESS_DISTANCE -> s)),

        fetchZipToZipDistance(billingAddress, shippingAddress)
          .map(_.map(s => BILLING_TO_SHIPPING_ADDRESS_DISTANCE -> s)),

        fetchZipToZipDistance(billingAddress, storeAddress)
          .map(_.map(s => BILLING_TO_STORE_ADDRESS_DISTANCE -> s)),

        fetchZipToZipDistance(shippingAddress, storeAddress)
          .map(_.map(s => SHIPPING_TO_STORE_ADDRESS_DISTANCE -> s))
      ))

      val results: Seq[Option[(String, Double)]] = Await.result(combinedFuture, 2.seconds)

      val numericalRuleCodes: Seq[NumericalRuleCode] = results.flatten.map {
        case (ruleCode, value) => NumericalRuleCode(ruleCode, value)
      }

      RuleCodeResponse(
        numericalRuleCodes = ruleCodeResponse.numericalRuleCodes ++ numericalRuleCodes,
        categoricalRuleCodes = ruleCodeResponse.categoricalRuleCodes,
        errors = ruleCodeResponse.errors
      )
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }
  }

  private def computeStoreAddressRuleCodes(requestJsonAst: JsonAST.JValue, ruleCodeResponse: RuleCodeResponse): RuleCodeResponse = {
    try {
      val latitude = Json4sUtility.getOptionalDouble(requestJsonAst, "piiStdResponse.pii.addressV2.smarty_streets.latitude")
      val longitude = Json4sUtility.getOptionalDouble(requestJsonAst, "piiStdResponse.pii.addressV2.smarty_streets.longitude")

      val latitudeRule = if (latitude.isDefined) Some(NumericalRuleCode(FM_SMARTY_STORE_LATITUDE, latitude.get)) else None
      val longitudeRule = if (longitude.isDefined) Some(NumericalRuleCode(FM_SMARTY_STORE_LONGITUDE, longitude.get)) else None
      RuleCodeResponse(
        ruleCodeResponse.numericalRuleCodes ++ Seq(latitudeRule, longitudeRule).flatten,
        ruleCodeResponse.categoricalRuleCodes,
        ruleCodeResponse.errors
      )
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }
  }

  private def fetchZipToZipDistance(
                                     customerAddress: Option[PostalAddress],
                                     storeAddress: Option[PostalAddress]
                                   )(implicit trxId: TrxId, ec: ExecutionContext): Future[Option[Double]] = {

    def extractZipAndCountry(address: Option[PostalAddress]): Option[(String, String)] =
      for {
        postal <- address
        zip <- postal.zip.map(_.base)
        country <- postal.country.map(_.code2)
      } yield (zip, country)

    val customerZipCountry = extractZipAndCountry(customerAddress)
    val storeZipCountry = extractZipAndCountry(storeAddress)

    (customerZipCountry, storeZipCountry) match {
      case (Some((zip1, country1)), Some((zip2, country2))) if zip1 == zip2 =>
        Future.successful(Some(0.0))
      case (Some((zip1, country1)), Some((zip2, country2))) =>
        zipCodeResolver.calculateDistance(zip1, zip2, Units.Miles, Some(country1), Some(country2))
          .map(_.map(_.distance))
      case _ =>
        Future.successful(None)
    }
  }

  private def fetchPhoneToZipDistance(phone: Option[Phone],
                                      address: Option[PostalAddress],
                                      phoneCountry: Option[String]
                                     )(implicit trxId: TrxId, ec: ExecutionContext): Future[Option[Double]] = {
    val zipCode = address.flatMap(_.zip.map(_.base))
    val zipCountryCode = address.flatMap(_.country.map(_.code2))
    val areaCode = phone.flatMap { phone =>
      phoneCountry match {
        case Some("GB") => PhonePreprocessorUtil.getPhoneAreaCode(Some(phone.value), phoneCountry)
        case _ => phone.areaCode
      }
    }

    val identifiers = Seq(areaCode, zipCode).flatten
    identifiers match {
      case area :: zip :: Nil =>
        zipAreacodeResolverService.calculateDistance(zip, area, Units.Miles, zipCountryCode, phoneCountry)
          .map {
            case Some(distanceResult) =>
              Some(distanceResult.distance)
            case _ =>
              None
          }
      case _ => Future.successful(None)
    }
  }

  private def boolToDouble(b: Boolean): Double = {
    if (b) {
      1.0
    } else {
      0.0
    }
  }

  private def suspiciousAreaCode(phone: Phone): Option[Boolean] = {
    phone.areaCode.map(areaCode => {
      val firstNum = areaCode.head
      firstNum == '0' || firstNum == '1'
    })
  }

  private def suspiciousCentralOffice(phone: Phone): Boolean = {
    val firstCentralNum = phone.value10.takeRight(7).head
    firstCentralNum == '0' || firstCentralNum == '1'
  }

  private def maxConsecutivePhoneDigits(phone: Phone): Int = {
    calculateMaxSequence(phone.value.substring(1))
  }

  private def calculateMaxSequence(word: String): Int = {

    @tailrec
    def calcMaxSequenceRemaining(current: Char, remaining: List[Char], count: Int, max: Int, forward: Boolean): Int = {
      remaining match {
        case head :: tail => {
          if (head.isDigit && current.isDigit && Math.abs(current.toInt - head.toInt) == 1) {
            val headForward = current.toInt > head.toInt
            if (headForward != forward && count > 1) {
              val newCount = 1
              calcMaxSequenceRemaining(head, tail, newCount + 1, Math.max(max, 1), headForward)
            }
            else {
              calcMaxSequenceRemaining(head, tail, count + 1, Math.max(max, count + 1), headForward)
            }
          }
          else {
            calcMaxSequenceRemaining(head, tail, 1, max, forward)
          }
        }
        case _ => max
      }
    }

    if (word.nonEmpty) {
      val charList = word.toList
      val result = calcMaxSequenceRemaining(charList.head, charList.tail, 1, 1, true)
      if (result == 1) 0 else result
    }
    else 0
  }

  private def uniquePhoneLetterCount(phone: Phone): Int = {
    phone.value.groupBy(c => c.toLower).size
  }

  def getRegion(phone: Option[Phone]): Option[Double] = {
    val phoneUtil = PhoneNumberUtil.getInstance
    phone match {
      case Some(p) if (p.value != null && !p.value.equals("")) =>
        val mobNum = p.value.filterNot(c => c == '-' || c == '(' || c == ')').replaceAll("\\s", "")
        val number = if (!mobNum.startsWith("+")) "+" + mobNum else mobNum
        val numberProto = phoneUtil.parse(number, null)
        val region = phoneUtil.getRegionCodeForNumber(numberProto)
        if (region != null && (region.equals("US") || region.equals("CA"))) {
          Some(1.0)
        } else if (region == null) {
          None
        } else {
          Some(0.0)
        }
      case _ => None
    }
  }

  def usCleanedPhone(countryStr: Option[String], phone: Phone): Phone = {
    countryStr.map {
      code =>
        if (code.equals(Countries.UNITED_STATES.code2) && phone.value.length == 10) {
          new Phone("1" + phone.value)
        } else {
          phone
        }
    }.getOrElse(phone)
  }

  def isPhoneValid(phone: Option[Phone]): Option[Double] = {
    val phoneUtil = PhoneNumberUtil.getInstance
    phone match {
      case Some(p) if (p.value != null && !p.value.equals("")) =>
        val mobNum = p.value.filterNot(c => c == '-' || c == '(' || c == ')').replaceAll("\\s", "")
        val number = if (!mobNum.startsWith("+")) "+" + mobNum else mobNum
        val numberProto = phoneUtil.parse(number, null)
        if (phoneUtil.isValidNumber(numberProto)) {
          Some(0.0)
        } else {
          Some(1.0)
        }
      case _ => None
    }
  }

  private def isUsHoliday(dateTime: DateTime): Boolean = {
    UsHolidays.contains(dateTime.withZone(NYTimeZone).toLocalDate)
  }

  private def isUsWeekDay(dateTime: DateTime): Boolean = {
    val dayOfWeek = dateTime.withZone(NYTimeZone).getDayOfWeek
    dayOfWeek != DateTimeConstants.SUNDAY && dayOfWeek != DateTimeConstants.SATURDAY
  }

  private def storeZipMatches(customerAddress: Option[PostalAddress], storeAddress: Option[PostalAddress]): Option[Boolean] = {
    customerAddress.flatMap(addr => {
      addr.zip.flatMap(customerState => {
        storeAddress.flatMap(storeAddr => {
          storeAddr.zip.map(_.base == customerState.base)
        })
      })
    })
  }

  def isFirstNameNonPerson(name: Option[Name]): Option[Map[String, Double]] = {
    name.flatMap(_.firstName).map(_.trim.toLowerCase).map {
      case firstName if NonPersonEntityUtil.isMatchingEntityFound(firstName) => Map(FIRSTNAME_IS_LIKELY_NON_PERSON -> 1.0)
      case _ => Map.empty
    }
  }

  def isSurNameNonPerson(name: Option[Name]): Option[Map[String, Double]] = {
    name.flatMap(_.surName).map(_.trim.toLowerCase).map {
      case surName if NonPersonEntityUtil.isMatchingEntityFound(surName) => Map(SURNAME_IS_LIKELY_NON_PERSON -> Score(1.0))
      case _ => Map.empty
    }
  }

  private def isPossibleDualApplicant(name: Option[Name]): Option[Map[String, Double]] = {
    val firstName = name.flatMap(_.firstName).map(_.trim.toLowerCase)
    val surName = name.flatMap(_.surName).map(_.trim.toLowerCase)

    val isDualApplicant = (firstName, surName) match {
      case (Some(fName), _) if DualApplicantPattern.pattern().r.findFirstIn(fName).nonEmpty => true
      case (_, Some(lName)) if DualApplicantPattern.pattern().r.findFirstIn(lName).nonEmpty => true
      case _ => false
    }
    if (isDualApplicant) Some(Map(POSSIBLE_DUAL_APPLICANT -> Score(1.0)))
    else None
  }

  private def inputEmailRuleScores(email: String): Map[String, Double] = {
    EmailValidationHelper.disectEmail(email) match {
      case (userName, domain, tld) =>
        val userNameRelatedRuleCodes = userName.map { un =>
          Map(
            FM_EMAIL_MAX_LENGTH_OF_REPEATING_CHARACTER_IN_HANDLE -> (RunLength.getHighestRunLength(un).toDouble)
          )
        }.getOrElse(Map.empty)
        val domainRelatedRuleCodes = domain.map { d =>
          Map(
            FM_EMAIL_POPULAR_DOMAIN -> boolToDouble(EmailValidationHelper.isPopularDomain(d)),
            FM_EMAIL_TOP_DOMAIN -> boolToDouble(EmailValidationHelper.isTopLevelDomain(d)),
            FM_EMAIL_FREE_DOMAIN -> boolToDouble(EmailValidationHelper.isFreeDomain(d)),
            FM_EMAIL_RISKY_DOMAIN -> boolToDouble(EmailValidationHelper.isRiskyDomain(d))
          )
        }.getOrElse(Map.empty)
        val tldRelatedRuleCodes = tld.map { t =>
          Map(
            FM_EMAIL_TLD_NOT_IN_IANA -> boolToDouble(EmailValidationHelper.tldNotInIdna(t)),
            FM_EMAIL_TLD_IS_COUNTRY_CODE -> boolToDouble(EmailValidationHelper.tldIsCountryCode(t)),
            FM_EMAIL_TLD_IS_US -> boolToDouble(EmailValidationHelper.tldIsUS(t)),
            FM_EMAIL_RISKY_TOP_LEVEL_DOMAIN -> boolToDouble(EmailValidationHelper.riskyEmailTLD(t))
          )
        }.getOrElse(Map.empty)
        userNameRelatedRuleCodes ++ domainRelatedRuleCodes ++ tldRelatedRuleCodes
      case _ => Map.empty
    }
  }

  private def inputEmailDistance(email: String): Map[String, Double] = {
    EmailValidationHelper.disectEmail(email) match {
      case (userName, domain, tld) =>
        val tldRelatedRuleCodes = tld.map { t =>
          val distance = if (EmailValidationHelper.isFreeTLD(t)) 0.0 else EmailValidationHelper.levenstheinDistBetweenFreeDomainTLDs(t)
          Map(
            FM_EMAIL_TLD_VS_FREE_TLD_LEVENSHTEIN_SCORE -> distance
          )
        }.getOrElse(Map.empty)
        tldRelatedRuleCodes
      case _ => Map.empty
    }
  }

  def dobAgeOutliersRule(dob: Option[DateOfBirth]): Option[Map[String, Double]] = {
    dob.map {
      case d: BirthDate =>
        val ageOpt = d.age
        if (ageOpt.exists(age => age < 16 || age > 100)) Map(FM_DOB_AGE_LESS_16_OR_MORE_100 -> 1.0)
        else Map(FM_DOB_AGE_LESS_16_OR_MORE_100 -> 0.0)
      case d =>
        logger.warn(s"unknown age class for ${d.getClass}")
        Map(FM_DOB_AGE_LESS_16_OR_MORE_100 -> 0.0)
    }
  }

  def dobAgeOutliersAnotherRule(dob: Option[DateOfBirth]): Option[Map[String, Double]] = {
    val dobRuleCode = FM_DOB_AGE_LESS_13_OR_MORE_120
    dob.map {
      case d: BirthDate =>
        val ageOpt = d.age
        if (ageOpt.exists(age => age < 13 || age > 120)) Map(dobRuleCode -> 1.0)
        else Map(dobRuleCode -> 0.0)
      case d =>
        logger.warn(s"unknown age class for ${d.getClass}")
        Map(dobRuleCode -> 0.0)
    }
  }

  def ageInYears(dob: Option[DateOfBirth]): Option[Map[String, Double]] = {
    val currentDate: ReadablePartial = LocalDate.now
    val dobRuleCode = FM_AGE_YEARS
    dob.map {
      case d: BirthDate =>
        val bool = d.value != null && (d.value.isBefore(LocalDate.now) || d.value.isEqual(LocalDate.now()))
        if (bool) {
          Map(dobRuleCode -> getYearDiff(currentDate, d.value).toDouble)
        }
        else {
          Map.empty
        }
      case d =>
        logger.warn(s"unknown age class for ${d.getClass}")
        Map.empty
    }
  }

  def getYearDiff(adjustedDate: ReadablePartial, dt: LocalDate): Int = {
    Years.yearsBetween(dt, adjustedDate).getYears
  }

  def ageInYearsTimeAdj(dob: Option[DateOfBirth], adjustedDateTime: ReadablePartial): Option[Map[String, Double]] = {
    val dobRuleCode = FM_AGE_YEARS_ADJ
    dob.map {
      case d: BirthDate => {
        RulecodeLogicRegistry.getRulecodeValue("FMVAL.300668", List(adjustedDateTime, d.value)) match {
          case Some(value) =>
            value match {
              case Left(score) =>
                Map(
                  dobRuleCode -> Score(score),
                  FM_AGE_13_15 -> boolToDouble(score >= 13.0 && score <= 15.0),
                  FM_AGE_16_17 -> boolToDouble(score >= 16.0 && score <= 17.0),
                  FM_AGE_18_20 -> boolToDouble(score >= 18.0 && score <= 20.0),
                  FM_AGE_21_OVER -> boolToDouble(score >= 21))
              case Right(_) => Map.empty
            }
          case None => Map.empty
        }
      }
      case d =>
        logger.warn(s"unknown age class for ${d.getClass}")
        Map.empty
    }
  }


  def isDobInfuture(dob: Option[DateOfBirth], adjustedDateTime: ReadablePartial): Option[Map[String, Double]] = {
    val dobRuleCode = FM_DOB_IS_FUTURE_DATE
    dob.map {
      case d: BirthDate => {
        val bool = d.value != null && d.value.isAfter(adjustedDateTime)
        if (bool) {
          Map(dobRuleCode -> 1.0)
        }
        else if (d.value != null && (d.value.isBefore(adjustedDateTime) || d.value.isEqual(LocalDate.now))) {
          Map(dobRuleCode -> 0.0)
        }
        else Map.empty
      }
      case d =>
        logger.warn(s"unknown age class for ${d.getClass}")
        Map.empty
    }
  }

  def invalidYearOfBirthRule(dob: Option[DateOfBirth]): Option[Map[String, Double]] = {
    val invalidYearRuleCode = FM_DOB_NOT_VALID_YEAR
    dob.map {
      case d: BirthDate =>
        val year = if (d.value != null) d.value.getYear else 0
        if (year < 1900 || year > 2099) Map(invalidYearRuleCode -> Score(1.0))
        else Map(invalidYearRuleCode -> Score(0.0))
      case d =>
        logger.warn(s"unknown age class for ${d.getClass}")
        Map(invalidYearRuleCode -> Score(0.0))
    }
  }

  private def consumerPhoneRule(country: Option[Country], phone: Option[Phone]): Option[Map[String, Double]] = {
    val score: Option[Double] = country.fold[Option[Double]](consumerPhoneRuleNA(phone)) {
      case Countries.UNITED_STATES ⇒ consumerPhoneRuleNA(phone)
      case Countries.CANADA ⇒ consumerPhoneRuleNA(phone)
      case _ ⇒ Some(0.0)
    }
    score.map { s => Map(FM_PHONE_NOT_ALLOWED_FOR_CONSUMERS -> s) }
  }

  private def consumerPhoneRuleNA(phone: Option[Phone]): Option[Double] = {
    val bad = 1.0
    val good = 0.0
    phone.map { p =>
      p.areaCode match {
        case Some(x) if (x.startsWith("1")) ⇒ bad
        case Some(x) if (x.matches("""^[2-9](\d)\1$""")) => bad // 888, 877 numbers all ERC numbers
        case _ =>
          val ph = p.value10.replace("\\D*", "")
          ph match {
            case x if (x.matches("""^(\d)\1*$""")) ⇒ bad
            case x if (x.matches("""^\d{3}555\d{4}$""")) ⇒ bad
            case _ ⇒ good
          }
      }
    }
  }

  private def coaleasceSubmissionWithCurrent(form: Form): ReadablePartial = {
    val adjustedDate: ReadablePartial = form.submissionDate match {
      case Some(x) => submissionDateTime(form).get.toLocalDate
      case None => LocalDate.now()
    }
    adjustedDate
  }

  def parseToNumericalRuleCodes(maps: Map[String, Double]): Seq[NumericalRuleCode] = {
    maps.map { case (name, value) =>
      NumericalRuleCode(name, value)
    }.toSeq
  }

  def parseToCategoricalRuleCodes(maps: Map[String, String]): Seq[CategoricalRuleCode] = {
    maps.map { case (name, value) =>
      CategoricalRuleCode(name, value)
    }.toSeq
  }
}