package me.socure.rulecode.operators.derived

import me.socure.model.ErrorResponse
import me.socure.rulecode.common.models.{CategoricalRuleCode, NumericalRuleCode, RuleCodeResponse}
import me.socure.rulecode.common.processors.ParseBrazilNamesProcessor
import me.socure.rulecode.common.utilities.{Json4sUtility, MatcherUtility}
import me.socure.rulecode.filedata.RulecodeDataFileLookup
import org.joda.time.{DateTime, Days}
import org.json4s.{DefaultFormats, JsonAST}
import org.slf4j.LoggerFactory

import java.time.LocalDate
import scala.collection.Seq

object MnivlDerivedRuleCodesResolver extends DerivedRuleCodesResolver {

  private val logger = LoggerFactory.getLogger(getClass)

  implicit val localDateOrdering: Ordering[LocalDate] = Ordering.fromLessThan(_ isBefore _)

  implicit val formats = DefaultFormats

  type DerivedRuleCodeGenerator = (RuleCodeResponse, JsonAST.JValue, Map[String, JsonAST.JValue]) => RuleCodeResponse

  override def vendor: String = "MNIVL"

  val MONNAI_PHONE_ACTIVE_INDICATOR = s"$vendor.100005"
  val MONNAI_PHONE_PORT_COUNT_LAST_30_DAYS = s"$vendor.100024"
  val MONNAI_PHONE_PORT_COUNT_LAST_365_DAYS = s"$vendor.100025"
  val MONNAI_PHONE_HIGH_PORT_VELOCITY = s"$vendor.100026"
  val MONNAI_PHONE_ACTIVE_DATE = s"$vendor.100027"
  val MONNAI_PHONE_ACTIVE_DAYS = s"$vendor.100028"
  val MONNAI_PHONE_ACTIVE_DAYS_BIN = s"$vendor.100034"

  val MNIVL_200016 = s"$vendor.200016"
  val MNIVL_200017 = s"$vendor.200017"
  val MNIVL_200018 = s"$vendor.200018"
  val MNIVL_200019 = s"$vendor.200019"
  val MNIVL_200020 = s"$vendor.200020"
  val MNIVL_200021 = s"$vendor.200021"
  val MNIVL_200022 = s"$vendor.200022"
  val MNIVL_200023 = s"$vendor.200023"
  val MNIVL_200024 = s"$vendor.200024"
  val MNIVL_200025 = s"$vendor.200025"
  val MNIVL_200026 = s"$vendor.200026"

  val MNIVL_900001 = s"$vendor.900001"
  val MNIVL_900023 = s"$vendor.900023"
  val MNIVL_900024 = s"$vendor.900024"
  val MNIVL_900025 = s"$vendor.900025"

  val MNIVL_100022 = s"$vendor.100022"
  val MNIVL_100007 = s"$vendor.100007"

  override def generate(
                         ruleCodeResponse: RuleCodeResponse,
                         requestJsonAST: JsonAST.JValue,
                         dbJsonMap: Map[String, JsonAST.JValue],
                         errorResponseMap: Map[String, ErrorResponse],
                         ruleCodeDataFileLookup: RulecodeDataFileLookup): RuleCodeResponse = {
    try {
      generateRuleCodes(
        Seq(computeMnivl100024_100025_100026, computeMnivl100027_100028_100034, computeMnivl200024, computeMnivl200025, computeMnivl200026, computeMnivl_900023_900024_900025),
        ruleCodeResponse,
        requestJsonAST,
        dbJsonMap
      )
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occured while generating derived rulecodes for vendor $vendor : ", exception)
        ruleCodeResponse
    }

  }

  private def generateRuleCodes(
                                 generators: Seq[DerivedRuleCodeGenerator],
                                 ruleCodeResponse: RuleCodeResponse,
                                 requestJsonAST: JsonAST.JValue,
                                 dbJsonMap: Map[String, JsonAST.JValue]
                               ): RuleCodeResponse = {
    generators.foldLeft(ruleCodeResponse)((r, f) => f(r, requestJsonAST, dbJsonMap))
  }

  private val computeMnivl100024_100025_100026: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val submissionDateOpt = Json4sUtility.getOptionalLong(requestJsonAST, "raw.submissionDate")
    val transactionDateOpt = Json4sUtility.getOptionalLong(requestJsonAST, "raw.transactionDate")
    val timeAdjustedDateOpt = if(submissionDateOpt.isDefined) submissionDateOpt else if(transactionDateOpt.isDefined) transactionDateOpt else Option.empty[Long]

    val mnivl100022 = getCategoricalResult(MNIVL_100022, ruleCodeResponse)

    val (last30Days, last365Days)= mnivl100022 match {
      case Some(dates) if dates.nonEmpty =>
        val datesList = dates.split(",")
        (
          Some(datesList.count { portedDate =>
            val diffDays =  Days.daysBetween(new DateTime(portedDate).toLocalDate, new DateTime(timeAdjustedDateOpt.get).toLocalDate).getDays
            diffDays >= 0 && diffDays <=30
          }),
          Some(datesList.count { portedDate =>
            val diffDays =  Days.daysBetween(new DateTime(portedDate).toLocalDate, new DateTime(timeAdjustedDateOpt.get).toLocalDate).getDays
            diffDays >= 0 && diffDays <=365
          })
        )
      case None => (None,None)
    }
    (last30Days, last365Days) match {
      case (Some(v1),Some(v2)) =>
        val portVelocity = if(v1 > 0 || v2 > 1) 1.0 else 0.0
        RuleCodeResponse (
          ruleCodeResponse.numericalRuleCodes ++ Seq(
            NumericalRuleCode(MONNAI_PHONE_PORT_COUNT_LAST_30_DAYS, v1),
            NumericalRuleCode(MONNAI_PHONE_PORT_COUNT_LAST_365_DAYS, v2),
            NumericalRuleCode(MONNAI_PHONE_HIGH_PORT_VELOCITY, portVelocity)
          ),
          ruleCodeResponse.categoricalRuleCodes,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private val computeMnivl100027_100028_100034: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, dbJsonMap: Map[String, JsonAST.JValue]) => {
    val submissionDateOpt = Json4sUtility.getOptionalLong(requestJsonAST, "raw.submissionDate")
    val transactionDateOpt = Json4sUtility.getOptionalLong(requestJsonAST, "raw.transactionDate")
    val timeAdjustedDateOpt = if(submissionDateOpt.isDefined) submissionDateOpt else if(transactionDateOpt.isDefined) transactionDateOpt else Option.empty[Long]

    val mnivl100022 = getCategoricalResult(MNIVL_100022, ruleCodeResponse)
    val mnivl100007 = getCategoricalResult(MNIVL_100007, ruleCodeResponse)

    val mnivl100005 = getResult(MONNAI_PHONE_ACTIVE_INDICATOR, ruleCodeResponse)
    if(mnivl100005.isDefined && mnivl100005.get == 1.0) {
      val firstActivated = (mnivl100022,mnivl100007) match {
        case (Some(portedDates), Some(activationDate)) => Some((portedDates.split(",") ++ List(activationDate)).minBy(dateStr => LocalDate.parse(dateStr)))
        case (Some(portedDates), None) =>  Some(portedDates.split(",").minBy(dateStr => LocalDate.parse(dateStr)))
        case (None, Some(activationDate)) => Some(activationDate)
        case (None,None) => None
      }

      val activatedSinceOpt = if(firstActivated.isDefined)  Some(Days.daysBetween(new DateTime(firstActivated.get).toLocalDate, new DateTime(timeAdjustedDateOpt.get).toLocalDate).getDays) else None

      val phoneActiveDaysBinOpt = activatedSinceOpt match {
        case Some(activatedSince) =>
          if(activatedSince >= 365) Some(7.0)
          else if(activatedSince >= 270) Some(6.0)
          else if(activatedSince >= 180) Some(5.0)
          else if(activatedSince >= 90) Some(4.0)
          else if(activatedSince >= 60) Some(3.0)
          else if(activatedSince >= 30) Some(2.0)
          else if(activatedSince >= 0) Some(1.0)
          else Some(0.0)
        case None => None
      }

      (firstActivated, activatedSinceOpt, phoneActiveDaysBinOpt) match {
        case (Some(v1), Some(v2), Some(v3)) =>
          RuleCodeResponse (
            ruleCodeResponse.numericalRuleCodes ++ Seq(
              NumericalRuleCode(MONNAI_PHONE_ACTIVE_DAYS, v2),
              NumericalRuleCode(MONNAI_PHONE_ACTIVE_DAYS_BIN, v3)
            ),
            ruleCodeResponse.categoricalRuleCodes ++ Seq(
              CategoricalRuleCode(MONNAI_PHONE_ACTIVE_DATE, v1)
            ),
            ruleCodeResponse.errors
          )
        case _ => ruleCodeResponse
      }
    } else ruleCodeResponse

  }

  private val computeMnivl200024: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, _: JsonAST.JValue, _: Map[String, JsonAST.JValue]) => {
    val rule200016 = getResult(MNIVL_200016, ruleCodeResponse)
    val rule200017 = getResult(MNIVL_200017, ruleCodeResponse)
    val rule200018 = getResult(MNIVL_200018, ruleCodeResponse)
    val rule200019 = getResult(MNIVL_200019, ruleCodeResponse)
    val rule200020 = getResult(MNIVL_200020, ruleCodeResponse)
    val rule200021 = getResult(MNIVL_200021, ruleCodeResponse)
    val rule200022 = getResult(MNIVL_200022, ruleCodeResponse)
    val rule200023 = getResult(MNIVL_200023, ruleCodeResponse)

    val result = if (rule200016.contains(1.0) || rule200017.exists(_ > 0.70)) {
      Some(0.98)
    } else if ((rule200020.contains(1.0) || rule200023.contains(1.0)) && (rule200018.contains(1.0) || rule200022.contains(1.0))) {
      Some(0.95)
    } else if ((rule200019.exists(_ > 0.9) || rule200022.contains(1.0)) && (rule200021.exists(_ > 0.9) || rule200023.contains(1.0))) {
      Some(0.90)
    } else if ((rule200019.exists(_ > 0.7) || rule200022.contains(1.0)) && (rule200021.exists(_ > 0.7) || rule200023.contains(1.0))) {
      Some(0.86)
    } else if (rule200020.contains(1.0) || rule200023.contains(1.0)) {
      Some(0.84)
    } else if (rule200021.exists(_ > 0.7)) {
      Some(0.79)
    } else if (rule200018.contains(1.0) || rule200022.contains(1.0)) {
      Some(0.72)
    } else if (rule200019.exists(_ > 0.7)) {
      Some(0.68)
    } else if (rule200021.exists(_ <= 0.7) && rule200019.exists(_ <= 0.7)) {
      Some(0.03)
    } else if (rule200021.exists(_ <= 0.7) || rule200020.contains(0.0)) {
      Some(0.07)
    } else if (rule200019.exists(_ <= 0.7) || rule200018.contains(0.0)) {
      Some(0.11)
    } else {
      None
    }

    RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ result.fold(Seq.empty[NumericalRuleCode])(e => Seq(NumericalRuleCode(MNIVL_200024, e))),
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )
  }

  private val computeMnivl200025: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, _: JsonAST.JValue, _: Map[String, JsonAST.JValue]) => {
    val rule200018 = getResult(MNIVL_200018, ruleCodeResponse)
    val rule200019 = getResult(MNIVL_200019, ruleCodeResponse)
    val rule200022 = getResult(MNIVL_200022, ruleCodeResponse)

    val result = if (rule200018.contains(1.0) || rule200022.contains(1.0)) {
      Some(2.0)
    } else if ((rule200019.exists(_ > 0.7) && rule200018.contains(0.0)) && rule200022.contains(0.0)) {
      Some(1.0)
    } else if ((rule200019.isEmpty || rule200019.exists(_ <= 0.7)) && rule200018.contains(0.0) && rule200022.contains(0.0)) {
      Some(-1.0)
    } else if (rule200018.isEmpty) {
      Some(0.0)
    } else {
      None
    }

    RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ result.fold(Seq.empty[NumericalRuleCode])(e => Seq(NumericalRuleCode(MNIVL_200025, e))),
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )
  }

  private val computeMnivl200026: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, _: JsonAST.JValue, _: Map[String, JsonAST.JValue]) => {
    val rule200020 = getResult(MNIVL_200020, ruleCodeResponse)
    val rule200021 = getResult(MNIVL_200021, ruleCodeResponse)
    val rule200023 = getResult(MNIVL_200023, ruleCodeResponse)

    val result = if (rule200020.contains(1.0) || rule200023.contains(1.0)) {
      Some(2.0)
    } else if ((rule200021.exists(_ > 0.7) && rule200020.contains(0.0)) && rule200023.contains(0.0)) {
      Some(1.0)
    } else if ((rule200021.isEmpty || rule200021.exists(_ <= 0.7)) && rule200020.contains(0.0) && rule200023.contains(0.0)) {
      Some(-1.0)
    } else if (rule200020.isEmpty) {
      Some(0.0)
    } else {
      None
    }

    RuleCodeResponse(
      ruleCodeResponse.numericalRuleCodes ++ result.fold(Seq.empty[NumericalRuleCode])(e => Seq(NumericalRuleCode(MNIVL_200026, e))),
      ruleCodeResponse.categoricalRuleCodes,
      ruleCodeResponse.errors
    )
  }

  private val computeMnivl_900023_900024_900025: DerivedRuleCodeGenerator = (ruleCodeResponse: RuleCodeResponse, requestJsonAST: JsonAST.JValue, _: Map[String, JsonAST.JValue]) => {
    val rule900001Opt = ruleCodeResponse.categoricalRuleCodes.find(r => MNIVL_900001.equals(r.name)).map(_.value)

    rule900001Opt match {
      case Some(rule900001) =>
        val lookupNames = rule900001.split(",")
        val firstNames = lookupNames.flatMap(ParseBrazilNamesProcessor.parseBrazilianName(_, "firstName").extractOpt[String])
        val lastNames = lookupNames.flatMap(ParseBrazilNamesProcessor.parseBrazilianName(_, "lastName").extractOpt[String])
        val middleNames = lookupNames.flatMap(ParseBrazilNamesProcessor.parseBrazilianName(_, "middleName").extractOpt[String])
        RuleCodeResponse(
          ruleCodeResponse.numericalRuleCodes,
          ruleCodeResponse.categoricalRuleCodes ++ Seq(
            Option(firstNames.filter(_.trim.nonEmpty).mkString("::")).filter(_.nonEmpty).map(CategoricalRuleCode(MNIVL_900023, _)),
            Option(middleNames.filter(_.trim.nonEmpty).mkString("::")).filter(_.nonEmpty).map(CategoricalRuleCode(MNIVL_900024, _)),
            Option(lastNames.filter(_.trim.nonEmpty).mkString("::")).filter(_.nonEmpty).map(CategoricalRuleCode(MNIVL_900025, _))
          ).flatten,
          ruleCodeResponse.errors
        )
      case _ => ruleCodeResponse
    }
  }

  private def getResult(ruleName: String, ruleCodeResponse: RuleCodeResponse): Option[Double] = {
    ruleCodeResponse.numericalRuleCodes.find(r => ruleName.equals(r.name)).map(_.value)
  }

  private def getCategoricalResult(ruleName: String, ruleCodeResponse: RuleCodeResponse): Option[String] = {
    ruleCodeResponse.categoricalRuleCodes.find(r => ruleName.equals(r.name)).map(_.value)
  }
}
