package me.socure.rulecode.operators.impl

import me.socure.rulecode.common.models.StreetAddress
import me.socure.rulecode.common.utilities.MatcherUtility
import me.socure.rulecode.operators.OperatorV2
import me.socure.rulecode.operators.options.{GenericKYCMatchOperatorOptions, MatchType}
import org.json4s.JsonAST.{JArray, JBool, JNothing, JValue}
import org.json4s.{DefaultFormats, Formats}

/**
 * <b>GenericKYCMatchOperator</b> performs KYC Match for the given inputs based on the options provided
 *
 * @param inputs  - Seq of JValues has to be provided as the input. value at 0th index will be idplusInput and 1st index will be dbJson
 * @param options - type supported name or street
 * @example
 *  <p>
 *  <pre>
 *  GenericKYCMatchOperator.process(parse("""["john"]"""), JArray(List(JString("john"),JString("jack"))), GenericKYCMatchOperatorOptions(type = "name")) // returns true
 *  GenericKYCMatchOperator.process(parse("""["street"]"""), JArray(List(JString("street1"),J<PERSON><PERSON>("abc"))), GenericKYCMatchOperatorOptions(type = "street")) // returns true
 *  </pre>
 */
object GenericKYCMatchOperator extends OperatorV2[JValue, GenericKYCMatchOperatorOptions] {

  private implicit val formats: Formats = DefaultFormats

  override def process(inputs: Seq[JValue], options: GenericKYCMatchOperatorOptions): Option[JValue] = {
    if (inputs.nonEmpty) {
      val dbJson = inputs.last match {
        case jArr: JArray => jArr
        case jVal: JValue => JArray(List(jVal))
        case _ => JNothing
      }
      (inputs.headOption, dbJson) match {
        case (Some(idplusInput: JValue), JArray(list)) if list.nonEmpty && list.exists(_ != JNothing) && idplusInput != JNothing =>
          val result = list.map {
            dbJsonVal =>
              (idplusInput.extractOpt[String], dbJsonVal.extractOpt[String]) match {
                case (Some(s1), Some(s2)) =>
                  options.matchType match {
                    case MatchType.NameMatch => nameMatch(s1, s2)
                    case MatchType.StreetMatch => streetMatch(s1, s2)
                  }
                case _ => JNothing
              }
          }
          JBool(result.contains(true)).toOption
        case _ if options.returnNoMatchOnEmptyInputs => JBool(false).toOption
        case _ => None
      }
    } else None
  }

  private def nameMatch(s1: String, s2: String): Boolean = {
    MatcherUtility.matchN(s1, s2)
  }

  private def streetMatch(s1: String, s2: String): Boolean = {
    MatcherUtility.matchStreetAddressOptimized(MatcherUtility.cleanStreetAddress(Some(StreetAddress(s1))), Some(StreetAddress(s2)))
  }

}