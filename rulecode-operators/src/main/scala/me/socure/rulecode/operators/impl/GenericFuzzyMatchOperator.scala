package me.socure.rulecode.operators.impl

import me.socure.common.operators.utilities.DOBMatcherUtility
import me.socure.rulecode.common.models.JsonFormats
import me.socure.rulecode.common.utilities.{DateUtility, MatcherUtility}
import me.socure.rulecode.operators.OperatorV2
import me.socure.rulecode.operators.options.FuzzyAlgorithm._
import me.socure.rulecode.operators.options.GenericFuzzyMatchOperatorOptions
import me.socure.service.constants.DobMatchLogic
import org.json4s.JsonAST.{JArray, JBool, JString, JValue}
import org.json4s.{Formats, JDouble}
import org.slf4j.{Logger, LoggerFactory}

import scala.util.{Failure, Success, Try}

/**
 * This is a generic fuzzy match operator. It supports two sets of inputs
 *  - two strings
 *  - a single input string (expected as input from ID+) and an array of strings (expected from DB lookup)
 *
 * For supported options, refer to GenericFuzzyMatchOperatorOptions
 */
object GenericFuzzyMatchOperator extends OperatorV2[JValue, GenericFuzzyMatchOperatorOptions] {
  private implicit val formats: Formats = JsonFormats.RuleCodeWorkerFormats
  private val logger: Logger = LoggerFactory.getLogger(getClass)

  override def process(inputs: Seq[JValue], options: GenericFuzzyMatchOperatorOptions): Option[JValue] = {
    inputs match {
      case Seq(inputLookupValue, dbLookupValue) =>
        (inputLookupValue, dbLookupValue) match {
          case (JString(inputValue), JString(dbValue)) if inputValue.trim.nonEmpty =>
            Some(JDouble(fuzzyMatchScore(inputValue, dbValue, options)))
          case (JString(inputValue), JArray(dbValues)) if inputValue.trim.nonEmpty && (dbValues.nonEmpty && dbValues.forall(_.isInstanceOf[JString]))  =>
            Some(JArray(dbValues.map(dbValue => JDouble(fuzzyMatchScore(inputValue, dbValue.extract[String], options)))))
          case (JArray(dbValues), JString(inputValue)) if inputValue.trim.nonEmpty && (dbValues.nonEmpty && dbValues.forall(_.isInstanceOf[JString])) =>
            Some(JArray(dbValues.map(dbValue => JDouble(fuzzyMatchScore(dbValue.extract[String], inputValue, options)))))
          case _ if options.returnNoMatchOnEmptyInputs => JDouble(0.0).toOption
          case _ => None
        }
      case Seq(inputLookupValue, dbLookupValue, dobMatchLogicValue) =>
        (inputLookupValue, dbLookupValue, dobMatchLogicValue) match {
          case (JString(inputValue), JString(dbValue), JString(dobMatchLogicName)) if inputValue.trim.nonEmpty && options.algorithm == DobFuzzy =>
            Some(JDouble(calculateDobFuzzyMatchScore(inputValue, dbValue, dobMatchLogicName, options)))
          case _ => None
        }
      case _ => None
    }
  }

  private def fuzzyMatchScore(inputString: String, dbString: String, options: GenericFuzzyMatchOperatorOptions) = {
    val score = options.algorithm match {
      case Ratcliff =>
        MatcherUtility.fuzzy_match(inputString, dbString)
      case CharOff =>
        MatcherUtility.getUnMatchedCharCount(inputString, dbString, options.ignoreCase)
      case DirectTransposition =>
        MatcherUtility.getTranspositionCount(inputString, dbString, options.ignoreCase)
      case Levenshtein =>
        MatcherUtility.levenshteinMatch(inputString, dbString, options.ignoreCase)
      case _ =>
        0.0
    }
    MatcherUtility.roundUpScore(score, options.decimalPoints)
  }

  private def calculateDobFuzzyMatchScore(inputDateStr: String, dbDateStr: String, dobMatchLogicName: String, options: GenericFuzzyMatchOperatorOptions): Double = {

    Try(DobMatchLogic.withName(dobMatchLogicName)) match {
      case Success(dobMatchLogic) =>

        val inputDateOpt = DateUtility.getDateTime(inputDateStr, options.datePattern)
        val dbDateOpt = DateUtility.getDateTime(dbDateStr, options.datePattern)

        val result = (inputDateOpt, dbDateOpt) match {
          case (Some(inputDate), Some(dbDate)) =>
            DOBMatcherUtility.isDOBMisKeyed(inputDate, dbDate, dobMatchLogic)
          case _ =>
            DOBMatcherUtility.isDOBMisKeyed(inputDateStr, dbDateStr, dobMatchLogic)
        }
        if (result) 1.0 else 0.0

      case Failure(_) =>
        logger.error(s"Invalid dobMatchLogic: $dobMatchLogicName")
        0.0
    }
  }
}
