package me.socure.rulecode.operators.options

import me.socure.rulecode.operators.OperatorV2Options
import me.socure.rulecode.operators.options.MatchType.MatchType

case class GenericKYCMatchOperatorOptions(matchType: MatchType, returnNoMatchOnEmptyInputs: Boolean = false) extends OperatorV2Options

object GenericKYCMatchOperatorOptions {
  def apply(inputMap: Map[String,String]): GenericKYCMatchOperatorOptions = {
    val matchType = inputMap.get("type").map(MatchType.withName).getOrElse(throw new IllegalArgumentException("match type is not defined for KY<PERSON>atchOperator"))
    val returnNoMatchOnEmptyInputs = inputMap.get("returnNoMatchOnEmptyInputs").exists(_.toBoolean)
    GenericKYCMatchOperatorOptions(matchType, returnNoMatchOnEmptyInputs)
  }
}

object MatchType extends Enumeration {
  type MatchType = Value
  val NameMatch: MatchType.Value = Value("name")
  val StreetMatch: MatchType.Value = Value("street")
}
