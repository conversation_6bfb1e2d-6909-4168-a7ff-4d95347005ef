package me.socure.rulecode.operators.options

import me.socure.rulecode.operators.OperatorV2Options
import me.socure.rulecode.operators.options.FilterType.FilterType
import me.socure.rulecode.operators.options.OrderType.OrderType

case class GenericFilterOptions(
                                 filterType: FilterType,
                                 attrToBeFiltered: String,
                                 filterCriteria: Set[String],
                                 sortByAttr: Option[String],
                                 orderType: OrderType,
                                 isCaseInsensitive: Boolean = false,
                                 trimInputs: <PERSON><PERSON><PERSON> = false,
                                 returnNAOnEmptyInputs: <PERSON><PERSON><PERSON> = false,
                                 ignoreEmptyFilterCriteria: Boolean = false,
                                 attrToBeFilteredSafeExtract: Boolean = false
                               ) extends OperatorV2Options

object GenericFilterOptions {
  def apply(inputMap: Map[String,String]): GenericFilterOptions = {
    val filterType = inputMap.get("filterType").map(FilterType.withName).getOrElse(throw new IllegalArgumentException("filterType attribute not found for FilterOperator"))
    val attrToBeFiltered = inputMap.getOrElse("attrToBeFiltered", "")
    val filterCriteria = inputMap.getOrElse("filterCriteria", "").split(",").toSet
    val sortByAttr = inputMap.get("sortByAttr")
    val orderType = inputMap.get("orderType").map(OrderType.withName).getOrElse(OrderType.asc)
    val isCaseInsensitive = inputMap.getOrElse("isCaseInsensitive", "false").toBoolean
    val trimInputs = inputMap.getOrElse("trimInputs", "false").toBoolean
    val returnNAOnEmptyInputs = inputMap.getOrElse("returnNAOnEmptyInputs", "false").toBoolean
    val ignoreEmptyFilterCriteria = inputMap.getOrElse("ignoreEmptyFilterCriteria", "false").toBoolean
    val attrToBeFilteredSafeExtract = inputMap.getOrElse("attrToBeFilteredSafeExtract", "false").toBoolean
    GenericFilterOptions(filterType, attrToBeFiltered, filterCriteria, sortByAttr, orderType, isCaseInsensitive, trimInputs,
      returnNAOnEmptyInputs, ignoreEmptyFilterCriteria, attrToBeFilteredSafeExtract)
  }
}

object FilterType extends Enumeration {
  type FilterType = Value
  val In: FilterType.Value = Value("In")
  val NotIn: FilterType.Value = Value("NotIn")
  val InSubStr: FilterType.Value = Value("InSubStr")
  val NotInSubStr: FilterType.Value = Value("NotInSubStr")
}

object OrderType extends Enumeration {
  type OrderType = Value
  val asc: OrderType.Value = Value("asc")
  val desc: OrderType.Value = Value("desc")
}