package me.socure.rulecode.operators.derived

import me.socure.types.scala.{ByMember, Enum}

object DerivedRuleCodesResolvers extends Enum with ByMember {

  type DerivedRcResolver = EnumVal

  sealed class EnumVal(val name: String,  val resolver: DerivedRuleCodesResolver) extends Value

  def apply(resolver: DerivedRuleCodesResolver): DerivedRcResolver = {
    new DerivedRcResolver(resolver.vendor, resolver)
  }

  val ifevlDerivedRuleCodesResolver: DerivedRcResolver = apply(IfevlDerivedRuleCodesResolver)
  val ifivlDerivedRuleCodesResolver: DerivedRcResolver = apply(IfivlDerivedRuleCodesResolver)
  val ifpvlDerivedRuleCodesResolver: DerivedRcResolver = apply(IfpvlDerivedRuleCodesResolver)
  val ifavlDerivedRuleCodesResolver: DerivedRcResolver = apply(IfavlDerivedRuleCodesResolver)
  val ssevlDerivedRuleCodesResolver: DerivedRcResolver = apply(SsevlDerivedRuleCodesResolver)
  val pfivlDerivedRuleCodesResolver: DerivedRcResolver = apply(PfivlDerivedRuleCodesResolver)
  val pfvvlDerivedRuleCodesResolver: DerivedRcResolver = apply(PfvvlDerivedRuleCodesResolver)
  val fcevlDerivedRuleCodesResolver: DerivedRcResolver = apply(FcevlDerivedRuleCodesResolver)
  val tdevlDerivedRuleCodesResolver: DerivedRcResolver = apply(TdevlDerivedRuleCodesResolver)
  val nsrvlDerivedRuleCodesResolver: DerivedRcResolver = apply(NsrvlDerivedRuleCodesResolver)
  val exevlDerivedRuleCodesResolver: DerivedRcResolver = apply(ExevlDerivedRuleCodesResolver)
  val exavlDerivedRuleCodesResolver: DerivedRcResolver = apply(ExavlDerivedRuleCodesResolver)
  val expvlDerivedRuleCodesResolver: DerivedRcResolver = apply(ExpvlDerivedRuleCodesResolver)
  val nsgvlDerivedRuleCodesResolver: DerivedRcResolver = apply(NsgvlDerivedRuleCodesResolver)
  val asavlDerivedRuleCodesResolver: DerivedRcResolver = apply(AsavlDerivedRuleCodesResolver)
  val fmvalDerivedRuleCodesResolver: DerivedRcResolver = apply(FormValDerivedRuleCodesResolver)
  val enpvlDerivedRuleCodesResolver: DerivedRcResolver = apply(EnpvlDerivedRuleCodesResolver)
  val cgvalDerivedRuleCodesResolver: DerivedRcResolver = apply(CgvalDerivedRuleCodesResolver)
  val scpvlDerivedRuleCodesResolver: DerivedRcResolver = apply(SCPVLDerivedRuleCodesResolver)
  val ensvlDerivedRuleCodesResolver: DerivedRcResolver = apply(EnsvlDerivedRuleCodesResolver)
  val otwvlDerivedRuleCodesResolver: DerivedRcResolver = apply(OtwvlDerivedRuleCodesResolver)
  val arvalDerivedRuleCodesResolver: DerivedRcResolver = apply(ArvalDerivedRuleCodesResolver)
  val favalDerivedRulecodesResolver: DerivedRcResolver = apply(FavalDerivedRulecodesResolver)
  val attmvlDerivedRuleCodesResolver: DerivedRcResolver = apply(AttmvlDerivedRuleCodesResolver)
  val bmevlDerivedRuleCodesResolver: DerivedRcResolver = apply(BmevlDerivedRuleCodesResolver)
  val twdvlDerivedRuleCodesResolver: DerivedRcResolver = apply(TwdvlDerivedRuleCodesResolver)
  val imivlDerivedRuleCodesResolver: DerivedRcResolver = apply(ImivlDerivedRuleCodesResolver)
  val mnivlDerivedRuleCodesResolver: DerivedRcResolver = apply(MnivlDerivedRuleCodesResolver)



  val lookupByName = byMember(_.name)

  private lazy val allResolvers : Set[DerivedRuleCodesResolver] = DerivedRuleCodesResolvers.values.map(_.resolver).toSet

  def getAllResolvers: Set[DerivedRuleCodesResolver] = allResolvers

}