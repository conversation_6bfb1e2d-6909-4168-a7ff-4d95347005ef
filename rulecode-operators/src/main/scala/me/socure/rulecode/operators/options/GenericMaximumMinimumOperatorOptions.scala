package me.socure.rulecode.operators.options

import me.socure.rulecode.operators.OperatorV2Options
import me.socure.rulecode.operators.options.GenericMaxMinOperatorConditions.GenericMaxMinOperatorCondition
import me.socure.rulecode.operators.options.ValueType.ValueType

case class GenericMaximumMinimumOperatorOptions(attr: String, valueType: ValueType, condition: GenericMaxMinOperatorCondition) extends OperatorV2Options

object GenericMaximumMinimumOperatorOptions {
  def apply(inputMap: Map[String, String]): GenericMaximumMinimumOperatorOptions = {
    val attr = inputMap.getOrElse("attr", "")
    val valueType = inputMap.get("valueType").map(ValueType.withName).getOrElse(throw new IllegalArgumentException("ValueType attribute not found for MinMaxOperator"))
    val condition = inputMap.get("condition").map(GenericMaxMinOperatorConditions.withName).getOrElse(throw new IllegalArgumentException("condition attribute not found for MinMaxOperator"))
    GenericMaximumMinimumOperatorOptions(attr, valueType, condition)
  }
}

object ValueType extends Enumeration {
  type ValueType = Value
  val Int: ValueType.Value = Value("int")
  val Long: ValueType.Value = Value("long")
  val Float: ValueType.Value = Value("float")
  val Double: ValueType.Value = Value("double")
  val Boolean: ValueType.Value = Value("bool")
  val Date: ValueType.Value = Value("date")
}

object GenericMaxMinOperatorConditions extends Enumeration {
  type GenericMaxMinOperatorCondition = Value
  val maximum: GenericMaxMinOperatorCondition = Value("maximum")
  val minimum: GenericMaxMinOperatorCondition = Value("minimum")
}
