package me.socure.rulecode.operators.options

import me.socure.rulecode.operators.OperatorV2Options
import me.socure.rulecode.operators.options.FuzzyAlgorithm.FuzzyAlgorithm

/**
 * Supported options for the GenericFuzzyMatchOperator
 * @param algorithm - The name of the algorithm to use. Support algorithms - ratcliff, charOff, directTransposition
 *                   - ratcliff = Returns match score using RatcliffObershelpMetric
 *                   - charOff = Returns number of unmatched characters between two strings
 *                   - directTransposition = Returns number of direct transpositions between two strings
 * @param decimalPoints - The precision for the decimal points for the fuzzy score
 * @param ignoreCase - If this value is true, then cases will be ignored for charOff and directTransposition algorithms
 */
case class GenericFuzzyMatchOperatorOptions(algorithm: FuzzyAlgorithm, decimalPoints: Int, ignoreCase: Boolean = false, datePattern: Option[String] = None, returnNoMatchOnEmptyInputs: Boolean = false) extends OperatorV2Options

object GenericFuzzyMatchOperatorOptions {
  def apply(optionsMap: Map[String, String]): GenericFuzzyMatchOperatorOptions = {
    GenericFuzzyMatchOperatorOptions(
      optionsMap.get("algorithm").map(FuzzyAlgorithm.withName).getOrElse(FuzzyAlgorithm.Ratcliff),
      optionsMap.getOrElse("decimalPoints", "2").toInt,
      optionsMap.getOrElse("ignoreCase", "false").toBoolean,
      optionsMap.get("datePattern"),
      optionsMap.getOrElse("returnNoMatchOnEmptyInputs", "false").toBoolean
    )
  }
}

object FuzzyAlgorithm extends Enumeration {
  type FuzzyAlgorithm = Value
  val Ratcliff: FuzzyAlgorithm.Value = Value("ratcliff")
  val CharOff: FuzzyAlgorithm.Value = Value("charOff")
  val DirectTransposition: FuzzyAlgorithm.Value = Value("directTransposition")
  val DobFuzzy: FuzzyAlgorithm.Value = Value("dobFuzzy")
  val Levenshtein: FuzzyAlgorithm.Value = Value("levenshtein")
}