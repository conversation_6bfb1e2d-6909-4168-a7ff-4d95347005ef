package me.socure.rulecode.operators.impl

import me.socure.rulecode.common.utilities.DateUtility
import me.socure.rulecode.operators.OperatorV2
import me.socure.rulecode.operators.options.GenericMaxMinOperatorConditions.GenericMaxMinOperatorCondition
import me.socure.rulecode.operators.options.{GenericMaxMinOperatorConditions, GenericMaximumMinimumOperatorOptions, ValueType}
import org.json4s.JsonAST.{JA<PERSON>y, JBool, JD<PERSON>ble, JInt, JNothing, JString, JValue}
import org.json4s.{DefaultFormats, Formats}

import scala.util.Try

/**
 * <b>GenericMaximumMinimumOperator</b> returns the maximum or minimum value from the array based on the condition provided
 *
 * In the future, we would also want to extend this to return the index of max or min value based on an option
 *
 * @param inputs - a sequence of JValue for which the maximum or minimum has to be identified
 * @param config - contains a set of parameters like datatype and max/min value to be returned
 */
object GenericMaximumMinimumOperator extends OperatorV2[JValue, GenericMaximumMinimumOperatorOptions] {

  private implicit val formats: Formats = DefaultFormats

  private def getMaximumOrMinimum(a : JValue, b: JValue, diff: Double, condition: GenericMaxMinOperatorCondition): JValue = {
    condition match {
      case GenericMaxMinOperatorConditions.minimum => if(diff <= 0.0) a else b
      case GenericMaxMinOperatorConditions.maximum => if(diff >= 0.0) a else b
      case _ => a
    }
  }

   override def process(inputs: Seq[JValue], options: GenericMaximumMinimumOperatorOptions): Option[JValue] = {
     inputs match {
       case Seq(values) =>
         values.toOption match {
           case Some(JArray(arr)) =>
             val attr = options.attr
             val condition: GenericMaxMinOperatorCondition = options.condition
             val minOrMaxValue = arr.reduceLeft {
               (a, b) =>
                 val (mappedA, mappedB) = if (attr.isEmpty) (a, b) else (a \ attr, b \ attr)
                 (mappedA, mappedB) match {
                   case (JNothing, JNothing) => JNothing
                   case (_, JNothing) => a
                   case (JNothing, _) => b
                   case (val1,val2) =>
                     val v1 = val1 match {
                       case JBool(b) => b.toString
                       case _ => val1.extract[String]
                     }
                     val v2 = val2 match {
                       case JBool(b) => b.toString
                       case _ => val2.extract[String]
                     }
                     options.valueType match {
                       case ValueType.Int => getMaximumOrMinimum(a, b, v1.toInt - v2.toInt, condition = condition)
                       case ValueType.Long =>  getMaximumOrMinimum(a, b, v1.toLong - v2.toLong, condition = condition)
                       case ValueType.Float => getMaximumOrMinimum(a, b, v1.toFloat - v2.toFloat, condition = condition)
                       case ValueType.Double => getMaximumOrMinimum(a, b, v1.toDouble - v2.toDouble, condition = condition)
                       case ValueType.Boolean => getMaximumOrMinimum(a, b, (if (v1 == "true") 1 else 0) - (if (v2 == "true") 1 else 0), condition = condition)
                       case ValueType.Date =>
                         val date1 = DateUtility.getDateTime(v1)
                         val date2 = DateUtility.getDateTime(v2)
                         if(date1.isDefined && date2.isDefined) {
                           getMaximumOrMinimum(a, b, date1.get.getMillis - date2.get.getMillis, condition = condition)
                         } else JNothing
                     }
                 }
             }
             Some(minOrMaxValue)
           case Some(jDouble: JDouble) if options.valueType == ValueType.Double => Some(jDouble)
           case Some(jInt: JInt) if options.valueType == ValueType.Int => Some(jInt)
           case Some(JString(s)) if s.nonEmpty =>
             options.valueType match {
               case ValueType.Int => if (Try(s.toInt).isSuccess) Some(JInt(s.toInt)) else None
               case ValueType.Double => if (Try(s.toDouble).isSuccess) Some(JDouble(s.toDouble)) else None
               case ValueType.Date => if (DateUtility.getDateTime(s).isDefined) Some(JString(s)) else None
               case _ => None
             }
           case _ => None
         }
       case _ => None
     }
   }
}
