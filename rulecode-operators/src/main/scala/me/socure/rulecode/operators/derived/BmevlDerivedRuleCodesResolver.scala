package me.socure.rulecode.operators.derived

import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import me.socure.rulecode.common.models._
import me.socure.rulecode.common.utilities.Json4sUtility
import me.socure.rulecode.filedata.RulecodeDataFileLookup
import org.json4s.JsonAST
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext

object BmevlDerivedRuleCodesResolver extends DerivedRuleCodesResolver {

  private val logger = LoggerFactory.getLogger(getClass)

  type DerivedRuleCodeGenerator = (RuleCodeResponse, JsonAST.JValue, Map[String, JsonAST.JValue]) => RuleCodeResponse

  override def vendor: String = "BMEVL"

  //FMVAL
  private val FM_SSN_ISSUE_STATE_MATCH = "FMVAL.308674"
  private val FM_SSN_ISSUEYEAR_YOB_GAP = "FMVAL.308680"
  private val FM_SSN_ISSUEYEAR_YOB_ANOMALY_LT_1990 = "FMVAL.308681"
  private val FM_SSN_ISSUEYEAR_YOB_ANOMALY_GTE_1990 = "FMVAL.308682"
  val FM_EMAIL_CONTAIN_FIRST_NAME = "FMVAL.308002"
  val FM_EMAIL_CONTAIN_SURNAME = "FMVAL.308003"
  val FM_EMAIL_CONTAINS_FIRSTNAME_NICKNAME = "FMVAL.308054"
  val FM_EMAIL_CONTAINS_SURNAME_NICKNAME = "FMVAL.308055"
  val FM_EMAIL_FIRSTNAME_FUZZY_MATCH = "FMVAL.308056"
  val FM_EMAIL_SURNAME_FUZZY_MATCH = "FMVAL.308057"
  val EMAIL_HAS_A_FIRSTNAME_MATCH = "FMVAL.308061"
  val EMAIL_HAS_A_SURNAME_MATCH ="FMVAL.308062"

  //NSRVL
  val NSR_IDENTITY_VERIFIED_COMPONENTS = "NSRVL.100002"
  val NSR_IDENTITY_RISK_ADDRESS_TO_NAME = "NSRVL.100008"
  val NSR_IDENTITY_RISK_PHONE_TO_NAME = "NSRVL.100004"
  val NSR_IDENTITY_RISK_PHONE_TO_FIRSTNAME = "NSRVL.100005"
  val NSR_IDENTITY_RISK_EMAIL_TO_NAME = "NSRVL.100009"
  val NSR_ADDRESS_NAME_MATCH_LEVEL = "NSRVL.108012"
  val NSR_PHONE_NAME_MATCH_LEVEL = "NSRVL.108013"
  val NSR_EMAIL_NAME_MATCH_LEVEL = "NSRVL.108014"

  implicit val formats = org.json4s.DefaultFormats

  override def generate(
                         ruleCodeResponse: RuleCodeResponse,
                         requestJsonAST: JsonAST.JValue,
                         dbJsonMap: Map[String, JsonAST.JValue],
                         errorResponseMap: Map[String, ErrorResponse],
                         ruleCodeDataFileLookup: RulecodeDataFileLookup
                       ): RuleCodeResponse = {

    implicit val trxId = TrxId.apply(Json4sUtility.getOptionalString(requestJsonAST, "transactionId").getOrElse(""))

    val result100012 = try {
      NsrvlDerivedRuleCodesResolver.computeNsrvl100012(ruleCodeResponse, NSR_IDENTITY_VERIFIED_COMPONENTS, NSR_IDENTITY_RISK_ADDRESS_TO_NAME, NSR_ADDRESS_NAME_MATCH_LEVEL)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $NSR_ADDRESS_NAME_MATCH_LEVEL : ", exception)
        ruleCodeResponse
    }
    val result100013 = try {
      NsrvlDerivedRuleCodesResolver.computeNsrvl100013(result100012, NSR_IDENTITY_RISK_PHONE_TO_NAME, NSR_IDENTITY_RISK_PHONE_TO_FIRSTNAME, NSR_PHONE_NAME_MATCH_LEVEL)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $NSR_PHONE_NAME_MATCH_LEVEL : ", exception)
        result100012
    }
    val result100014 = try {
      NsrvlDerivedRuleCodesResolver.computeNsrvl100014(result100013, NSR_IDENTITY_RISK_EMAIL_TO_NAME, NSR_EMAIL_NAME_MATCH_LEVEL)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $NSR_EMAIL_NAME_MATCH_LEVEL : ", exception)
        result100013
    }

    val result308002 = try {
      FormValDerivedRuleCodesResolver.computeFmval300002(result100014, requestJsonAST,FM_EMAIL_CONTAIN_FIRST_NAME)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_CONTAIN_FIRST_NAME : ", exception)
        result100014
    }
    val result308003 = try {
      FormValDerivedRuleCodesResolver.computeFmval300003(result308002, requestJsonAST,FM_EMAIL_CONTAIN_SURNAME)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_CONTAIN_SURNAME : ", exception)
        result308002
    }
    val result308054 = try {
      FormValDerivedRuleCodesResolver.computeFmval300054(result308003, dbJsonMap, requestJsonAST,FM_EMAIL_CONTAIN_FIRST_NAME,FM_EMAIL_CONTAINS_FIRSTNAME_NICKNAME)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_CONTAINS_FIRSTNAME_NICKNAME : ", exception)
        result308003
    }
    val result308055 = try {
      FormValDerivedRuleCodesResolver.computeFmval300055(result308054, dbJsonMap, requestJsonAST,FM_EMAIL_CONTAIN_SURNAME,FM_EMAIL_CONTAINS_SURNAME_NICKNAME)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_CONTAINS_SURNAME_NICKNAME : ", exception)
        result308054
    }
    val result308056 = try {
      FormValDerivedRuleCodesResolver.computeFmval300056(result308055, requestJsonAST,FM_EMAIL_CONTAIN_FIRST_NAME,FM_EMAIL_FIRSTNAME_FUZZY_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_FIRSTNAME_FUZZY_MATCH : ", exception)
        result308055
    }
    val result308057 = try {
      FormValDerivedRuleCodesResolver.computeFmval300057(result308056, requestJsonAST,FM_EMAIL_CONTAIN_SURNAME,FM_EMAIL_SURNAME_FUZZY_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_EMAIL_SURNAME_FUZZY_MATCH : ", exception)
        result308056
    }
    val result308061 = try {
      FormValDerivedRuleCodesResolver.computeFmval300061(result308057,FM_EMAIL_CONTAIN_FIRST_NAME,FM_EMAIL_CONTAINS_FIRSTNAME_NICKNAME,FM_EMAIL_FIRSTNAME_FUZZY_MATCH,EMAIL_HAS_A_FIRSTNAME_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $EMAIL_HAS_A_FIRSTNAME_MATCH : ", exception)
        result308057
    }
    val result308062 = try {
      FormValDerivedRuleCodesResolver.computeFmval300062(result308061,FM_EMAIL_CONTAIN_SURNAME,FM_EMAIL_CONTAINS_SURNAME_NICKNAME,FM_EMAIL_SURNAME_FUZZY_MATCH,EMAIL_HAS_A_SURNAME_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $EMAIL_HAS_A_SURNAME_MATCH : ", exception)
        result308061
    }

    val result300674 = try {
      FormValDerivedRuleCodesResolver.computeFmval300674(result308062, requestJsonAST, ruleCodeDataFileLookup,FM_SSN_ISSUE_STATE_MATCH)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_ISSUE_STATE_MATCH : ", exception)
        result308062
    }
    val result300680 = try {
      FormValDerivedRuleCodesResolver.computeFmval300680(result300674, requestJsonAST, ruleCodeDataFileLookup,FM_SSN_ISSUEYEAR_YOB_GAP)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_ISSUEYEAR_YOB_GAP : ", exception)
        result300674
    }

    val result300681 = try {
      FormValDerivedRuleCodesResolver.computeFmval300681(result300680, requestJsonAST, ruleCodeDataFileLookup,FM_SSN_ISSUEYEAR_YOB_ANOMALY_LT_1990)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_ISSUEYEAR_YOB_ANOMALY_LT_1990 : ", exception)
        result300680
    }

    try {
      FormValDerivedRuleCodesResolver.computeFmval300682(result300681, requestJsonAST, ruleCodeDataFileLookup,FM_SSN_ISSUEYEAR_YOB_ANOMALY_GTE_1990)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception occurred while generating derived rulecodes for vendor $FM_SSN_ISSUEYEAR_YOB_ANOMALY_GTE_1990 : ", exception)
        result300681
    }
  }

}
